from pii_consortium_attributes import calculate_pii_consortium_attrs
from consumer_profile_attributes import calculate_consumer_profile_attrs
from pyspark.sql import SparkSession
import argparse


def parse_arguments():
    parser = argparse.ArgumentParser(description="Process application attributes and window sizes.")

    # Argument for multiple windows
    parser.add_argument(
        '--window',
        type=int,
        nargs='+',
        required=True,
        help="List of window sizes in days (e.g., --window 30 60 90)."
    )

    parser.add_argument(
        '--app_report_delay',
        type=int,
        default=1,
        help="Delay in hours for application report (default is 1 hour)."
    )

    # Argument for application attributes dataframe path
    parser.add_argument(
        '--app_attr_path',
        type=str,
        required=True,
        help="Path to the application attributes dataframe file."
    )

    parser.add_argument(
        '--output_path',
        type=str,
        required=True,
        help="Path to the application attributes dataframe file."
    )

    return parser.parse_args()


def main():
    args = parse_arguments()
    spark = SparkSession.builder.appName("generate_telco_consortium_attrs").getOrCreate()
    app_attr_df = spark.read.parquet(args.app_attr_path)

    # Calculate PII consortium attributes
    pii_consortium_attrs = calculate_pii_consortium_attrs(
        app_attr_df, "opin", args.window, args.app_report_delay
    )

    # Calculate consumer profile attributes
    consumer_profile_attrs = calculate_consumer_profile_attrs(
        app_attr_df, "opin", args.window, args.app_report_delay
    )

    # checking cnt
    app_cnt, pii_consortium_attrs_count, consumer_profile_attrs_count = (
        app_attr_df.count(),
        pii_consortium_attrs.count(),
        consumer_profile_attrs.count()
    )

    if app_cnt != pii_consortium_attrs_count or app_cnt != consumer_profile_attrs_count:
        raise ValueError("Counts of application attributes and calculated attributes do not match.")
    print(f"Application attributes count: {app_cnt}")

    res_df = pii_consortium_attrs.join(
        consumer_profile_attrs, on="APP_NUMBER"
    )
    for col in res_df.columns:
        if col != "APP_NUMBER":
            res_df = res_df.withColumnRenamed(col, f"fcra_{col}")
    res_df.write.mode("overwrite").parquet(args.output_path)
    spark.stop()


if __name__ == "__main__":
    main()