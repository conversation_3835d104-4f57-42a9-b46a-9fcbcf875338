from typing import List

from pyspark.sql import functions as F
from pyspark.sql import Window as W
from pyspark.sql import types as T
from pyspark.sql import DataFrame


from consumer_profile_attributes import get_app_hist_df, NUM_SECOND_IN_A_DAY, fillna_no_prev_hist

UID_NULL_DEFAULT_VALUE = -2
NEVER_USED_DEFAULT_VALUE = -3
NO_HIT_DEFAULT_VALUE = -9999
uid_type_list = ['addr', 'phone', 'email', 'ssn']


def _calc_consumer_pii_attr(
    df_app_hist, window_size_in_days, uid_type
):
    uid_col = f"{uid_type}_uid"
    window = W.partitionBy("curr.APP_NUMBER").orderBy("prev.app_datetime_unix").rowsBetween(
        W.unboundedPreceding, W.unboundedFollowing
    )
    df_app_hist = df_app_hist.withColumn(
        "last_uid", F.last(F.col(f"prev.{uid_col}"), ignorenulls=False).over(window)
    ).withColumn(
        "last_time_use_this_uid", F.last(
            F.when(
                (F.col(f"curr.{uid_col}").isNotNull()) &
                (F.col(f"prev.{uid_col}") == F.col(f"curr.{uid_col}")),
                F.col("prev.app_datetime_unix")
            ).otherwise(
                F.lit(None)
            ), ignorenulls=True
        ).over(window)
    )

    df_pii_consortium_attr = df_app_hist.groupBy("curr.APP_NUMBER").agg(
        F.count_distinct(F.col(f"prev.{uid_col}")).alias(f"num_{uid_type}_{window_size_in_days}d"),
        F.last("last_uid").alias(f"last_uid"),
        F.last(f"curr.{uid_col}").alias(uid_col),
        F.last("last_time_use_this_uid").alias(f"last_time_use_this_uid"),
        F.last("curr.app_datetime_unix").alias("app_datetime_unix"),
        F.sum(
            F.when(
                (F.col(f"curr.{uid_col}").isNotNull()) &
                (F.col(f"prev.{uid_col}") == F.col(f"curr.{uid_col}")),
                F.lit(1)
            ).otherwise(
                F.lit(0)
            )
        ).alias(f'num_app_using_{uid_type}_same_identity_{window_size_in_days}d')
    ).withColumn(
        f"num_{uid_type}_{window_size_in_days}d", F.when(
            # all prior apps' uid are null
            F.col(f"num_{uid_type}_{window_size_in_days}d") == 0, F.lit(UID_NULL_DEFAULT_VALUE)
        ).otherwise(
            F.col(f"num_{uid_type}_{window_size_in_days}d")
        )
    ).withColumn(
        f'same_{uid_type}_as_last_app_{window_size_in_days}d', F.when(
            (F.col(uid_col).isNull()) | (F.col("last_uid").isNull()),
            F.lit(UID_NULL_DEFAULT_VALUE)
        ).when(
            F.col(uid_col) == F.col("last_uid"),
            F.lit(1)
        ).otherwise(
            F.lit(0)
        )
    ).withColumn(
        f'num_app_using_{uid_type}_same_identity_{window_size_in_days}d',
        F.when(
            F.col(uid_col).isNull(), F.lit(UID_NULL_DEFAULT_VALUE)
        ).otherwise(
            F.col(f'num_app_using_{uid_type}_same_identity_{window_size_in_days}d')
        )
    ).withColumn(
        f'days_since_last_app_using_{uid_type}_same_identity_{window_size_in_days}d',
        F.when(
            F.col(uid_col).isNull(), F.lit(UID_NULL_DEFAULT_VALUE).cast(T.DoubleType())
        ).when(
            F.col('last_time_use_this_uid').isNull(), F.lit(NEVER_USED_DEFAULT_VALUE).cast(T.DoubleType())
        ).otherwise(
            F.round(
                (F.col("app_datetime_unix") - F.col("last_time_use_this_uid")) / NUM_SECOND_IN_A_DAY, 2
            )
        )
    ).select(
        "curr.APP_NUMBER",
        f"num_{uid_type}_{window_size_in_days}d",
        f"same_{uid_type}_as_last_app_{window_size_in_days}d",
        f'num_app_using_{uid_type}_same_identity_{window_size_in_days}d',
        f'days_since_last_app_using_{uid_type}_same_identity_{window_size_in_days}d',
    )
    return df_pii_consortium_attr


def calc_consumer_pii_attr(
    df_app_attr, window_size_in_days, same_client_delay_hour
):
    df_app_hist = get_app_hist_df(df_app_attr, window_size_in_days, same_client_delay_hour)
    df_consumer_pii_attr = None
    for uid_type in uid_type_list:
        df_pii_attr = _calc_consumer_pii_attr(
            df_app_hist, window_size_in_days, uid_type
        )
        if df_consumer_pii_attr is None:
            df_consumer_pii_attr = df_pii_attr
        else:
            df_consumer_pii_attr = df_consumer_pii_attr.join(
                df_pii_attr, on="APP_NUMBER", how="left"
            )
    return fillna_no_prev_hist(df_app_attr, df_consumer_pii_attr)


def calc_consumer_pii_attr_all_client(
    df_app_attr, window_size_in_days, same_client_delay_hour,
):
    client_list = sorted([row['client_name'] for row in df_app_attr.select('client_name').distinct().collect()])
    print("All client names:", client_list)
    df_pii_consortium_attr = None
    for client_name in client_list:
        df_app_hist = get_app_hist_df(
            df_app_attr.filter(F.col('client_name') == client_name),
            window_size_in_days, same_client_delay_hour
        )
        df_pii_consortium_attr_by_client = None
        for uid_type in uid_type_list:
            df_pii_attr = _calc_consumer_pii_attr(
                df_app_hist, window_size_in_days, uid_type
            )
            if df_pii_consortium_attr_by_client is None:
                df_pii_consortium_attr_by_client = df_pii_attr
            else:
                df_pii_consortium_attr_by_client = df_pii_consortium_attr_by_client.join(
                    df_pii_attr, on="APP_NUMBER", how="left"
                )
        if df_pii_consortium_attr is None:
            df_pii_consortium_attr = df_pii_consortium_attr_by_client
        else:
            df_pii_consortium_attr = df_pii_consortium_attr.unionByName(
                df_pii_consortium_attr_by_client
            )
    new_columns = [
        f"{col} AS {col}_with_client" if col != "APP_NUMBER" else col for col in df_pii_consortium_attr.columns
    ]
    return fillna_no_prev_hist(df_app_attr, df_pii_consortium_attr.selectExpr(new_columns), window_size_in_days)


def _calculate_pii_consortium_attrs(
    df_app_attr: DataFrame, consumer_key, window_size_in_days: int, same_client_delay_hour: int
) -> DataFrame:
    df_no_hit = df_app_attr.filter(F.col(consumer_key).isNull()).select("APP_NUMBER")
    print(f"Number of apps with no hit (window_size_in_days: {window_size_in_days}):", df_no_hit.count())
    df_hit_app_attr = df_app_attr.filter(F.col(consumer_key).isNotNull())
    df_pii_consortium_attr = calc_consumer_pii_attr(
        df_hit_app_attr, window_size_in_days, same_client_delay_hour
    )
    df_pii_consortium_attr_all_client = calc_consumer_pii_attr_all_client(
        df_hit_app_attr, window_size_in_days, same_client_delay_hour
    )
    df_joined = df_pii_consortium_attr.join(
        df_pii_consortium_attr_all_client, on="APP_NUMBER", how="outer"
    )
    for col_name, col_type in df_joined.dtypes:
        if col_name == "APP_NUMBER":
            continue
        df_no_hit = df_no_hit.withColumn(
            col_name, F.lit(NO_HIT_DEFAULT_VALUE).cast(col_type)
        )
    return df_joined.unionByName(df_no_hit)


def calculate_pii_consortium_attrs(
    df_app_attr: DataFrame, consumer_key: str, window_size_list: List[int], same_client_delay_hour: int
) -> DataFrame:
    df_attrs = None
    for window_size_in_days in window_size_list:
        df_pii_consortium_attrs = _calculate_pii_consortium_attrs(
            df_app_attr, consumer_key, window_size_in_days, same_client_delay_hour
        )
        if df_attrs is None:
            df_attrs = df_pii_consortium_attrs
        else:
            df_attrs = df_attrs.join(df_pii_consortium_attrs, on="APP_NUMBER", how="outer")
    return df_attrs


