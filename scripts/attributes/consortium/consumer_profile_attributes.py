from pyspark.sql import functions as F
from pyspark.sql import Window as W
from pyspark.sql import types as T
from pyspark.sql import DataFrame
from typing import List

MIN_WINDOW_LEN = 5
NUM_SECOND_IN_A_DAY = 86400
NO_HIT_DEFAULT_VALUE = -9999
NO_PRIOR_APP_DEFAULT_VALUE = -1


def time_gap_attr(ts_list, curr_time):
    """
    ts_list is a list of unix timestamps of all applications during the effective time window from a consumer
    """

    if len(ts_list) == 0:
        return -1.0, -1.0

    ts_list = sorted(ts_list)
    min_gap = curr_time - ts_list[-1]
    for i in range(len(ts_list) - 1):
        min_gap = min(min_gap, ts_list[i + 1] - ts_list[i])
    min_app_date_gap = round(min_gap / NUM_SECOND_IN_A_DAY, 2)
    avg_app_date_gap = round(
        (curr_time - ts_list[0]) / NUM_SECOND_IN_A_DAY / len(ts_list), 2
    )
    return min_app_date_gap, avg_app_date_gap

time_gap_attr_schema = T.StructType([
    T.StructField('min_time_gap', T.DoubleType(), False),
    T.StructField('avg_time_gap', T.DoubleType(), False)]
)

time_gap_attr_udf = F.udf(
    time_gap_attr,
    time_gap_attr_schema
)

def get_app_hist_df(df_app_attr, window_size_in_days, same_client_delay_hour):
    df_app_hist = df_app_attr.withColumn(
        "start_window", F.col("app_datetime_unix") - window_size_in_days * 24 * 3600
    ).withColumn(
        "sales_channel", F.when(
            F.col('sales_channel') >= -1, F.col('sales_channel')
        ).otherwise(
            F.lit(None)
        )
    ).withColumn(
        "id_type", F.when(
            F.col('id_type') >= 0, F.col('id_type')
        ).otherwise(
            F.lit(None)
        )
    )

    df_app_hist = df_app_hist.alias("curr").join(
        df_app_hist.alias("prev"),
        (F.col("curr.opin") == F.col("prev.opin")) &
        (F.col("prev.app_datetime_unix") < F.col("curr.app_datetime_unix")) &
        (F.col("prev.app_datetime_unix") >= F.col("curr.start_window")),
        "left_outer"
    ).filter(
        (F.col("prev.client_name") != F.col("curr.client_name")) |
        (F.col("curr.app_datetime_unix") - F.col("prev.app_datetime_unix") >= same_client_delay_hour * 3600)
    )

    return df_app_hist


def _calc_app_hist_attr(df_app_attr, window_size_in_days, same_client_delay_hour):

    # set invalid id_type and sales_channel to None
    df_app_hist = get_app_hist_df(
        df_app_attr,
        window_size_in_days,
        same_client_delay_hour
    )

    window = W.partitionBy("curr.APP_NUMBER").orderBy("prev.app_datetime_unix").rowsBetween(
        W.unboundedPreceding, W.unboundedFollowing
    )
    df_app_hist = df_app_hist.withColumn(
        "last_sales_channel", F.last("prev.sales_channel", ignorenulls=False).over(window)
    ).withColumn(
        "last_id_type", F.last("prev.id_type", ignorenulls=False).over(window)
    )

    df_app_hist_attr = df_app_hist.groupBy("curr.APP_NUMBER").agg(
        F.first("curr.app_datetime_unix").alias("app_datetime_unix"),
        F.count("prev.APP_NUMBER").alias(f'num_app_{window_size_in_days}d'),
        F.min("prev.app_datetime_unix").alias(f'min_app_time_{window_size_in_days}d'),
        F.max("prev.app_datetime_unix").alias(f'max_app_time_{window_size_in_days}d'),

        F.collect_list("prev.app_datetime_unix").alias('ts_list'),
        F.count_distinct("prev.sales_channel").alias(f'num_sales_channel_{window_size_in_days}d'),
        F.last(
            (F.col("curr.sales_channel") == F.col("last_sales_channel")).cast(T.IntegerType())
        ).alias(f'same_sales_channel_with_last_app_{window_size_in_days}d'),

        F.count_distinct("prev.id_type").alias(f'num_id_type_{window_size_in_days}d'),
        F.last(
            (F.col("curr.id_type") == F.col("last_id_type")).cast(T.IntegerType())
        ).alias(f'same_id_type_with_last_app_{window_size_in_days}d'),

        F.sum(
            F.col('prev.day_of_week').isin([0, 1, 2, 3, 4]).cast('int')
        ).alias('num_app_in_weekday'),
        F.sum(
            F.col('prev.time_of_day').isin([3, 4, 5]).cast('int')
        ).alias('num_app_during_day'),
        F.sum(
            (F.col('prev.during_office_hour') == 1).cast(T.IntegerType())
        ).alias('num_app_in_office_hour'),
        F.sum('prev.has_dob').alias("num_app_has_dob"),
        F.sum('prev.has_phone').alias("num_app_has_phone"),
        F.sum('prev.has_email').alias("num_app_has_email"),
        F.sum(
            (F.col('prev.has_ssn') > 0).cast('int')
        ).alias('num_app_has_ssn'),
    ).withColumn(
        f'days_since_last_app_{window_size_in_days}d', F.round(
            (F.col("app_datetime_unix") - F.col(f'max_app_time_{window_size_in_days}d')) / NUM_SECOND_IN_A_DAY, 2
        )
    ).withColumn(
        f'days_since_first_app_{window_size_in_days}d', F.round(
            (F.col("app_datetime_unix") - F.col(f'min_app_time_{window_size_in_days}d')) / NUM_SECOND_IN_A_DAY, 2
        )
    ).withColumn(
        "time_gap_attr", time_gap_attr_udf(
            F.col("ts_list"), F.col("app_datetime_unix")
        )
    ).withColumn(
        f'min_app_date_gap_{window_size_in_days}d', F.col('time_gap_attr').getField('min_time_gap')
    ).withColumn(
        f'avg_app_date_gap_{window_size_in_days}d', F.col('time_gap_attr').getField('avg_time_gap')
    ).withColumn(
        f'pct_app_in_weekday_{window_size_in_days}d', F.col("num_app_in_weekday") / F.col(f'num_app_{window_size_in_days}d')
    ).withColumn(
        f'pct_app_during_day_{window_size_in_days}d', F.col("num_app_during_day") / F.col(f'num_app_{window_size_in_days}d')
    ).withColumn(
        f'pct_app_during_office_hour_{window_size_in_days}d', F.col("num_app_in_office_hour") / F.col(f'num_app_{window_size_in_days}d')
    ).withColumn(
        f'pct_app_has_dob_{window_size_in_days}d', F.col("num_app_has_dob") / F.col(f'num_app_{window_size_in_days}d')
    ).withColumn(
        f'pct_app_has_phone_{window_size_in_days}d', F.col("num_app_has_phone") / F.col(f'num_app_{window_size_in_days}d')
    ).withColumn(
        f'pct_app_has_email_{window_size_in_days}d', F.col("num_app_has_email") / F.col(f'num_app_{window_size_in_days}d')
    ).withColumn(
        f'pct_app_has_ssn_{window_size_in_days}d', F.col("num_app_has_ssn") / F.col(f'num_app_{window_size_in_days}d')
    ).fillna(
        {f'same_id_type_with_last_app_{window_size_in_days}d': -2}
    ).select(
        "curr.APP_NUMBER",
        f'num_app_{window_size_in_days}d',
        f'days_since_last_app_{window_size_in_days}d',
        f'days_since_first_app_{window_size_in_days}d',
        f'min_app_date_gap_{window_size_in_days}d',
        f'avg_app_date_gap_{window_size_in_days}d',
        f'num_sales_channel_{window_size_in_days}d',
        f'same_sales_channel_with_last_app_{window_size_in_days}d',
        f'num_id_type_{window_size_in_days}d',
        f'same_id_type_with_last_app_{window_size_in_days}d',
        f'pct_app_in_weekday_{window_size_in_days}d',
        f'pct_app_during_day_{window_size_in_days}d',
        f'pct_app_during_office_hour_{window_size_in_days}d',
        f'pct_app_has_dob_{window_size_in_days}d',
        f'pct_app_has_phone_{window_size_in_days}d',
        f'pct_app_has_email_{window_size_in_days}d',
        f'pct_app_has_ssn_{window_size_in_days}d',
    )
    return df_app_hist_attr


def fillna_no_prev_hist(df_app_attr, df_app_hist_attr, window_size_in_days):
    df_no_prev_hist = df_app_attr.select("APP_NUMBER").join(
        df_app_hist_attr.select("APP_NUMBER"),
        on="APP_NUMBER",
        how="left_anti"
    )
    if window_size_in_days is None:
        raise(ValueError("window_size_in_days must be specified"))

    print(f"Applications without previous history (window_size_in_days: {window_size_in_days}): {df_no_prev_hist.count()}")
    for col_name, col_type in df_app_hist_attr.dtypes:
        if col_name == "APP_NUMBER":
            continue
        if col_name.startswith(f'num_app_{window_size_in_days}d'):
            default_value = 0
        else:
            default_value = NO_PRIOR_APP_DEFAULT_VALUE
        df_no_prev_hist = df_no_prev_hist.withColumn(
            col_name, F.lit(default_value).cast(col_type)
        )
    return df_app_hist_attr.unionByName(df_no_prev_hist)


def calc_app_hist_attr(df_app_attr, window_size_in_days, same_client_delay_hour):
    df_app_hist_attr = _calc_app_hist_attr(
        df_app_attr, window_size_in_days, same_client_delay_hour
    )
    return fillna_no_prev_hist(df_app_attr, df_app_hist_attr, window_size_in_days)


def _calc_app_hist_attrs_by_client(
    df_app_attr: DataFrame,
    window_size_in_days: int,
    same_client_delay_hour: int,
    client_name: str
) -> DataFrame:
    """
    Calculate application history attributes for a specific client.

    Parameters:
    - df_app_attr: DataFrame containing application attributes.
    - window_size_in_days: Size of the window in days to consider for historical applications.
    - same_client_delay_hour: Delay in hours to consider applications from the same client.
    - client_name: Name of the client to filter applications.

    Returns:
    - DataFrame with calculated application history attributes for the specified client.
    """

    df_app_hist_attr = _calc_app_hist_attr(
        df_app_attr.filter(F.col("client_name") == client_name),
        window_size_in_days,
        same_client_delay_hour
    )
    new_columns = [
        f"{col} AS {col}_with_client" if col != "APP_NUMBER" else col for col in df_app_hist_attr.columns
    ]
    return df_app_hist_attr.selectExpr(new_columns)


def calc_app_hist_attrs_all_clients(
    df_app_attr: DataFrame,
    window_size_in_days: int,
    same_client_delay_hour: int
) -> DataFrame:
    """
    Calculate application history attributes for all clients.

    Parameters:
    - df_app_attr: DataFrame containing application attributes.
    - window_size_in_days: Size of the window in days to consider for historical applications.
    - same_client_delay_hour: Delay in hours to consider applications from the same client.

    Returns:
    - DataFrame with calculated application history attributes for all clients.
    """
    client_list = sorted([row['client_name'] for row in df_app_attr.select('client_name').distinct().collect()])
    app_hist_attr_all_clients = None
    for client_name in client_list:
        if client_name is None:
            raise ValueError("Client name cannot be None")
        df_app_hist = _calc_app_hist_attrs_by_client(
            df_app_attr,
            window_size_in_days,
            same_client_delay_hour,
            client_name
        )
        if app_hist_attr_all_clients is None:
            app_hist_attr_all_clients = df_app_hist
        else:
            app_hist_attr_all_clients = app_hist_attr_all_clients.unionByName(df_app_hist)
    return fillna_no_prev_hist(df_app_attr, app_hist_attr_all_clients, window_size_in_days)


def _calc_client_hist_attr(
    df_app_attr: DataFrame,
    window_size_in_days: int,
    same_client_delay_hour: int
) -> DataFrame:
    df_app_hist = get_app_hist_df(
        df_app_attr,
        window_size_in_days,
        same_client_delay_hour
    )
    bounded_window = W.partitionBy("curr.APP_NUMBER").orderBy("prev.app_datetime_unix")
    unbounded_window = W.partitionBy("curr.APP_NUMBER").orderBy("prev.app_datetime_unix").rowsBetween(
        W.unboundedPreceding, W.unboundedFollowing
    )
    df_app_hist = df_app_hist.withColumn(
        "last_client_name", F.last(F.col("prev.client_name"), ignorenulls=False).over(unbounded_window)
    ).withColumn(
        "lead_client_name", F.lead(F.col("prev.client_name"), 1).over(bounded_window)
    ).withColumn(
        "lead_client_name", F.when(
            F.col("lead_client_name").isNull(), F.col("curr.client_name")
        ).otherwise(
            F.col("lead_client_name")
        )
    ).withColumn(
        "last_app_datetime_unix_with_different_client", F.last(
            F.when(
                F.col("prev.client_name") != F.col("curr.client_name"),
                F.col("prev.app_datetime_unix")
            ).otherwise(
                F.lit(None)
            ), ignorenulls=True
        ).over(unbounded_window)
    )
    df_client_hist_attr = df_app_hist.groupBy("curr.APP_NUMBER").agg(
        F.first("curr.client_name").alias("client_name"),
        F.first("curr.app_datetime_unix").alias("app_datetime_unix"),
        F.last("last_app_datetime_unix_with_different_client", ignorenulls=False).alias(
            "last_app_datetime_unix_with_different_client"
        ),
        F.count_distinct("prev.client_name").alias(f'num_client_with_app_{window_size_in_days}d'),
        F.last("last_client_name", ignorenulls=False).alias(f'client_of_last_app_{window_size_in_days}d'),

        F.sum(
            (F.col('prev.client_name') != F.col("curr.client_name")).cast(T.IntegerType())
        ).alias(f'num_app_with_other_client_{window_size_in_days}d'),
        F.sum(
            (F.col('prev.client_name') != F.col("lead_client_name")).cast(T.IntegerType())
        ).alias(f'num_app_client_switch_{window_size_in_days}d'),
        F.count_distinct(
            F.when(
                F.col("prev.client_name") != F.col("curr.client_name"), F.col("prev.client_name")
            ).otherwise(F.lit(None))
        ).alias(f'num_other_client_with_app_{window_size_in_days}d')

    ).withColumn(
        f'same_client_with_last_app_{window_size_in_days}d',
        (F.col("client_name") == F.col(f'client_of_last_app_{window_size_in_days}d')).cast(T.IntegerType())
    ).withColumn(
        f'days_since_last_app_with_another_client_{window_size_in_days}d',
        F.when(
            F.col("last_app_datetime_unix_with_different_client").isNull(),
            F.lit(-2.0)
        ).otherwise(
            F.round(
                (F.col("app_datetime_unix") - F.col("last_app_datetime_unix_with_different_client")) / NUM_SECOND_IN_A_DAY, 2
            )
        )
    ).select(
        "curr.APP_NUMBER",
        f'num_client_with_app_{window_size_in_days}d',
        f'same_client_with_last_app_{window_size_in_days}d',
        f'num_app_client_switch_{window_size_in_days}d',
        f'days_since_last_app_with_another_client_{window_size_in_days}d',
        f'num_other_client_with_app_{window_size_in_days}d',
        f'num_app_with_other_client_{window_size_in_days}d',
    )
    return df_client_hist_attr


def calc_client_hist_attr(
    df_app_attr: DataFrame,
    window_size_in_days: int,
    same_client_delay_hour: int
):
    df_client_hist_attr = _calc_client_hist_attr(
        df_app_attr, window_size_in_days, same_client_delay_hour
    )
    return fillna_no_prev_hist(df_app_attr, df_client_hist_attr, window_size_in_days)


def _calculate_consumer_profile_attr(
    df_app_attr: DataFrame, consumer_key: str, window_size_in_days: int, same_client_delay_hour: int
) -> DataFrame:
    df_no_hit = df_app_attr.filter(F.col(consumer_key).isNull()).select("APP_NUMBER")
    df_hit = df_app_attr.filter(F.col(consumer_key).isNotNull())
    df_app_hist_attr = calc_app_hist_attr(df_hit, window_size_in_days, same_client_delay_hour)
    df_app_hist_attr_all_clients = calc_app_hist_attrs_all_clients(df_hit, window_size_in_days, same_client_delay_hour)
    df_client_hist_attr = calc_client_hist_attr(df_hit, window_size_in_days, same_client_delay_hour)
    df_joined = df_app_hist_attr.join(
        df_app_hist_attr_all_clients, on="APP_NUMBER", how="left_outer"
    ).join(
        df_client_hist_attr, on="APP_NUMBER", how="left_outer"
    )
    for col_name, col_type in df_joined.dtypes:
        if col_name == "APP_NUMBER":
            continue
        df_no_hit = df_no_hit.withColumn(
            col_name, F.lit(NO_HIT_DEFAULT_VALUE).cast(col_type)
        )
    return df_joined.unionByName(df_no_hit)


def calculate_consumer_profile_attrs(
    df_app_attr: DataFrame, consumer_key: str, window_size_list: List[int], same_client_delay_hour: int
) -> DataFrame:
    consumer_profile_attrs = None
    for window_size_in_days in window_size_list:
        df_app_hist_attr = _calculate_consumer_profile_attr(
            df_app_attr, consumer_key, window_size_in_days, same_client_delay_hour
        )
        if consumer_profile_attrs is None:
            consumer_profile_attrs = df_app_hist_attr
        else:
            consumer_profile_attrs = consumer_profile_attrs.join(
                df_app_hist_attr, on="APP_NUMBER", how="outer"
            )
    return consumer_profile_attrs
