import numpy as np


class Preprocessor(object):

    # default constructor - does not accept any input parameters
    # but fixed parameter can be used
    def __init__(self):
        name = "Preprocessor"

    # preprocess method that returns preprocessed data
    def preprocess(self, data: np.ndarray, columns: list) -> tuple:

        credit_indices = [
            i
            for i, col in enumerate(columns)
            if col.startswith("p13_") or col.startswith("t11_")
        ]
        credit_data = data[:, credit_indices].astype(float)
        mask = np.isnan(credit_data)

        ## Fillna with this value for Premier and Trended 3D attributes
        ## NOTE: We will likely change this default value in future models
        CREDIT_ATTR_DEFAULT_VALUE = -1

        credit_data[mask] = (
            CREDIT_ATTR_DEFAULT_VALUE  # fill NaNs in credit col with default value
        )

        data[:, credit_indices] = (
            credit_data  # update the modified credit col back into original data array
        )

        return data, columns
