from collections import defaultdict
import logging
import numpy as np
from numpy import inf, exp, isnan


class Postprocessor(object):

    def __init__(self, score_data_columns, contribution_columns, tag, risk_level):
        name = "Postprocessor"

        self.score_data_columns = score_data_columns
        self.contribution_columns = contribution_columns
        self.tag = tag
        self.risk_level = risk_level

    def postprocess(self, predict_out):
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)

        preprocessed_df, (df_score, df_contribution) = predict_out

        logger.debug("Preprocessed: %s", preprocessed_df)
        logger.debug("Scores: %s", df_score)
        logger.debug("Contributions: %s", df_contribution)

        # copied from preprocess
        constraints = {
            "t11_tpil4218": [0.0, 23.0, -1, "trended_3d_v1_1"],
            "p13_iqb9410": [0.0, 90.0, 1, "premier_1_3"],
            "p13_rev5627": [0.0, 999999990.0, -1, "premier_1_3"],
            "same_email_as_last_app_183d": [0.0, 1.0, 0, "consortium_pii_lookup"],
            "t11_trtr3277": [0.0, 11.0, 1, "trended_3d_v1_1"],
            "p13_all8221": [0.0, 9990.0, -1, "premier_1_3"],
            "p13_rtr8320": [0.0, 9990.0, -1, "premier_1_3"],
            "p13_aua8220": [0.0, 9990.0, -1, "premier_1_3"],
            "p13_all1401": [0.0, 90.0, -1, "premier_1_3"],
            "p13_bca6220": [0.0, 400.0, 1, "premier_1_3"],
            "t11_taua4217": [0.0, 23.0, -1, "trended_3d_v1_1"],
            "p13_rev7432": [0.0, 100.0, -1, "premier_1_3"],
            "num_sales_channel_183d": [1.0, inf, 1, "consumer_profile_app_based"],
            "t11_tbca4255": [0.0, 999999990.0, 1, "trended_3d_v1_1"],
            "num_email_91d": [1.0, inf, 1, "consortium_pii_lookup"],
            "p13_bcc5627": [0.0, 999999990.0, -1, "premier_1_3"],
            "p13_all6220": [0.0, 400.0, 1, "premier_1_3"],
            "p13_iqt9410": [0.0, 90.0, 1, "premier_1_3"],
            "p13_stu6280": [0.0, 400.0, 1, "premier_1_3"],
            "num_app_using_ssn_same_identity_183d": [
                0.0,
                inf,
                1,
                "consortium_pii_lookup",
            ],
            "t11_tbca3270": [0.0, 12.0, -1, "trended_3d_v1_1"],
            "t11_trtr4504": [0.0, 24.0, -1, "trended_3d_v1_1"],
            "same_addr_as_last_app_91d": [0.0, 1.0, 0, "consortium_pii_lookup"],
            "p13_bcc8124": [0.0, 9990.0, -1, "premier_1_3"],
            "p13_alm2347": [0.0, 90.0, 1, "premier_1_3"],
            "t11_tbca3504": [0.0, 12.0, -1, "trended_3d_v1_1"],
            "t11_tall1410": [0.0, 990.0, -1, "trended_3d_v1_1"],
            "p13_all8323": [0.0, 9990.0, -1, "premier_1_3"],
            "p13_aut7110": [0.0, 990.0, 1, "premier_1_3"],
            "t11_tbca4103": [0.0, 990.0, 1, "trended_3d_v1_1"],
            "t11_trtr3263": [0.0, 12.0, -1, "trended_3d_v1_1"],
            "t11_tmti2215": [0.0, 5.0, 1, "trended_3d_v1_1"],
            "t11_trev3243": [0.0, 990.0, -1, "trended_3d_v1_1"],
            "t11_trtr4203": [-999999990.0, 999999990.0, 1, "trended_3d_v1_1"],
            "p13_brc5620": [0.0, 999999990.0, -1, "premier_1_3"],
            "p13_stu5820": [0.0, 999999990.0, 1, "premier_1_3"],
            "num_email_183d": [1.0, inf, 1, "consortium_pii_lookup"],
            "num_app_using_addr_same_identity_183d": [
                0.0,
                inf,
                1,
                "consortium_pii_lookup",
            ],
            "days_since_first_app_183d": [0.0, inf, 0, "consumer_profile_app_based"],
            "days_since_last_app_with_another_client_183d": [
                0.0,
                inf,
                -1,
                "consumer_profile_app_based",
            ],
            "t11_tbca2273": [0.0, 999999990.0, 1, "trended_3d_v1_1"],
            "p13_all2011": [0.0, 90.0, -1, "premier_1_3"],
            "p13_alm6160": [0.0, 400.0, 1, "premier_1_3"],
            "p13_rev6220": [0.0, 400.0, 1, "premier_1_3"],
            "p13_hlc7117": [0.0, 990.0, 1, "premier_1_3"],
            "p13_reh7120": [0.0, 990.0, 1, "premier_1_3"],
            "p13_bcn5020": [0.0, 999999990.0, 1, "premier_1_3"],
            "num_app_using_email_same_identity_91d": [
                0.0,
                inf,
                0,
                "consortium_pii_lookup",
            ],
            "days_since_last_app_using_addr_same_identity_7d": [
                0.0,
                inf,
                0,
                "consortium_pii_lookup",
            ],
            "t11_taua4751": [0.0, 999999990.0, 1, "trended_3d_v1_1"],
            "p13_all2184": [0.0, 90.0, 1, "premier_1_3"],
            "p13_bcc5620": [0.0, 999999990.0, -1, "premier_1_3"],
            "p13_aut5838": [0.0, 999999990.0, 1, "premier_1_3"],
            "p13_all2005": [0.0, 90.0, -1, "premier_1_3"],
            "t11_tbca4504": [0.0, 24.0, -1, "trended_3d_v1_1"],
            "p13_all6250": [0.0, 400.0, 1, "premier_1_3"],
            "t11_taua4218": [0.0, 23.0, -1, "trended_3d_v1_1"],
            "num_id_type_183d_with_client": [1.0, inf, 1, "consumer_profile_app_based"],
            "p13_alm2353": [0.0, 90.0, 1, "premier_1_3"],
            "t11_tall3205": [0.0, 12.0, -1, "trended_3d_v1_1"],
            "t11_trtr3260": [0.0, 999999990.0, -1, "trended_3d_v1_1"],
            "p13_mta8160": [0.0, 9990.0, -1, "premier_1_3"],
            "days_since_last_app_using_ssn_same_identity_30d": [
                0.0,
                inf,
                0,
                "consortium_pii_lookup",
            ],
            "num_phone_183d": [1.0, inf, 1, "consortium_pii_lookup"],
            "days_since_last_app_using_addr_same_identity_183d": [
                0.0,
                inf,
                0,
                "consortium_pii_lookup",
            ],
            "p13_als1300": [0.0, 90.0, -1, "premier_1_3"],
            "p13_all2009": [0.0, 90.0, -1, "premier_1_3"],
            "days_since_last_app_using_email_same_identity_30d": [
                0.0,
                inf,
                0,
                "consortium_pii_lookup",
            ],
            "p13_alm2318": [0.0, 90.0, 1, "premier_1_3"],
            "t11_tbca3273": [0.0, 999999990.0, 1, "trended_3d_v1_1"],
            "p13_rta5020": [0.0, 999999990.0, 1, "premier_1_3"],
            "t11_tbca3278": [0.0, 11.0, 1, "trended_3d_v1_1"],
            "p13_all7333": [0.0, 100.0, -1, "premier_1_3"],
            "p13_all6230": [0.0, 400.0, 1, "premier_1_3"],
            "p13_rta5530": [0.0, 999999990.0, 1, "premier_1_3"],
            "same_sales_channel_with_last_app_183d": [
                0.0,
                1.0,
                -1,
                "consumer_profile_app_based",
            ],
            "same_addr_as_last_app_7d": [0.0, 1.0, 0, "consortium_pii_lookup"],
            "t11_tstu04q1": [0.0, 999999990.0, -1, "trended_3d_v1_1"],
            "days_since_last_app_with_another_client_91d": [
                0.0,
                inf,
                -1,
                "consumer_profile_app_based",
            ],
            "p13_iqa9740": [0.0, 9990.0, -1, "premier_1_3"],
            "p13_iln6210": [0.0, 400.0, 1, "premier_1_3"],
            "p13_mta7430": [0.0, 100.0, -1, "premier_1_3"],
            "p13_rev6230": [0.0, 400.0, 1, "premier_1_3"],
            "t11_tbca3274": [0.0, 999999990.0, -1, "trended_3d_v1_1"],
            "t11_tall2410": [0.0, 990.0, -1, "trended_3d_v1_1"],
            "p13_bcc7147": [0.0, 990.0, 1, "premier_1_3"],
            "p13_mta6220": [0.0, 400.0, 1, "premier_1_3"],
            "p13_mtf7110": [0.0, 990.0, 1, "premier_1_3"],
            "p13_iln7432": [0.0, 100.0, -1, "premier_1_3"],
            "p13_bca8320": [0.0, 9990.0, -1, "premier_1_3"],
            "t11_taua2215": [0.0, 5.0, 1, "trended_3d_v1_1"],
            "t11_tmti3421": [0.0, 999999990.0, 1, "trended_3d_v1_1"],
            "t11_tbca4303": [-990.0, 990.0, 1, "trended_3d_v1_1"],
            "p13_cru6280": [0.0, 400.0, 1, "premier_1_3"],
            "p13_aua8320": [0.0, 9990.0, -1, "premier_1_3"],
            "p13_iln6220": [0.0, 400.0, 1, "premier_1_3"],
            "p13_mtf5100": [0.0, 999999990.0, 1, "premier_1_3"],
            "days_since_last_app_using_phone_same_identity_30d": [
                0.0,
                inf,
                0,
                "consortium_pii_lookup",
            ],
            "p13_all2182": [0.0, 90.0, 1, "premier_1_3"],
            "t11_tmti3215": [0.0, 11.0, 1, "trended_3d_v1_1"],
            "t11_trev4243": [0.0, 990.0, -1, "trended_3d_v1_1"],
            "p13_iqt9417": [0.0, 90.0, 1, "premier_1_3"],
            "days_since_last_app_using_ssn_same_identity_183d": [
                0.0,
                inf,
                0,
                "consortium_pii_lookup",
            ],
            "p13_bcc8322": [0.0, 9990.0, -1, "premier_1_3"],
            "p13_brc7140": [0.0, 990.0, 1, "premier_1_3"],
            "days_since_last_app_using_addr_same_identity_91d_with_client": [
                0.0,
                inf,
                0,
                "consortium_pii_lookup",
            ],
            "t11_tbcc4205": [0.0, 24.0, -1, "trended_3d_v1_1"],
            "p13_reh8227": [0.0, 9990.0, -1, "premier_1_3"],
            "t11_tbca2381": [0.0, 990.0, 1, "trended_3d_v1_1"],
            "p13_rta8320": [0.0, 9990.0, -1, "premier_1_3"],
            "p13_bcc7117": [0.0, 990.0, 1, "premier_1_3"],
            "p13_aut5620": [0.0, 999999990.0, -1, "premier_1_3"],
            "p13_stu8125": [0.0, 9990.0, -1, "premier_1_3"],
            "p13_all7332": [0.0, 100.0, -1, "premier_1_3"],
            "p13_all7120": [0.0, 990.0, 1, "premier_1_3"],
            "num_id_type_30d": [1.0, inf, 1, "consumer_profile_app_based"],
            "p13_aut5820": [0.0, 999999990.0, 1, "premier_1_3"],
            "p13_pil8220": [0.0, 9990.0, -1, "premier_1_3"],
            "p13_alm2317": [0.0, 90.0, 1, "premier_1_3"],
            "days_since_last_app_using_email_same_identity_183d_with_client": [
                0.0,
                inf,
                0,
                "consortium_pii_lookup",
            ],
            "t11_tbcc4363": [0.0, 24.0, -1, "trended_3d_v1_1"],
            "t11_tmti2606": [-999999990.0, 999999990.0, 1, "trended_3d_v1_1"],
            "t11_tall1205": [0.0, 3.0, -1, "trended_3d_v1_1"],
            "p13_all1300": [0.0, 90.0, -1, "premier_1_3"],
            "min_app_date_gap_183d": [0.0, inf, -1, "consumer_profile_app_based"],
            "t11_tall4205": [0.0, 24.0, -1, "trended_3d_v1_1"],
            "num_app_using_addr_same_identity_183d_with_client": [
                0.0,
                inf,
                1,
                "consortium_pii_lookup",
            ],
            "num_sales_channel_91d": [1.0, inf, 1, "consumer_profile_app_based"],
            "days_since_last_app_using_phone_same_identity_183d": [
                0.0,
                inf,
                0,
                "consortium_pii_lookup",
            ],
            "num_id_type_183d": [1.0, inf, 1, "consumer_profile_app_based"],
            "t11_trev4244": [0.0, 990.0, -1, "trended_3d_v1_1"],
            "days_since_last_app_using_addr_same_identity_30d": [
                0.0,
                inf,
                0,
                "consortium_pii_lookup",
            ],
            "t11_tcol2592": [0.0, 999999990.0, 1, "trended_3d_v1_1"],
            "p13_alm2348": [0.0, 90.0, 1, "premier_1_3"],
            "days_since_last_app_using_phone_same_identity_91d": [
                0.0,
                inf,
                0,
                "consortium_pii_lookup",
            ],
            "t11_trtr4280": [0.0, 24.0, 1, "trended_3d_v1_1"],
            "p13_aua1300": [0.0, 90.0, -1, "premier_1_3"],
            "p13_all2183": [0.0, 90.0, 1, "premier_1_3"],
            "t11_tall2205": [0.0, 6.0, -1, "trended_3d_v1_1"],
            "p13_rta1300": [0.0, 90.0, -1, "premier_1_3"],
            "t11_tbca2530": [0.0, 999999990.0, -1, "trended_3d_v1_1"],
            "t11_tcau04q1": [0.0, 999999990.0, -1, "trended_3d_v1_1"],
            "p13_iqb9416": [0.0, 90.0, 1, "premier_1_3"],
            "p13_rev7410": [0.0, 100.0, -1, "premier_1_3"],
            "days_since_last_app_using_email_same_identity_91d": [
                0.0,
                inf,
                0,
                "consortium_pii_lookup",
            ],
            "p13_all8351": [0.0, 9990.0, -1, "premier_1_3"],
            "p13_rev5620": [0.0, 999999990.0, -1, "premier_1_3"],
            "t11_tmti4751": [0.0, 999999990.0, 1, "trended_3d_v1_1"],
            "same_phone_as_last_app_183d": [0.0, 1.0, 0, "consortium_pii_lookup"],
            "t11_tall2412": [0.0, 990.0, -1, "trended_3d_v1_1"],
            "p13_rtr1380": [0.0, 90.0, -1, "premier_1_3"],
            "t11_trev2244": [0.0, 990.0, -1, "trended_3d_v1_1"],
            "p13_rev1300": [0.0, 90.0, -1, "premier_1_3"],
            "p13_alm2310": [0.0, 90.0, 1, "premier_1_3"],
            "p13_all2012": [0.0, 90.0, -1, "premier_1_3"],
            "p13_rtr8220": [0.0, 9990.0, -1, "premier_1_3"],
            "days_since_last_app_using_email_same_identity_183d": [
                0.0,
                inf,
                0,
                "consortium_pii_lookup",
            ],
            "t11_tmti3751": [0.0, 999999990.0, 1, "trended_3d_v1_1"],
        }

        D_aa_credit_attr = {
            "t11_tpil4218": {
                "AA Code": "TD",
                "AA Code Message": "Time since balance increase on installment loans is too short",
                "Default AA Code": "X7",
                "Default AA Code Message": "Insufficient information on installment loans",
            },
            "p13_iqb9410": {
                "AA Code": "E6",
                "AA Code Message": "Number of inquiries is too high",
                "Default AA Code": "XL",
                "Default AA Code Message": "",
            },
            "p13_rev5627": {
                "AA Code": "B9",
                "AA Code Message": "Balance compared to credit amount on revolving accounts is too high",
                "Default AA Code": "XH",
                "Default AA Code Message": "Insufficient information on revolving accounts",
            },
            "t11_trtr3277": {
                "AA Code": "T9",
                "AA Code Message": "Time since balance decrease on credit cards is too long",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_all8221": {
                "AA Code": "TH",
                "AA Code Message": "Time since opening accounts is too short",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_rtr8320": {
                "AA Code": "TK",
                "AA Code Message": "Time since opening credit cards is too short",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_aua8220": {
                "AA Code": "TN",
                "AA Code Message": "Time since opening installment loans is too short",
                "Default AA Code": "X7",
                "Default AA Code Message": "Insufficient information on installment loans",
            },
            "p13_all1401": {
                "AA Code": "FO",
                "AA Code Message": "Number of open, satisfactory accounts is too low",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_bca6220": {
                "AA Code": "W2",
                "AA Code Message": "Worst delinquency on credit cards is too high",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "t11_taua4217": {
                "AA Code": "TD",
                "AA Code Message": "Time since balance increase on installment loans is too short",
                "Default AA Code": "X7",
                "Default AA Code Message": "Insufficient information on installment loans",
            },
            "p13_rev7432": {
                "AA Code": "NG",
                "AA Code Message": "Number of recent, satisfactory revolving accounts is too low",
                "Default AA Code": "XH",
                "Default AA Code Message": "Insufficient information on revolving accounts",
            },
            "t11_tbca4255": {
                "AA Code": "BZ",
                "AA Code Message": "Balance increase on credit cards is too high",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_bcc5627": {
                "AA Code": "B3",
                "AA Code Message": "Balance compared to credit amount on credit cards is too high",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_all6220": {
                "AA Code": "W1",
                "AA Code Message": "Worst delinquency on accounts is too high",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_iqt9410": {
                "AA Code": "E6",
                "AA Code Message": "Number of inquiries is too high",
                "Default AA Code": "XL",
                "Default AA Code Message": "",
            },
            "p13_stu6280": {
                "AA Code": "WA",
                "AA Code Message": "Worst delinquency on student loans is too high",
                "Default AA Code": "XI",
                "Default AA Code Message": "Insufficient information on student loans",
            },
            "t11_tbca3270": {
                "AA Code": "TF",
                "AA Code Message": "Time since high balance on credit cards is too short",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "t11_trtr4504": {
                "AA Code": "TF",
                "AA Code Message": "Time since high balance on credit cards is too short",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_bcc8124": {
                "AA Code": "TK",
                "AA Code Message": "Time since opening credit cards is too short",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_alm2347": {
                "AA Code": "ES",
                "AA Code Message": "Number of delinquent, derogatory accounts is too high",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "t11_tbca3504": {
                "AA Code": "TF",
                "AA Code Message": "Time since high balance on credit cards is too short",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "t11_tall1410": {
                "AA Code": "M1",
                "AA Code Message": "Number of balance decreases on accounts is too low",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_all8323": {
                "AA Code": "T1",
                "AA Code Message": "Time since delinquent, derogatory accounts is too short",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_aut7110": {
                "AA Code": "B4",
                "AA Code Message": "Balance compared to credit amount on installment loans is too high",
                "Default AA Code": "X7",
                "Default AA Code Message": "Insufficient information on installment loans",
            },
            "t11_tbca4103": {
                "AA Code": "EL",
                "AA Code Message": "Number of active credit cards is too high",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "t11_trtr3263": {
                "AA Code": "TC",
                "AA Code Message": "Time since balance increase on credit cards is too short",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "t11_tmti2215": {
                "AA Code": "TB",
                "AA Code Message": "Time since balance decrease on mortgages is too long",
                "Default AA Code": "XD",
                "Default AA Code Message": "Insufficient information on mortgages",
            },
            "t11_trev3243": {
                "AA Code": "M3",
                "AA Code Message": "Number of balance decreases on revolving accounts is too low",
                "Default AA Code": "XH",
                "Default AA Code Message": "Insufficient information on revolving accounts",
            },
            "t11_trtr4203": {
                "AA Code": "BZ",
                "AA Code Message": "Balance increase on credit cards is too high",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_brc5620": {
                "AA Code": "B3",
                "AA Code Message": "Balance compared to credit amount on credit cards is too high",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_stu5820": {
                "AA Code": "SE",
                "AA Code Message": "Scheduled payment on student loans is too high",
                "Default AA Code": "XI",
                "Default AA Code Message": "Insufficient information on student loans",
            },
            "t11_tbca2273": {
                "AA Code": "BZ",
                "AA Code Message": "Balance increase on credit cards is too high",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_all2011": {
                "AA Code": "NU",
                "AA Code Message": "Number of satisfied delinquent, derogatory accounts is too low",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_alm6160": {
                "AA Code": "W1",
                "AA Code Message": "Worst delinquency on accounts is too high",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_rev6220": {
                "AA Code": "W9",
                "AA Code Message": "Worst delinquency on revolving accounts is too high",
                "Default AA Code": "XH",
                "Default AA Code Message": "Insufficient information on revolving accounts",
            },
            "p13_hlc7117": {
                "AA Code": "B9",
                "AA Code Message": "Balance compared to credit amount on revolving accounts is too high",
                "Default AA Code": "XH",
                "Default AA Code Message": "Insufficient information on revolving accounts",
            },
            "p13_reh7120": {
                "AA Code": "B9",
                "AA Code Message": "Balance compared to credit amount on revolving accounts is too high",
                "Default AA Code": "XH",
                "Default AA Code Message": "Insufficient information on revolving accounts",
            },
            "p13_bcn5020": {
                "AA Code": "BE",
                "AA Code Message": "Balance on credit cards is too high",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "t11_taua4751": {
                "AA Code": "S4",
                "AA Code Message": "Scheduled payment on installment loans is too high",
                "Default AA Code": "X7",
                "Default AA Code Message": "Insufficient information on installment loans",
            },
            "p13_all2184": {
                "AA Code": "ES",
                "AA Code Message": "Number of delinquent, derogatory accounts is too high",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_bcc5620": {
                "AA Code": "B3",
                "AA Code Message": "Balance compared to credit amount on credit cards is too high",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_aut5838": {
                "AA Code": "S4",
                "AA Code Message": "Scheduled payment on installment loans is too high",
                "Default AA Code": "X7",
                "Default AA Code Message": "Insufficient information on installment loans",
            },
            "p13_all2005": {
                "AA Code": "NU",
                "AA Code Message": "Number of satisfied delinquent, derogatory accounts is too low",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "t11_tbca4504": {
                "AA Code": "TF",
                "AA Code Message": "Time since high balance on credit cards is too short",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_all6250": {
                "AA Code": "W1",
                "AA Code Message": "Worst delinquency on accounts is too high",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "t11_taua4218": {
                "AA Code": "TD",
                "AA Code Message": "Time since balance increase on installment loans is too short",
                "Default AA Code": "X7",
                "Default AA Code Message": "Insufficient information on installment loans",
            },
            "p13_alm2353": {
                "AA Code": "ES",
                "AA Code Message": "Number of delinquent, derogatory accounts is too high",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "t11_tall3205": {
                "AA Code": "BU",
                "AA Code Message": "Balance decrease on accounts is too low",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "t11_trtr3260": {
                "AA Code": "BV",
                "AA Code Message": "Balance decrease on credit cards is too low",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_mta8160": {
                "AA Code": "T5",
                "AA Code Message": "Time since delinquent, derogatory mortgages is too short",
                "Default AA Code": "XD",
                "Default AA Code Message": "Insufficient information on mortgages",
            },
            "p13_als1300": {
                "AA Code": "N7",
                "AA Code Message": "Number of recent, satisfactory accounts is too low",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_all2009": {
                "AA Code": "N7",
                "AA Code Message": "Number of recent, satisfactory accounts is too low",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_alm2318": {
                "AA Code": "ES",
                "AA Code Message": "Number of delinquent, derogatory accounts is too high",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "t11_tbca3273": {
                "AA Code": "BZ",
                "AA Code Message": "Balance increase on credit cards is too high",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_rta5020": {
                "AA Code": "BE",
                "AA Code Message": "Balance on credit cards is too high",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "t11_tbca3278": {
                "AA Code": "T9",
                "AA Code Message": "Time since balance decrease on credit cards is too long",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_all7333": {
                "AA Code": "N7",
                "AA Code Message": "Number of recent, satisfactory accounts is too low",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_all6230": {
                "AA Code": "W1",
                "AA Code Message": "Worst delinquency on accounts is too high",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_rta5530": {
                "AA Code": "BE",
                "AA Code Message": "Balance on credit cards is too high",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "t11_tstu04q1": {
                "AA Code": "A5",
                "AA Code Message": "Actual payment on student loans is too low",
                "Default AA Code": "XI",
                "Default AA Code Message": "Insufficient information on student loans",
            },
            "p13_iqa9740": {
                "AA Code": "SG",
                "AA Code Message": "Time since inquiries is too short",
                "Default AA Code": "XL",
                "Default AA Code Message": "",
            },
            "p13_iln6210": {
                "AA Code": "W5",
                "AA Code Message": "Worst delinquency on installment loans is too high",
                "Default AA Code": "X7",
                "Default AA Code Message": "Insufficient information on installment loans",
            },
            "p13_mta7430": {
                "AA Code": "NE",
                "AA Code Message": "Number of recent, satisfactory mortgages is too low",
                "Default AA Code": "XD",
                "Default AA Code Message": "Insufficient information on mortgages",
            },
            "p13_rev6230": {
                "AA Code": "W9",
                "AA Code Message": "Worst delinquency on revolving accounts is too high",
                "Default AA Code": "XH",
                "Default AA Code Message": "Insufficient information on revolving accounts",
            },
            "t11_tbca3274": {
                "AA Code": "BV",
                "AA Code Message": "Balance decrease on credit cards is too low",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "t11_tall2410": {
                "AA Code": "M1",
                "AA Code Message": "Number of balance decreases on accounts is too low",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_bcc7147": {
                "AA Code": "B3",
                "AA Code Message": "Balance compared to credit amount on credit cards is too high",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_mta6220": {
                "AA Code": "W7",
                "AA Code Message": "Worst delinquency on mortgages is too high",
                "Default AA Code": "XD",
                "Default AA Code Message": "Insufficient information on mortgages",
            },
            "p13_mtf7110": {
                "AA Code": "B6",
                "AA Code Message": "Balance compared to credit amount on mortgages is too high",
                "Default AA Code": "XD",
                "Default AA Code Message": "Insufficient information on mortgages",
            },
            "p13_iln7432": {
                "AA Code": "NC",
                "AA Code Message": "Number of recent, satisfactory installment loans is too low",
                "Default AA Code": "X7",
                "Default AA Code Message": "Insufficient information on installment loans",
            },
            "p13_bca8320": {
                "AA Code": "TK",
                "AA Code Message": "Time since opening credit cards is too short",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "t11_taua2215": {
                "AA Code": "TA",
                "AA Code Message": "Time since balance decrease on installment loans is too long",
                "Default AA Code": "X7",
                "Default AA Code Message": "Insufficient information on installment loans",
            },
            "t11_tmti3421": {
                "AA Code": "M6",
                "AA Code Message": "Number of balance increases on mortgages is too high",
                "Default AA Code": "XD",
                "Default AA Code Message": "Insufficient information on mortgages",
            },
            "t11_tbca4303": {
                "AA Code": "BZ",
                "AA Code Message": "Balance increase on credit cards is too high",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_cru6280": {
                "AA Code": "W3",
                "AA Code Message": "Worst delinquency on credit union accounts is too high",
                "Default AA Code": "X5",
                "Default AA Code Message": "Insufficient information on credit union accounts",
            },
            "p13_aua8320": {
                "AA Code": "TN",
                "AA Code Message": "Time since opening installment loans is too short",
                "Default AA Code": "X7",
                "Default AA Code Message": "Insufficient information on installment loans",
            },
            "p13_iln6220": {
                "AA Code": "W5",
                "AA Code Message": "Worst delinquency on installment loans is too high",
                "Default AA Code": "X7",
                "Default AA Code Message": "Insufficient information on installment loans",
            },
            "p13_mtf5100": {
                "AA Code": "BN",
                "AA Code Message": "Balance on mortgages is too high",
                "Default AA Code": "XD",
                "Default AA Code Message": "Insufficient information on mortgages",
            },
            "p13_all2182": {
                "AA Code": "ES",
                "AA Code Message": "Number of delinquent, derogatory accounts is too high",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "t11_tmti3215": {
                "AA Code": "TB",
                "AA Code Message": "Time since balance decrease on mortgages is too long",
                "Default AA Code": "XD",
                "Default AA Code Message": "Insufficient information on mortgages",
            },
            "t11_trev4243": {
                "AA Code": "M3",
                "AA Code Message": "Number of balance decreases on revolving accounts is too low",
                "Default AA Code": "XH",
                "Default AA Code Message": "Insufficient information on revolving accounts",
            },
            "p13_iqt9417": {
                "AA Code": "E6",
                "AA Code Message": "Number of inquiries is too high",
                "Default AA Code": "XL",
                "Default AA Code Message": "",
            },
            "p13_bcc8322": {
                "AA Code": "TK",
                "AA Code Message": "Time since opening credit cards is too short",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_brc7140": {
                "AA Code": "B3",
                "AA Code Message": "Balance compared to credit amount on credit cards is too high",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "t11_tbcc4205": {
                "AA Code": "BV",
                "AA Code Message": "Balance decrease on credit cards is too low",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_reh8227": {
                "AA Code": "TW",
                "AA Code Message": "Time since opening revolving accounts is too short",
                "Default AA Code": "XH",
                "Default AA Code Message": "Insufficient information on revolving accounts",
            },
            "t11_tbca2381": {
                "AA Code": "B3",
                "AA Code Message": "Balance compared to credit amount on credit cards is too high",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_rta8320": {
                "AA Code": "TK",
                "AA Code Message": "Time since opening credit cards is too short",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_bcc7117": {
                "AA Code": "B3",
                "AA Code Message": "Balance compared to credit amount on credit cards is too high",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_aut5620": {
                "AA Code": "B4",
                "AA Code Message": "Balance compared to credit amount on installment loans is too high",
                "Default AA Code": "X7",
                "Default AA Code Message": "Insufficient information on installment loans",
            },
            "p13_stu8125": {
                "AA Code": "TX",
                "AA Code Message": "Time since opening student loans is too short",
                "Default AA Code": "XI",
                "Default AA Code Message": "Insufficient information on student loans",
            },
            "p13_all7332": {
                "AA Code": "N7",
                "AA Code Message": "Number of recent, satisfactory accounts is too low",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_all7120": {
                "AA Code": "B1",
                "AA Code Message": "Balance compared to credit amount on accounts is too high",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_aut5820": {
                "AA Code": "S4",
                "AA Code Message": "Scheduled payment on installment loans is too high",
                "Default AA Code": "X7",
                "Default AA Code Message": "Insufficient information on installment loans",
            },
            "p13_pil8220": {
                "AA Code": "TN",
                "AA Code Message": "Time since opening installment loans is too short",
                "Default AA Code": "X7",
                "Default AA Code Message": "Insufficient information on installment loans",
            },
            "p13_alm2317": {
                "AA Code": "ES",
                "AA Code Message": "Number of delinquent, derogatory accounts is too high",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "t11_tbcc4363": {
                "AA Code": "TC",
                "AA Code Message": "Time since balance increase on credit cards is too short",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "t11_tmti2606": {
                "AA Code": "AM",
                "AA Code Message": "Balance increase on mortgages is too high",
                "Default AA Code": "XD",
                "Default AA Code Message": "Insufficient information on mortgages",
            },
            "t11_tall1205": {
                "AA Code": "BU",
                "AA Code Message": "Balance decrease on accounts is too low",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_all1300": {
                "AA Code": "N7",
                "AA Code Message": "Number of recent, satisfactory accounts is too low",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "t11_tall4205": {
                "AA Code": "BU",
                "AA Code Message": "Balance decrease on accounts is too low",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "t11_trev4244": {
                "AA Code": "M3",
                "AA Code Message": "Number of balance decreases on revolving accounts is too low",
                "Default AA Code": "XH",
                "Default AA Code Message": "Insufficient information on revolving accounts",
            },
            "t11_tcol2592": {
                "AA Code": "D1",
                "AA Code Message": "Delinquent, derogatory balance on accounts is too high",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_alm2348": {
                "AA Code": "ES",
                "AA Code Message": "Number of delinquent, derogatory accounts is too high",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "t11_trtr4280": {
                "AA Code": "T9",
                "AA Code Message": "Time since balance decrease on credit cards is too long",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "p13_aua1300": {
                "AA Code": "NC",
                "AA Code Message": "Number of recent, satisfactory installment loans is too low",
                "Default AA Code": "X7",
                "Default AA Code Message": "Insufficient information on installment loans",
            },
            "p13_all2183": {
                "AA Code": "ES",
                "AA Code Message": "Number of delinquent, derogatory accounts is too high",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "t11_tall2205": {
                "AA Code": "BU",
                "AA Code Message": "Balance decrease on accounts is too low",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_rta1300": {
                "AA Code": "N8",
                "AA Code Message": "Number of recent, satisfactory credit cards is too low",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "t11_tbca2530": {
                "AA Code": "B3",
                "AA Code Message": "Balance compared to credit amount on credit cards is too high",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "t11_tcau04q1": {
                "AA Code": "A2",
                "AA Code Message": "Actual payment on installment loans is too low",
                "Default AA Code": "X7",
                "Default AA Code Message": "Insufficient information on installment loans",
            },
            "p13_iqb9416": {
                "AA Code": "E6",
                "AA Code Message": "Number of inquiries is too high",
                "Default AA Code": "XL",
                "Default AA Code Message": "",
            },
            "p13_rev7410": {
                "AA Code": "FM",
                "AA Code Message": "Number of open, recently reported revolving accounts is too low",
                "Default AA Code": "XH",
                "Default AA Code Message": "Insufficient information on revolving accounts",
            },
            "p13_all8351": {
                "AA Code": "T1",
                "AA Code Message": "Time since delinquent, derogatory accounts is too short",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_rev5620": {
                "AA Code": "B9",
                "AA Code Message": "Balance compared to credit amount on revolving accounts is too high",
                "Default AA Code": "XH",
                "Default AA Code Message": "Insufficient information on revolving accounts",
            },
            "t11_tmti4751": {
                "AA Code": "S9",
                "AA Code Message": "Scheduled payment on mortgages is too high",
                "Default AA Code": "XD",
                "Default AA Code Message": "Insufficient information on mortgages",
            },
            "t11_tall2412": {
                "AA Code": "M1",
                "AA Code Message": "Number of balance decreases on accounts is too low",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_rtr1380": {
                "AA Code": "N8",
                "AA Code Message": "Number of recent, satisfactory credit cards is too low",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "t11_trev2244": {
                "AA Code": "M3",
                "AA Code Message": "Number of balance decreases on revolving accounts is too low",
                "Default AA Code": "XH",
                "Default AA Code Message": "Insufficient information on revolving accounts",
            },
            "p13_rev1300": {
                "AA Code": "NG",
                "AA Code Message": "Number of recent, satisfactory revolving accounts is too low",
                "Default AA Code": "XH",
                "Default AA Code Message": "Insufficient information on revolving accounts",
            },
            "p13_alm2310": {
                "AA Code": "ES",
                "AA Code Message": "Number of delinquent, derogatory accounts is too high",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_all2012": {
                "AA Code": "NU",
                "AA Code Message": "Number of satisfied delinquent, derogatory accounts is too low",
                "Default AA Code": "X1",
                "Default AA Code Message": "Insufficient information on accounts",
            },
            "p13_rtr8220": {
                "AA Code": "TK",
                "AA Code Message": "Time since opening credit cards is too short",
                "Default AA Code": "X4",
                "Default AA Code Message": "Insufficient information on credit cards",
            },
            "t11_tmti3751": {
                "AA Code": "S9",
                "AA Code Message": "Scheduled payment on mortgages is too high",
                "Default AA Code": "XD",
                "Default AA Code Message": "Insufficient information on mortgages",
            },
        }

        D_aa_fdn_attr = defaultdict(
            list,
            {
                "same_email_as_last_app": [
                    {
                        "min": 0,
                        "max": 0.0,
                        "AA Code": "TME",
                        "AA Code Message": "Number of emails ever used in Telco applications is too high",
                    },
                    {
                        "min": 1,
                        "max": 1.0,
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                ],
                "num_sales_channel": [
                    {
                        "min": 1,
                        "max": 1.0,
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                    {
                        "min": 2,
                        "max": inf,
                        "AA Code": "TSC",
                        "AA Code Message": "Applied from mixed channels",
                    },
                ],
                "num_email": [
                    {
                        "min": 1,
                        "max": 1.0,
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                    {
                        "min": 2,
                        "max": inf,
                        "AA Code": "TME",
                        "AA Code Message": "Number of emails ever used in Telco applications is too high",
                    },
                ],
                "num_app_using_ssn_same_identity": [
                    {
                        "min": 0,
                        "max": 0.0,
                        "AA Code": "TMS",
                        "AA Code Message": "Number of SSNs ever used in Telco applications is too high",
                    },
                    {
                        "min": 1,
                        "max": inf,
                        "AA Code": "TAPS",
                        "AA Code Message": "Number of past Telco applications using same SSN is too high",
                    },
                ],
                "same_addr_as_last_app": [
                    {
                        "min": 0,
                        "max": 0.0,
                        "AA Code": "TMA",
                        "AA Code Message": "Number of addresses ever used in Telco applications is too high",
                    },
                    {
                        "min": 1,
                        "max": 1.0,
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                ],
                "num_app_using_addr_same_identity": [
                    {
                        "min": 0,
                        "max": 0.0,
                        "AA Code": "TMA",
                        "AA Code Message": "Number of addresses ever used in Telco applications is too high",
                    },
                    {
                        "min": 1,
                        "max": inf,
                        "AA Code": "TAPA",
                        "AA Code Message": "Number of past Telco applications using same address is too high",
                    },
                ],
                "days_since_first_app": [
                    {
                        "min": 0,
                        "max": inf,
                        "AA Code": "TRC",
                        "AA Code Message": "Time since last Telco application is too short",
                    }
                ],
                "days_since_last_app_with_another_client": [
                    {
                        "min": 0,
                        "max": inf,
                        "AA Code": "TMC",
                        "AA Code Message": "Number of different carriers applied from is too high",
                    }
                ],
                "num_app_using_email_same_identity": [
                    {
                        "min": 0,
                        "max": 0.0,
                        "AA Code": "TME",
                        "AA Code Message": "Number of emails ever used in Telco applications is too high",
                    },
                    {
                        "min": 1,
                        "max": inf,
                        "AA Code": "TAPE",
                        "AA Code Message": "Number of past Telco applications using same email is too high",
                    },
                ],
                "days_since_last_app_using_addr_same_identity": [
                    {
                        "min": 0,
                        "max": inf,
                        "AA Code": "TRCA",
                        "AA Code Message": "Time since the last Telco application using the same address is too short",
                    }
                ],
                "num_id_type": [
                    {
                        "min": 1,
                        "max": 1.0,
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                    {
                        "min": 2,
                        "max": inf,
                        "AA Code": "TID",
                        "AA Code Message": "Multiple types of identity document in current and past Telco applications",
                    },
                ],
                "days_since_last_app_using_ssn_same_identity": [
                    {
                        "min": 0,
                        "max": inf,
                        "AA Code": "TRCS",
                        "AA Code Message": "Time since the last Telco application using the same SSN is too short",
                    }
                ],
                "num_phone": [
                    {
                        "min": 1,
                        "max": 1.0,
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                    {
                        "min": 2,
                        "max": inf,
                        "AA Code": "TMP",
                        "AA Code Message": "Number of phones ever used in Telco applications is too high",
                    },
                ],
                "days_since_last_app_using_email_same_identity": [
                    {
                        "min": 0,
                        "max": inf,
                        "AA Code": "TRCE",
                        "AA Code Message": "Time since the last Telco application using the same email is too short",
                    }
                ],
                "same_sales_channel_with_last_app": [
                    {
                        "min": 0,
                        "max": 0.0,
                        "AA Code": "TSC",
                        "AA Code Message": "Applied from mixed channels",
                    },
                    {
                        "min": 1,
                        "max": 1.0,
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                ],
                "days_since_last_app_using_phone_same_identity": [
                    {
                        "min": 0,
                        "max": inf,
                        "AA Code": "TRCP",
                        "AA Code Message": "Time since the last Telco application using the same phone is too short",
                    }
                ],
                "min_app_date_gap": [
                    {
                        "min": 0,
                        "max": inf,
                        "AA Code": "TFQ",
                        "AA Code Message": "The gap between Telco applications is too short",
                    }
                ],
                "same_phone_as_last_app": [
                    {
                        "min": 0,
                        "max": 0.0,
                        "AA Code": "TMP",
                        "AA Code Message": "Number of phones ever used in Telco applications is too high",
                    },
                    {
                        "min": 1,
                        "max": 1.0,
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                ],
            },
        )

        D_aa_fdn_attr_default = defaultdict(
            list,
            {
                "same_email_as_last_app": [
                    {
                        "default_value": [-9999],
                        "AA Code": "TX0",
                        "AA Code Message": "Insufficient information about the applicant",
                    },
                    {
                        "default_value": [-2],
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                    {
                        "default_value": [-1],
                        "AA Code": "TX1",
                        "AA Code Message": "No past Telco application",
                    },
                ],
                "num_sales_channel": [
                    {
                        "default_value": [-9999],
                        "AA Code": "TX0",
                        "AA Code Message": "Insufficient information about the applicant",
                    },
                    {
                        "default_value": [-1],
                        "AA Code": "TX1",
                        "AA Code Message": "No past Telco application",
                    },
                    {
                        "default_value": [0],
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                ],
                "num_email": [
                    {
                        "default_value": [-9999],
                        "AA Code": "TX0",
                        "AA Code Message": "Insufficient information about the applicant",
                    },
                    {
                        "default_value": [-2],
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                    {
                        "default_value": [-1],
                        "AA Code": "TX1",
                        "AA Code Message": "No past Telco application",
                    },
                ],
                "num_app_using_ssn_same_identity": [
                    {
                        "default_value": [-9999],
                        "AA Code": "TX0",
                        "AA Code Message": "Insufficient information about the applicant",
                    },
                    {
                        "default_value": [-2],
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                    {
                        "default_value": [-1],
                        "AA Code": "TX1",
                        "AA Code Message": "No past Telco application",
                    },
                ],
                "same_addr_as_last_app": [
                    {
                        "default_value": [-9999],
                        "AA Code": "TX0",
                        "AA Code Message": "Insufficient information about the applicant",
                    },
                    {
                        "default_value": [-2],
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                    {
                        "default_value": [-1],
                        "AA Code": "TX1",
                        "AA Code Message": "No past Telco application",
                    },
                ],
                "num_app_using_addr_same_identity": [
                    {
                        "default_value": [-9999],
                        "AA Code": "TX0",
                        "AA Code Message": "Insufficient information about the applicant",
                    },
                    {
                        "default_value": [-2],
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                    {
                        "default_value": [-1],
                        "AA Code": "TX1",
                        "AA Code Message": "No past Telco application",
                    },
                ],
                "days_since_first_app": [
                    {
                        "default_value": [-9999],
                        "AA Code": "TX0",
                        "AA Code Message": "Insufficient information about the applicant",
                    },
                    {
                        "default_value": [-1],
                        "AA Code": "TX1",
                        "AA Code Message": "No past Telco application",
                    },
                ],
                "days_since_last_app_with_another_client": [
                    {
                        "default_value": [-9999],
                        "AA Code": "TX0",
                        "AA Code Message": "Insufficient information about the applicant",
                    },
                    {
                        "default_value": [-2],
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                    {
                        "default_value": [-1],
                        "AA Code": "TX1",
                        "AA Code Message": "No past Telco application",
                    },
                ],
                "num_app_using_email_same_identity": [
                    {
                        "default_value": [-9999],
                        "AA Code": "TX0",
                        "AA Code Message": "Insufficient information about the applicant",
                    },
                    {
                        "default_value": [-2],
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                    {
                        "default_value": [-1],
                        "AA Code": "TX1",
                        "AA Code Message": "No past Telco application",
                    },
                ],
                "days_since_last_app_using_addr_same_identity": [
                    {
                        "default_value": [-9999],
                        "AA Code": "TX0",
                        "AA Code Message": "Insufficient information about the applicant",
                    },
                    {
                        "default_value": [-2, -3],
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                    {
                        "default_value": [-1],
                        "AA Code": "TX1",
                        "AA Code Message": "No past Telco application",
                    },
                ],
                "num_id_type": [
                    {
                        "default_value": [-9999],
                        "AA Code": "TX0",
                        "AA Code Message": "Insufficient information about the applicant",
                    },
                    {
                        "default_value": [-1],
                        "AA Code": "TX1",
                        "AA Code Message": "No past Telco application",
                    },
                    {
                        "default_value": [0],
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                ],
                "days_since_last_app_using_ssn_same_identity": [
                    {
                        "default_value": [-9999],
                        "AA Code": "TX0",
                        "AA Code Message": "Insufficient information about the applicant",
                    },
                    {
                        "default_value": [-2, -3],
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                    {
                        "default_value": [-1],
                        "AA Code": "TX1",
                        "AA Code Message": "No past Telco application",
                    },
                ],
                "num_phone": [
                    {
                        "default_value": [-9999],
                        "AA Code": "TX0",
                        "AA Code Message": "Insufficient information about the applicant",
                    },
                    {
                        "default_value": [-2],
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                    {
                        "default_value": [-1],
                        "AA Code": "TX1",
                        "AA Code Message": "No past Telco application",
                    },
                ],
                "days_since_last_app_using_email_same_identity": [
                    {
                        "default_value": [-9999],
                        "AA Code": "TX0",
                        "AA Code Message": "Insufficient information about the applicant",
                    },
                    {
                        "default_value": [-2, -3],
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                    {
                        "default_value": [-1],
                        "AA Code": "TX1",
                        "AA Code Message": "No past Telco application",
                    },
                ],
                "same_sales_channel_with_last_app": [
                    {
                        "default_value": [-9999],
                        "AA Code": "TX0",
                        "AA Code Message": "Insufficient information about the applicant",
                    },
                    {
                        "default_value": [-2],
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                    {
                        "default_value": [-1],
                        "AA Code": "TX1",
                        "AA Code Message": "No past Telco application",
                    },
                ],
                "days_since_last_app_using_phone_same_identity": [
                    {
                        "default_value": [-9999],
                        "AA Code": "TX0",
                        "AA Code Message": "Insufficient information about the applicant",
                    },
                    {
                        "default_value": [-2, -3],
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                    {
                        "default_value": [-1],
                        "AA Code": "TX1",
                        "AA Code Message": "No past Telco application",
                    },
                ],
                "min_app_date_gap": [
                    {
                        "default_value": [-9999],
                        "AA Code": "TX0",
                        "AA Code Message": "Insufficient information about the applicant",
                    },
                    {
                        "default_value": [-1],
                        "AA Code": "TX1",
                        "AA Code Message": "No past Telco application",
                    },
                ],
                "same_phone_as_last_app": [
                    {
                        "default_value": [-9999],
                        "AA Code": "TX0",
                        "AA Code Message": "Insufficient information about the applicant",
                    },
                    {
                        "default_value": [-2],
                        "AA Code": "TAP",
                        "AA Code Message": "Number of past Telco applications is too high",
                    },
                    {
                        "default_value": [-1],
                        "AA Code": "TX1",
                        "AA Code Message": "No past Telco application",
                    },
                ],
            },
        )

        D_alignment_coeff = {
            "fpf": {
                "low": {"slope": 1, "intercept": 0},
                "medium": {"slope": 1.12108436, "intercept": -0.23994365},
                "high": {"slope": 1.21447796, "intercept": -0.23994365},
            },
            "fpd": {
                "low": {"slope": 1, "intercept": 0},
                "medium": {"slope": 1.04342949, "intercept": -0.03013516},
                "high": {"slope": 1.02344982, "intercept": -0.03035157},
            },
            "tpf": {
                "low": {"slope": 1, "intercept": 0},
                "medium": {"slope": 0.97024256, "intercept": -0.05155249},
                "high": {"slope": 1.01060963, "intercept": 0.05519076},
            },
        }

        def scale_score(score):
            if isnan(score):
                return ""
            return round((1 / (1 + exp(score))) * 999.0)

        def aggregate_aa_code(row):
            """
            Calculate AA code and message for a single record.
            input:
                + row: a dictionary-like object with all attributes and their contribution scores, named like {attr} and {attr}_contribution
            """
            logger = logging.getLogger(__name__)
            logger.setLevel(logging.INFO)
            logger.debug("Calculating AA codes for %s", row)

            agg_aa_score = defaultdict(float)
            attr_list = [
                col for col in row if col in constraints and col != "sales_channel"
            ]

            ## loop through all the attributes and accumulate contributions for each different AA code
            for attr in attr_list:

                ## check if attr is default value
                constraints_info = constraints[attr]
                is_default = (row[attr] < constraints_info[0]) or (
                    row[attr] > constraints_info[1]
                )

                if attr.startswith("p13_") or attr.startswith("t11_"):
                    # if it is a credit attribute
                    attr_abbr = attr
                    if not is_default:
                        code = D_aa_credit_attr[attr]["AA Code"]
                    else:
                        code = D_aa_credit_attr[attr]["Default AA Code"]
                else:
                    # if it is a Telco attribute
                    attr_abbr = "_".join(
                        attr.replace("_with_client", "").split("_")[:-1]
                    )
                    if not is_default:
                        for item in D_aa_fdn_attr[attr_abbr]:
                            if row[attr] >= item["min"] and row[attr] <= item["max"]:
                                code = item["AA Code"]
                                break
                    else:
                        for item in D_aa_fdn_attr_default[attr_abbr]:
                            if row[attr] in item["default_value"]:
                                code = item["AA Code"]
                                break

                ## only include attributes that have positive contribution, also explicitly avoid TX1 (No past Telco application) as this is counter-intuitive
                if row[f"{attr}_contribution"] > 0 and code != "TX1":
                    agg_aa_score[code] += row[f"{attr}_contribution"]

            ## sort the AA codes by aggregated contribution score
            agg_aa_score_sorted = sorted(
                agg_aa_score.items(), key=lambda item: item[1], reverse=True
            )

            ## return AA codes
            output = ["" for i in range(MAX_NUM_AA_CODE + 1)]
            num_top_aa_code = min(len(agg_aa_score_sorted), MAX_NUM_AA_CODE)
            for i in range(num_top_aa_code):
                output[i] = agg_aa_score_sorted[i][0]
            ## check if E6 code is a candidate, if yes, always include it in the output
            if "E6" in agg_aa_score and "E6" not in output:
                output[num_top_aa_code] = "E6"

            return output


        ## align and scale the raw model score
        alignment_coeff = D_alignment_coeff[self.tag][self.risk_level]

        raw_score_idx = self.score_data_columns.index("raw_score")
        raw_score_col = df_score[:, raw_score_idx]
        raw_score_aligned = (
            raw_score_col * alignment_coeff["slope"] + alignment_coeff["intercept"]
        )
        model_score = np.vectorize(scale_score, otypes=[object])(raw_score_aligned)

        # add model_score as a new column to df_score_hit
        df_score = np.hstack((df_score, model_score.reshape(-1, 1)))

        # update score_data_columns to include 'model_score'
        self.score_data_columns.append("model_score")

        MAX_NUM_AA_CODE = 4
        AA_COLS = [
            f"reason_code{i+1}" for i in range(MAX_NUM_AA_CODE + 1)
        ]  # add an additional spot for 'E6' (too many inquiries) when it has adverse effect

        top_aa_codes = []
        for row in df_contribution:
            row_dict = {col: val for col, val in zip(self.contribution_columns, row)}
            result = aggregate_aa_code(row_dict)
            top_aa_codes.append([row_dict["APP_NUMBER"]] + result)
        df_aa_code = np.array(top_aa_codes, dtype=object)

        ## Prepare output for hit
        app_num_idx = self.score_data_columns.index("APP_NUMBER")
        model_score_idx = self.score_data_columns.index("model_score")

        app_nums = df_score[:, app_num_idx].reshape(-1, 1)
        model_scores = df_score[:, model_score_idx].reshape(-1, 1)
        model_name = "Telco_" + self.tag.split("_")[0].upper()
        model_name_col = np.full_like(app_nums, model_name, dtype=object)

        df_output = np.hstack(
            (app_nums, model_name_col, model_scores)
        )

        aa_code_dict = {app: codes for app, *codes in df_aa_code}
        aa_cols_len = len(AA_COLS)
        aa_data = np.array(
            [
                (aa_code_dict.get(app, [""] * aa_cols_len) + [""] * aa_cols_len)[
                    :aa_cols_len
                ]
                for app in app_nums.flatten()
            ],
            dtype=object,
        )  # retrieve AA data
        df_output = np.hstack((df_output, aa_data))

        return df_output
