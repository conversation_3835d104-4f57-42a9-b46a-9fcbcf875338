import logging
import numpy as np


class Predictor(object):

    # default constructor - does not accept any input parameters
    # but fixed parameter can be used
    def __init__(self):
        name = "Predictor"

    # predict method
    def predict(self, preprocessed_df, model_pkl_obj, columns):
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        logger.debug("Predicting with preprocessed: %s", preprocessed_df)

        def score_contributions(df_score, model, col_idx):

            # import logging
            # logger = logging.getLogger(__name__)
            # logger.setLevel(logging.INFO)

            logger.debug("Generating contributions for: %s", df_score)

            selected_vars = model.get_vars()
            selected_indices = [col_idx[col] for col in selected_vars]

            ## calculate attribute contributions
            model_contributions = model.get_vars_contribution(
                df_score[:, selected_indices].astype(np.float64)
            )

            # combine 'APP_NUMBER', input features and contributions
            df_contribution = np.hstack(
                (
                    df_score[:, [col_idx["APP_NUMBER"]]],
                    df_score[:, selected_indices],
                    model_contributions,
                )
            )

            # contribution column names
            contribution_columns = (
                ["APP_NUMBER"]
                + selected_vars
                + [f"{col}_contribution" for col in selected_vars]
            )

            return df_contribution, contribution_columns

        def score_data(
            model, df, score_name="score", additional_cols=None, only_return_score=False
        ):
            """
            Score the input date using the input model
            """
            # import logging
            # logger = logging.getLogger(__name__)
            # logger.setLevel(logging.INFO)

            logger.debug("Scoring: %s", df)

            if additional_cols is None:
                additional_cols = []

            # get the list of variables
            list_vars = model.get_vars()
            additional_cols_map = {col: i for i, col in enumerate(columns)}
            var_idx = [additional_cols_map[col] for col in list_vars]
            scores = model.predict(df[:, var_idx].astype(np.float64))

            if only_return_score:
                return scores

            # append new score field to the input data
            additional_cols = [col for col in additional_cols if col != score_name]
            selected_idx = [additional_cols_map[c] for c in additional_cols]
            selected_df = df[:, selected_idx]

            # insert raw_score after 'APP_NUMBER'
            scored_df = np.hstack(
                (selected_df[:, :2], scores.reshape(-1, 1), selected_df[:, 2:])
            )

            # score data column names
            score_data_columns = (
                additional_cols[:2] + [score_name] + additional_cols[2:]
            )
            return scored_df, score_data_columns

        # predict
        model_attr = model_pkl_obj.get_vars()

        score, score_data_columns = score_data(
            model_pkl_obj,
            preprocessed_df,
            score_name="raw_score",
            additional_cols=["APP_NUMBER"] + model_attr,
        )

        columns_idx = {col: i for i, col in enumerate(score_data_columns)}
        contributions, contribution_columns = score_contributions(
            score, model_pkl_obj, columns_idx
        )

        # return preprocessed data as well as the score for post-processing need
        return (
            preprocessed_df,
            (score, contributions),
            score_data_columns,
            contribution_columns,
        )
