{"cells": [{"cell_type": "markdown", "id": "fddf5fe0-e30b-4635-8c98-8c4a1926cec2", "metadata": {}, "source": ["# Artifact Collector\n", "derived from https://code.experian.local/projects/CIQ/repos/ascend-ops-model-onboarding/browse/for_client/Artifact-Collector-Model-Containing-Python-Model-Object-Has-Postprocessor.ipynb?at=master"]}, {"cell_type": "code", "id": "d9c6d82086da5696", "metadata": {}, "source": ["# fpd, fpf, or tpf\n", "TAG = ''\n", "# high, medium, or low\n", "RISK_LEVEL = ''\n", "\n", "# this depends on your <PERSON><PERSON><PERSON> deployment, you may need to change this\n", "PROJECT_ROOT_DIR = '/opt/art-gen'\n", "\n", "# this depeends on the location of your model development files\n", "DATA_ROOT_DIR = f'{PROJECT_ROOT_DIR}/test_data_202504'\n", "\n", "DEPLOY_DIR = f'{PROJECT_ROOT_DIR}/deploy/{TAG}_tag_{RISK_LEVEL}_risk'\n", "\n", "# TODO take from model_output_features.py\n", "output_columns = ['APP_NUMBER', 'model_name', 'model_score', 'reason_code1', 'reason_code2', 'reason_code3', 'reason_code4', 'reason_code5']\n", "\n", "# TODO remove this function and its usage when models with fcra prefix are ready\n", "def remove_fcra_prefix(attr_list):\n", "    return [attr[5:] if attr.startswith('fcra_') else attr for attr in attr_list]\n"], "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["# temporary expected outputs cleanup\n", "import pandas as pd\n", "temp_df = pd.read_csv(f'{DATA_ROOT_DIR}/outputs/ascend_ops_model_output_sample.{TAG}_tag.{RISK_LEVEL}_risk.csv')\n", "# remove rows  with response_code == 'No consumer record found'\n", "temp_df = temp_df[temp_df['response_code'] != 'No consumer record found']\n", "# remove the 'response_code' column\n", "temp_df = temp_df.drop(columns=['response_code'])\n", "# convert 'model_score' column to integer\n", "temp_df['model_score'] = temp_df['model_score'].astype('Int64')\n", "temp_df.to_csv(f'{DATA_ROOT_DIR}/outputs/ascend_ops_model_output_sample.{TAG}_tag.{RISK_LEVEL}_risk.hits-only.csv', index=False)"], "id": "3383dedfbf443645", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["import sys\n", "sys.path.append(PROJECT_ROOT_DIR)\n", "from ial import get_input_attribute_list\n", "from util import extract_class_dynamic\n", "# remove DATA_ROOT_DIR\n", "sys.path.pop()"], "id": "cca61a4939c7bc69", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["# load input attribute list\n", "input_attribute_list = get_input_attribute_list(f'{TAG}_{RISK_LEVEL}')\n", "print(input_attribute_list)\n", "\n", "print(remove_fcra_prefix(input_attribute_list))"], "id": "2f2210cad588800f", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# dynamically include Preprocesor, Predictor, and Postprocessor so that they will be included in the AO pickle as classes\n", "Preprocessor = extract_class_dynamic(f'{PROJECT_ROOT_DIR}/preprocessor.py', 'Preprocessor')\n", "Predictor = extract_class_dynamic(f'{PROJECT_ROOT_DIR}/predictor.py', 'Predictor')\n", "Postprocessor = extract_class_dynamic(f'{PROJECT_ROOT_DIR}/postprocessor.py', 'Postprocessor')\n", "# should be just \"<class 'Preprocessor'> <class 'Predictor'> <class 'Postprocessor'>\"\n", "print(Preprocessor, Predictor, Postprocessor)"], "id": "3ed498bf8e265d42"}, {"metadata": {}, "cell_type": "code", "source": ["# load input data\n", "import pandas as pd\n", "# dev_df = pd.read_parquet(f'/work/projects/telco/data/prod_data/ascend_ops/fcra_samples/ascend_ops_model_attr_sample.{TAG}_tag.{RISK_LEVEL}_risk.pq')\n", "dev_df = pd.read_parquet(f'{DATA_ROOT_DIR}/sample_data/ascend_ops_model_attr_sample.{TAG}_tag.{RISK_LEVEL}_risk.pq')"], "id": "3b1b20d7505ee979", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["# temporary data cleanup\n", "dev_df.dropna(subset='opin', inplace=True)"], "id": "73f6d80bba8fdccf", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": "dev_df", "id": "192f1e9b26751fb0", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "markdown", "source": "### Pre-processor", "id": "8a02924446f91b1a"}, {"metadata": {}, "cell_type": "code", "source": ["data = dev_df[remove_fcra_prefix(input_attribute_list)].to_numpy()\n", "preprocessed_df, columns = Preprocessor().preprocess(data, remove_fcra_prefix(input_attribute_list))"], "id": "df343b01e57c76c3", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "markdown", "source": ["### Predictor\n", "In this section you will copy your prediction code into the Predictor class.\n", "\n", "This should contain only score generation code that uses your original pickled model and the pre-processed data already created in the preprocessing step.\n", "\n", "Do not load any pickled object (such as python dictionary) anywhere in this notebook. If you need to use any dictionary/list that you already pickled, you must copy the content of that pickle object explicitly into the predictor class, where applicable.\n", "\n", "Follow inline comments for detailed instructions."], "id": "d0627dd53e9508ba"}, {"metadata": {}, "cell_type": "markdown", "source": "#### Predictor Testing", "id": "8348275cde535c55"}, {"metadata": {}, "cell_type": "markdown", "source": ["##### Load original model object\n", "\n", "If the model object's pickle file already exists, directly read the pickled object, \n", "otherwise use the following code to pickle and read back the object here:\n", "\n"], "id": "62c90732cef77b2e"}, {"metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "cell_type": "code", "source": ["import pickle\n", "\n", "MODEL_PATH = f'{DATA_ROOT_DIR}/models'\n", "suffix = 'dollar_weighted.discount2' if TAG.split('_')[0] != 'tpf' else 'weighted'\n", "gbm_model = pickle.load(open(f'{MODEL_PATH}/consortium_model_202202_202307_three_carriers.{TAG}_tag.fcra_aa.mono.{RISK_LEVEL}_risk_channel.{suffix}.20250318.top50.pkl', 'rb'))"], "id": "6c9f7f86f457be2e", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["# predict to output pre-processed data and score\n", "predicted_preprocess_df, (scored_df, contributions_df), score_data_columns, contribution_columns = Predictor().predict(preprocessed_df, gbm_model, columns)"], "id": "95edcdec808b88c5", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": "predicted_preprocess_df.shape, scored_df.shape, contributions_df.shape", "id": "6c0cba9bff696b49", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["# scored_df\n", "print(scored_df[:5])\n", "print(f\"row: {len(scored_df)}\")\n", "print(f\"columns: {len(scored_df[0])}\")\n", "print(score_data_columns)\n", "print(len(score_data_columns))"], "id": "b62395ee9bd214fa", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": "# scored_df[~scored_df['raw_score'].isna()]['raw_score'].hist(bins=100)", "id": "295dad5ce984dde3", "outputs": [], "execution_count": null}, {"metadata": {"scrolled": true}, "cell_type": "code", "source": ["# contributions_df\n", "print(contributions_df[:5])\n", "print(f\"row: {len(contributions_df)}\")\n", "print(f\"columns: {len(contributions_df[0])}\")\n", "print(contribution_columns)\n", "print(len(contribution_columns))"], "id": "485e4d268294eb25", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "markdown", "source": "### Post-processor", "id": "ff285cf96b303933"}, {"metadata": {}, "cell_type": "code", "source": ["# generate postprocess_input\n", "# postprocess_input = {\n", "    # ,'config': ....\n", "    # ,'mle_class': ....\n", "# }"], "id": "eb4763d288e33773", "outputs": [], "execution_count": null}, {"metadata": {"scrolled": true}, "cell_type": "code", "source": "postprocessed_df = Postprocessor(score_data_columns, contribution_columns, tag=TAG, risk_level=RISK_LEVEL).postprocess((predicted_preprocess_df, (scored_df, contributions_df)))", "id": "4c34d79e98775790", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": "postprocessed_df.shape", "id": "fb3d40616fc5bd93", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["print(postprocessed_df[:5])\n", "print(f\"row: {len(postprocessed_df)}\")\n", "print(f\"columns: {len(postprocessed_df[0])}\")"], "id": "bc22204a94a93cd", "outputs": [], "execution_count": null}, {"metadata": {"scrolled": true}, "cell_type": "code", "source": ["import numpy as np\n", "\n", "def get_value_counts(postprocessed_df, column_names, target_column):\n", "    col_idx = column_names.index(target_column)\n", "    column_data = postprocessed_df[:, col_idx]\n", "    unique, counts = np.unique(column_data, return_counts=True)\n", "    value_counts = {k: int(v) for k, v in zip(unique, counts)}\n", "    sorted_value_counts = dict(sorted(value_counts.items(), key=lambda x: x[1], reverse=True))\n", "    return sorted_value_counts\n", "\n", "# Should be wide distribution as of the 202504 models\n", "print(get_value_counts(postprocessed_df, output_columns, 'reason_code1'))\n", "# Should only be two values: '' and 'E6'\n", "print(get_value_counts(postprocessed_df, output_columns, 'reason_code5'))"], "id": "3c2249eb5070c39", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["for i in range(1, 6):\n", "    col_idx = output_columns.index(f'reason_code{i}')\n", "    print(np.sum(postprocessed_df[:, col_idx] == ''))"], "id": "5050dff9fb72a1b1", "outputs": [], "execution_count": null}, {"cell_type": "code", "id": "e72f426b-296b-4ad9-b583-84c5ad5d93a3", "metadata": {}, "source": "np.savetxt(f'{DATA_ROOT_DIR}/temp_output/ascend_ops_model_output_sample.{TAG}_tag.{RISK_LEVEL}_risk_any_npmd.csv', postprocessed_df, delimiter=',', header=','.join(output_columns), comments='', fmt='%s')", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "bc0c2152-d80e-4aa9-adaa-8ca25cc141e9", "metadata": {}, "source": ["## Required Python Libraries and Versions for Model Execution \n", "\n", "Using the Example library requirement dictionary below, fill-in this dictionary with all the required libraries and corresponding versions for your model"]}, {"cell_type": "code", "id": "98279b2f", "metadata": {}, "source": ["# example library requirement dictionary: fill this in with all the required libraries for your model\n", "# run !pip list in your model development notebook environment to figure out the correct library version\n", "lib_reqs = {\n", "    'cvxopt':'1.3.2',\n", "    'numpy':'2.0.2',\n", "    'pandas':'2.2.3',\n", "    'scikit-learn':'1.6.1'\n", "}"], "outputs": [], "execution_count": null}, {"cell_type": "code", "id": "d791b70ed5ca1909", "metadata": {}, "source": "# !pip list", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "markdown", "source": ["# Artifacts Registration in Ascend Ops\n", "\n", "For pickling, we are using the dill package, Install it before using Ascend Ops to register your model."], "id": "ef2c2af0-e9cb-474e-a28d-1085979a68ce"}, {"cell_type": "code", "id": "d2b4b56f", "metadata": {}, "source": ["# Importing AscendOps class, this assumes the notebook is in the same directory as ascend_ops.py file OR\n", "# the ascend_ops.py file is in the PYTHONPATH of the Jupyter server.\n", "from ascend_ops import AscendOps\n", "\n", "ops = AscendOps('Model')\n", "\n", "# python library setup\n", "ops.register_libraries(lib_reqs)\n", "\n", "# register input attributes  (REQUIRED)\n", "ops.register_input_attributes(attribute_list=input_attribute_list)\n", "\n", "# register preprocessor (REQUIRED)\n", "ops.register_preprocessor(Preprocessor)\n", "\n", "# register predictor (REQUIRED)\n", "ops.register_predictor(Predictor)\n", "\n", "# register model (REQUIRED)\n", "ops.register_model(gbm_model)\n", "\n", "# register postprocessor (REQUIRED)\n", "ops.register_postprocessor(Postprocessor)\n", "\n", "# register postprocessor inputs (OPTIONAL)\n", "ops.register_postprocess_info({\"tag\": TAG, \"risk_level\": RISK_LEVEL})"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "a78b9962-d982-47bb-898b-9c38bdeac865", "metadata": {}, "source": ["## Artifact Saving for Containerization in Ops UI\n", "\n", "This step will generate and save three artifact files that would be used to containerize the model in Ops UI"]}, {"cell_type": "code", "id": "874bc763", "metadata": {}, "source": ["import os\n", "\n", "try:\n", "    os.mkdir(DEPLOY_DIR)\n", "except FileExistsError:\n", "    print(f\"{DEPLOY_DIR} already exists\")\n", "\n", "ops.serialize_model(DEPLOY_DIR, filename='model.pkl')\n", "\n", "# do not use unless python version or dependencies have changed\n", "# ops.generate_docker_reqs(DEPLOY_DIR='', filename='requirements.txt')\n", "\n", "file_path = f'{DEPLOY_DIR}/model_attributes.py'\n", "if os.path.isfile(file_path):\n", "    print(\"model_attributes.py already exists. Make sure you want to replace it!\")\n", "else:\n", "    print(\"Generating new model_attributes.py. Do not forget to add model_output_attributes!\")\n", "    ops.generate_model_attributes(DEPLOY_DIR, filename='model_attributes.py')\n"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "4aa31a4e-dc97-4823-bd48-383d7dd89698", "metadata": {}, "source": ["## Ops Development Testing"]}, {"cell_type": "markdown", "id": "6698b894-321f-49db-bcae-0dd667678d98", "metadata": {}, "source": ["### <PERSON><PERSON> Required\n", "Before testing the artifacts you created, you must restart the kernel.\n", "This is to make sure you only import libraries that are required for the model execution."]}, {"cell_type": "code", "id": "58aac06d", "metadata": {}, "source": ["# import os\n", "# os._exit(00)"], "outputs": [], "execution_count": null}, {"cell_type": "code", "id": "fc063ee7", "metadata": {}, "source": ["# load the Ascend Ops created model pickle using the dill package\n", "# you may need to import other libraries to be able to load this pickle object\n", "import dill\n", "model_object = dill.load(open(f'{DEPLOY_DIR}/model.pkl','rb'))"], "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": ["# should be \"<class 'Preprocessor'> <class 'Predictor'> <class 'Postprocessor'>\"\n", "print(model_object['preprocessor'], model_object['predictor'], model_object['postprocessor'])"], "id": "2b5ec01d74c01857", "outputs": [], "execution_count": null}, {"cell_type": "code", "id": "bff4ac3f661177a2", "metadata": {}, "source": "model_object['model']", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": "model_object['postprocess_info']", "id": "31fce90f8c578ff4", "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": "model_object['input_feature_list']", "id": "3113e81bc3722ca1", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "e99db7b1-7c43-47aa-84c3-fd70ff12cb6e", "metadata": {}, "source": ["### a) Scores test"]}, {"cell_type": "code", "id": "mechanical-robert", "metadata": {}, "source": ["# Reload 10k data\n", "import pandas as pd\n", "# data = pd.read_csv('sample_data_10k.csv')\n", "test_data = pd.read_parquet(f'{DATA_ROOT_DIR}/sample_data/ascend_ops_model_attr_sample.{TAG}_tag.{RISK_LEVEL}_risk.pq')\n", "\n", "# temporary data cleanup\n", "test_data.dropna(subset='opin', inplace=True)"], "outputs": [], "execution_count": null}, {"cell_type": "code", "id": "8525caa478e2edd2", "metadata": {}, "source": ["# run preprocessor\n", "model_input_features = remove_fcra_prefix(model_object['input_feature_list'])\n", "df_preprocessed, df_columns = model_object['preprocessor']().preprocess(test_data[model_input_features].to_numpy(), model_input_features)"], "outputs": [], "execution_count": null}, {"cell_type": "code", "id": "9a1ec7fc094fa4b3", "metadata": {}, "source": ["df_preprocessed"], "outputs": [], "execution_count": null}, {"cell_type": "code", "id": "d8931cbe", "metadata": {}, "source": ["df_columns"], "outputs": [], "execution_count": null}, {"cell_type": "code", "id": "857b0e849243b2a5", "metadata": {}, "source": ["# run predictor\n", "test_preprocessed, (test_score, test_contributions), test_score_data_columns, test_contribution_columns = model_object['predictor']().predict(df_preprocessed, model_object['model'], df_columns)"], "outputs": [], "execution_count": null}, {"cell_type": "code", "id": "422223987cde244b", "metadata": {}, "source": "test_score", "outputs": [], "execution_count": null}, {"cell_type": "code", "id": "3f279bf8", "metadata": {}, "source": ["# run post-processor\n", "info = model_object['postprocess_info']\n", "test_post = (model_object['postprocessor'](\n", "    test_score_data_columns, test_contribution_columns, info['tag'], info['risk_level']\n", ").postprocess((test_preprocessed, (test_score, test_contributions))))"], "outputs": [], "execution_count": null}, {"cell_type": "code", "id": "ab190bfad4193d63", "metadata": {}, "source": ["test_post = pd.DataFrame(test_post, columns=output_columns)\n", "test_post[\"model_score\"] = test_post[\"model_score\"].map(lambda x: None if x == '' else x)\n", "test_post"], "outputs": [], "execution_count": null}, {"cell_type": "code", "id": "fc9e53fe", "metadata": {}, "source": ["test_post = test_post.dropna()\n", "test_post = test_post.copy()\n", "test_post[\"model_score\"] = test_post[\"model_score\"].astype(\"Int64\")\n", "test_post"], "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": "test_post.to_csv(f'{DATA_ROOT_DIR}/outputs/post_test_ascend_ops_model_output_sample.{TAG}_tag.{RISK_LEVEL}_risk.csv', index=False)", "id": "4efedb3071fea988", "outputs": [], "execution_count": null}, {"cell_type": "code", "id": "5ca841aebb349353", "metadata": {}, "source": ["test_post.columns"], "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "13b85b50-a08a-414b-9c42-fdb9384d29da", "metadata": {}, "source": ["### b) Latency test (optional for batch deployment)"]}, {"cell_type": "code", "id": "b02385b6", "metadata": {}, "source": ["# Model execution runtime check for a record\n", "def latency_test():\n", "    latency_test_preprocessed, latency_test_columns = model_object['preprocessor']().preprocess(test_data[model_input_features])\n", "    latency_test_preprocessed, (latency_test_score, latency_test_contributions), latency_test_score_data_columns, latency_test_contribution_columns = model_object['predictor']().predict(latency_test_preprocessed, model_object['model'], latency_test_columns)\n", "    latency_test_post, latency_test_output_cols = model_object['postprocessor'](latency_test_score_data_columns, latency_test_contribution_columns, tag=TAG, risk_level=RISK_LEVEL).postprocess((latency_test_preprocessed, (latency_test_score, latency_test_contributions)))\n", "\n", "%timeit latency_test()"], "outputs": [], "execution_count": null}, {"metadata": {}, "cell_type": "code", "source": "", "id": "b3c208f3811b60fa", "outputs": [], "execution_count": null}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.22"}}, "nbformat": 4, "nbformat_minor": 5}