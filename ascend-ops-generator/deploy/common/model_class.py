import io
import logging
import pandas as pd
import numpy as np

import model_attributes

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


class ModelClass:

    def __init__(self, data, columns, model_obj):
        self.data = data
        self.columns = columns
        self.model_obj = model_obj
        self.model = model_obj["model"]

    def preprocess(self):
        preprocessed_data, preprocessed_columns = self.model_obj[
            "preprocessor"
        ]().preprocess(self.data, self.columns)
        return preprocessed_data, preprocessed_columns

    def predict(self):
        preprocessed_data, preprocessed_columns = self.preprocess()
        (
            preprocessed_data,
            (score, contributions),
            score_columns,
            contribution_columns,
        ) = self.model_obj["predictor"]().predict(
            preprocessed_data, self.model, preprocessed_columns
        )
        return (
            preprocessed_data,
            (score, contributions),
            score_columns,
            contribution_columns,
        )

    def postprocessor(self):
        (
            preprocessed_data,
            (score, contributions),
            score_columns,
            contribution_columns,
        ) = self.predict()
        info = self.model_obj["postprocess_info"]
        postprocess_df = self.model_obj["postprocessor"](
            score_columns, contribution_columns, info['tag'], info['risk_level']
        ).postprocess((preprocessed_data, (score, contributions)))

        return postprocess_df


def predict_func(
    payload,
    content_type,
    model_obj=None,
    input_file_delimiter=",",
    output_file_delimiter=",",
    header=None,
):
    # options for header are 0 (present) and None (not present)

    out_format = (
        pd.DataFrame(model_attributes.model_output_features)
        .sort_values(by="sequence")["name"]
        .tolist()
    )

    if content_type == "json":
        logger.debug("JSON payload: %s", payload)
        payload = pd.DataFrame(payload)
        logger.debug("input data shape " + str(payload.shape))

        if out_format[0] not in payload.columns:
            raise Exception("key name mismatch between payload and model attributes")

        output = actual_predict(model_obj, out_format, payload)

        return output[out_format].to_dict(orient="records")

    elif content_type == "csv":
        # prepend ECK
        col_headers = [out_format[0]] + model_attributes.feature_list
        payload = pd.read_csv(
            io.BytesIO(payload), sep=input_file_delimiter, header=header
        )
        logger.debug("CSV payload: %s", payload)
        payload.columns = col_headers
        logger.debug("input data shape " + str(payload.shape))

        output = actual_predict(model_obj, out_format, payload)

        csv_buffer = io.StringIO()
        output[out_format].to_csv(
            csv_buffer, sep=output_file_delimiter, index=False, header=header
        )
        return csv_buffer.getvalue()
    else:
        raise Exception(f"Unknown content type: {content_type}")


def actual_predict(model_obj, out_format, payload: pd.DataFrame):

    columns = list(payload.columns.str.replace('fcra_', ''))  # TODO: revert fcra prefix removal when model is updated
    data = payload.to_numpy(copy=True)

    output = ModelClass(data, columns, model_obj).postprocessor()
    # prepend ECK
    output = pd.DataFrame(output)
    output = pd.concat([payload[out_format[0]], output], axis=1)
    output.columns = out_format
    return output
