model_output_features = [
    {'name': 'experian_consumer_key', 'description': 'key', 'datatype': 'string', 'sequence': 1},
    {'name': 'APP_NUMBER', 'description': 'additional_data', 'datatype': 'string', 'sequence': 2},
    {'name': 'model_name', 'description': 'additional_data', 'datatype': 'string', 'sequence': 3},
    {'name': 'model_score', 'description': 'score', 'datatype': 'integer', 'sequence': 4},
    {'name': 'reason_code1', 'description': 'additional_data', 'datatype': 'string', 'sequence': 5},
    {'name': 'reason_code2', 'description': 'additional_data', 'datatype': 'string', 'sequence': 6},
    {'name': 'reason_code3', 'description': 'additional_data', 'datatype': 'string', 'sequence': 7},
    {'name': 'reason_code4', 'description': 'additional_data', 'datatype': 'string', 'sequence': 8},
    {'name': 'reason_code5', 'description': 'additional_data', 'datatype': 'string', 'sequence': 9}
]
