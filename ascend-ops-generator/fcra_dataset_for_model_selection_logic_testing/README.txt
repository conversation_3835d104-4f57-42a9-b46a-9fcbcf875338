Objective:
Validate the model selection logic for three fraud types (FPF, NeverPay, and TFP) using sample data.

Files Included:
1. Attribute Sample Files (used as model input):
    - attr_samples_for_FPF_model_selection_logic_testing.parquet
    - attr_samples_for_NeverPay_model_selection_logic_testing.parquet
    - attr_samples_for_TPF_model_selection_logic_testing.parquet

2. Expected Output Files (used for validation):
    - output_for_FPF_model_selection_logic_testing.parquet
    - output_for_NeverPay_model_selection_logic_testing.parquet
    - output_for_TPF_model_selection_logic_testing.parquet

Testing Notes:
1. Each fraud type has its own model selection logic based on the sales channel.
2. Depending on the sales channel, one of three models (low risk, medium risk, or high risk) will be triggered.
3. In total, three model selection logics will be tested (one per fraud type).
4. For each test, use the corresponding pair of attribute and output files. For example, to test the FPF model selection logic:
    - Input: attr_samples_for_FPF_model_selection_logic_testing.parquet
    - Expected Output: output_for_FPF_model_selection_logic_testing.parquet
5. The model scores and reason codes generated using the attributes should match those in the expected output file.