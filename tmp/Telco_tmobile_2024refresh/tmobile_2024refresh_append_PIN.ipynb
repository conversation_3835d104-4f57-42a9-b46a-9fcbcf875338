{"cells": [{"cell_type": "code", "execution_count": 5, "id": "475683ed-fd97-4cf8-9017-d2cd28affb32", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["upload: process-output/final-output/A993540.P993540.E20250804000000_000000002.A.COMPLETED to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/A993540.P993540.E20250804000000_000000002.A.COMPLETED\n", "upload: process-output/final-output/csl/_SUCCESS to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/csl/_SUCCESS\n", "upload: process-output/final-output/csl/part-00000-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/csl/part-00000-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2B (76.1 MiB/s) with 27 file(s) remainingCompleted 41.2 MiB/1.1 GiB (100.9 MiB/s) with 27 file(s) remainingCompleted 52.5 MiB/1.1 GiB (114.0 MiB/s) with 27 file(s) remainingCompleted 67.5 MiB/1.1 GiB (132.2 MiB/s) with 27 file(s) remainingCompleted 76.5 MiB/1.1 GiB (136.4 MiB/s) with 27 file(s) remainingCompleted 86.8 MiB/1.1 GiB (141.6 MiB/s) with 27 file(s) remainingCompleted 98.8 MiB/1.1 GiB (148.3 MiB/s) with 27 file(s) remainingCompleted 111.5 MiB/1.1 GiB (156.4 MiB/s) with 27 file(s) remainingCompleted 125.5 MiB/1.1 GiB (164.0 MiB/s) with 27 file(s) remainingCompleted 139.5 MiB/1.1 GiB (171.4 MiB/s) with 27 file(s) remainingCompleted 152.0 MiB/1.1 GiB (175.3 MiB/s) with 27 file(s) remainingCompleted 163.2 MiB/1.1 GiB (178.0 MiB/s) with 27 file(s) remainingCompleted 172.0 MiB/1.1 GiB (177.8 MiB/s) with 27 file(s) remainingCompleted 183.0 MiB/1.1 GiB (179.6 MiB/s) with 27 file(s) remainingCompleted 193.4 MiB/1.1 GiB (180.9 MiB/s) with 27 file(s) remainingCompleted 208.1 MiB/1.1 GiB (185.9 MiB/s) with 27 file(s) remainingCompleted 219.9 MiB/1.1 GiB (187.9 MiB/s) with 27 file(s) remaining\n", "upload: process-output/final-output/csl/part-00001-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/csl/part-00001-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 MiB/1.1 GiB (194.6 MiB/s) with 26 file(s) remainingCompleted 283.3 MiB/1.1 GiB (198.9 MiB/s) with 26 file(s) remainingCompleted 293.4 MiB/1.1 GiB (198.9 MiB/s) with 26 file(s) remainingCompleted 302.8 MiB/1.1 GiB (197.8 MiB/s) with 26 file(s) remainingCompleted 303.7 MiB/1.1 GiB (192.6 MiB/s) with 26 file(s) remaining\n", "upload: process-output/final-output/csl/part-00003-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/csl/part-00003-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2\n", "upload: process-output/final-output/csl/part-00004-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/csl/part-00004-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2\n", "upload: process-output/final-output/csl/part-00002-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/csl/part-00002-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 MiB/1.1 GiB (192.6 MiB/s) with 23 file(s) remainingCompleted 375.4 MiB/1.1 GiB (194.4 MiB/s) with 23 file(s) remaining\n", "upload: process-output/final-output/csl/part-00005-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/csl/part-00005-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 MiB/1.1 GiB (197.0 MiB/s) with 22 file(s) remainingCompleted 430.4 MiB/1.1 GiB (196.6 MiB/s) with 22 file(s) remainingCompleted 441.4 MiB/1.1 GiB (197.3 MiB/s) with 22 file(s) remainingCompleted 454.2 MiB/1.1 GiB (198.5 MiB/s) with 22 file(s) remainingCompleted 466.3 MiB/1.1 GiB (199.3 MiB/s) with 22 file(s) remainingCompleted 475.8 MiB/1.1 GiB (199.2 MiB/s) with 22 file(s) remainingCompleted 490.6 MiB/1.1 GiB (201.0 MiB/s) with 22 file(s) remainingCompleted 503.8 MiB/1.1 GiB (202.2 MiB/s) with 22 file(s) remainingCompleted 516.6 MiB/1.1 GiB (203.0 MiB/s) with 22 file(s) remaining\n", "upload: process-output/final-output/no_pins_in_dw/993540_B_993540_pinning_file.no_pins_in_dw.manifest to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/no_pins_in_dw/993540_B_993540_pinning_file.no_pins_in_dw.manifest\n", "upload: process-output/final-output/csl/part-00008-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/csl/part-00008-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2\n", "upload: process-output/final-output/no_pins_in_dw/_SUCCESS to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/no_pins_in_dw/_SUCCESS\n", "upload: process-output/final-output/no_pins_in_dw/part-00000-14789ece-bdb6-4fda-8373-b4fb6e10119a-c000.csv.bz2 to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/no_pins_in_dw/part-00000-14789ece-bdb6-4fda-8373-b4fb6e10119a-c000.csv.bz2\n", "upload: process-output/final-output/csl/part-00006-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/csl/part-00006-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2\n", "upload: process-output/final-output/pin_analysis/_SUCCESS to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/pin_analysis/_SUCCESS\n", "upload: process-output/final-output/csl/part-00007-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/csl/part-00007-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2\n", "upload: process-output/final-output/csl/part-00010-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/csl/part-00010-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 MiB/1.1 GiB (203.4 MiB/s) with 14 file(s) remainingCompleted 662.7 MiB/1.1 GiB (203.7 MiB/s) with 14 file(s) remainingCompleted 676.7 MiB/1.1 GiB (204.9 MiB/s) with 14 file(s) remaining\n", "upload: process-output/final-output/csl/part-00009-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/csl/part-00009-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2\n", "upload: process-output/final-output/pinning_results/_SUCCESS to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/pinning_results/_SUCCESSmpleted 722.2 MiB/1.1 GiB (206.0 MiB/s) with 12 file(s) remainingCompleted 735.7 MiB/1.1 GiB (206.9 MiB/s) with 12 file(s) remainingCompleted 748.5 MiB/1.1 GiB (207.5 MiB/s) with 12 file(s) remainingCompleted 764.7 MiB/1.1 GiB (209.0 MiB/s) with 12 file(s) remainingCompleted 777.7 MiB/1.1 GiB (209.7 MiB/s) with 12 file(s) remainingCompleted 788.0 MiB/1.1 GiB (209.6 MiB/s) with 12 file(s) remainingCompleted 795.1 MiB/1.1 GiB (208.6 MiB/s) with 12 file(s) remainingCompleted 808.8 MiB/1.1 GiB (209.4 MiB/s) with 12 file(s) remainingCompleted 823.1 MiB/1.1 GiB (210.4 MiB/s) with 12 file(s) remaining\n", "Completed 830.2 MiB/1.1 GiB (211.0 MiB/s) with 11 file(s) remainingupload: process-output/final-output/pin_analysis/part-00000-9c1258f0-5509-4d26-8649-d50ab39fbcec-c000.csv.bz2 to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/pin_analysis/part-00000-9c1258f0-5509-4d26-8649-d50ab39fbcec-c000.csv.bz2\n", "upload: process-output/final-output/pin_analysis/part-00001-9c1258f0-5509-4d26-8649-d50ab39fbcec-c000.csv.bz2 to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/pin_analysis/part-00001-9c1258f0-5509-4d26-8649-d50ab39fbcec-c000.csv.bz2\n", "upload: process-output/final-output/pin_analysis/part-00002-9c1258f0-5509-4d26-8649-d50ab39fbcec-c000.csv.bz2 to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/pin_analysis/part-00002-9c1258f0-5509-4d26-8649-d50ab39fbcec-c000.csv.bz2B/s) with 9 file(s) remaining\n", "upload: process-output/final-output/pinning_results/part-00000-7d965150-61ad-4ff3-b0ef-e8d54a124cba-c000.csv.bz2 to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/pinning_results/part-00000-7d965150-61ad-4ff3-b0ef-e8d54a124cba-c000.csv.bz2ith 8 file(s) remainingCompleted 969.3 MiB/1.1 GiB (209.7 MiB/s) with 8 file(s) remainingCompleted 982.0 MiB/1.1 GiB (210.0 MiB/s) with 8 file(s) remainingCompleted 992.3 MiB/1.1 GiB (210.0 MiB/s) with 8 file(s) remainingCompleted 1003.8 MiB/1.1 GiB (210.2 MiB/s) with 8 file(s) remainingCompleted 1016.5 MiB/1.1 GiB (210.6 MiB/s) with 8 file(s) remainingCompleted 1.0 GiB/1.1 GiB (210.7 MiB/s) with 8 file(s) remaining   Completed 1.0 GiB/1.1 GiB (210.5 MiB/s) with 8 file(s) remaining   Completed 1.0 GiB/1.1 GiB (210.9 MiB/s) with 8 file(s) remaining   Completed 1.0 GiB/1.1 GiB (211.3 MiB/s) with 8 file(s) remaining   \n", "upload: process-output/final-output/reports/993540_B_993540_pinning_file.pdf to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/reports/993540_B_993540_pinning_file.pdf\n", "Completed 1.1 GiB/1.1 GiB (210.6 MiB/s) with 6 file(s) remainingupload: process-output/final-output/reports/993540_B_993540_pinning_file.csv to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/reports/993540_B_993540_pinning_file.csv\n", "upload: process-output/final-output/valcodes/_SUCCESS to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/valcodes/_SUCCESS\n", "upload: process-output/final-output/reports/993540_B_993540_pinning_file.rtf to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/reports/993540_B_993540_pinning_file.rtf\n", "upload: process-output/final-output/pinning_results/part-00001-7d965150-61ad-4ff3-b0ef-e8d54a124cba-c000.csv.bz2 to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/pinning_results/part-00001-7d965150-61ad-4ff3-b0ef-e8d54a124cba-c000.csv.bz2\n", "upload: process-output/final-output/pinning_results/part-00002-7d965150-61ad-4ff3-b0ef-e8d54a124cba-c000.csv.bz2 to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/pinning_results/part-00002-7d965150-61ad-4ff3-b0ef-e8d54a124cba-c000.csv.bz2\n", "Completed 1.1 GiB/1.1 GiB (207.4 MiB/s) with 1 file(s) remainingCompleted 1.1 GiB/1.1 GiB (206.7 MiB/s) with 1 file(s) remainingupload: process-output/final-output/valcodes/part-00000-d0d2821c-870c-401d-9e86-907d45b37a09-c000.csv.bz2 to s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/valcodes/part-00000-d0d2821c-870c-401d-9e86-907d45b37a09-c000.csv.bz2\n"]}], "source": ["# ! aws s3 cp /work/users/c21868e/Telco_tmobile/process-output s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/ --recursive"]}, {"cell_type": "code", "execution_count": 1, "id": "8d1f3943-54f4-4c57-96a0-3ddb1eeecc32", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3.9.23 | packaged by conda-forge | (main, Jun  4 2025, 17:57:12) \n", "[GCC 13.3.0]\n"]}], "source": ["# Config\n", "import os, sys, findspark\n", "spark_home= '/usr/lib/spark'\n", "findspark.init(spark_home)\n", "  \n", "print(sys.version)"]}, {"cell_type": "code", "execution_count": 2, "id": "88d9fdac-5bfd-4120-9841-865564868c0f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "25/08/05 10:16:58 WARN Utils: Service 'SparkUI' could not bind on port 4040. Attempting port 4041.\n", "25/08/05 10:16:58 WARN Utils: Service 'SparkUI' could not bind on port 4041. Attempting port 4042.\n", "25/08/05 10:16:58 WARN Utils: Service 'SparkUI' could not bind on port 4042. Attempting port 4043.\n", "25/08/05 10:16:58 WARN Utils: Service 'SparkUI' could not bind on port 4043. Attempting port 4044.\n", "25/08/05 10:17:02 WARN Client: Neither spark.yarn.jars nor spark.yarn.archive is set, falling back to uploading libraries under SPARK_HOME.\n", "----------------------------------------\n", "Exception occurred during processing of request from ('127.0.0.1', 42034)\n", "Traceback (most recent call last):\n", "  File \"/home/<USER>/.conda/envs/test/lib/python3.9/socketserver.py\", line 316, in _handle_request_noblock\n", "    self.process_request(request, client_address)\n", "  File \"/home/<USER>/.conda/envs/test/lib/python3.9/socketserver.py\", line 347, in process_request\n", "    self.finish_request(request, client_address)\n", "  File \"/home/<USER>/.conda/envs/test/lib/python3.9/socketserver.py\", line 360, in finish_request\n", "    self.RequestHandlerClass(request, client_address, self)\n", "  File \"/home/<USER>/.conda/envs/test/lib/python3.9/socketserver.py\", line 747, in __init__\n", "    self.handle()\n", "  File \"/usr/lib/spark/python/pyspark/accumulators.py\", line 281, in handle\n", "    poll(accum_updates)\n", "  File \"/usr/lib/spark/python/pyspark/accumulators.py\", line 253, in poll\n", "    if func():\n", "  File \"/usr/lib/spark/python/pyspark/accumulators.py\", line 257, in accum_updates\n", "    num_updates = read_int(self.rfile)\n", "  File \"/usr/lib/spark/python/pyspark/serializers.py\", line 596, in read_int\n", "    raise EOFError\n", "EOFError\n", "----------------------------------------\n"]}], "source": ["os.environ['PYSPARK_PYTHON'] = \"/work/users/c21868e/.conda/envs/test/bin/python\"\n", " \n", "# or use this but you need to shut down kernel:       .set(\"spark.dynamicAllocation.enabled\", \"false\") \\\n", " \n", "from pyspark import SparkConf\n", "from pyspark.sql import SparkSession\n", "sc = SparkConf() \\\n", "     .set(\"spark.app.name\", \"Telco\") \\\n", "     .set(\"spark.executor.memory\", \"24g\") \\\n", "     .set(\"spark.executor.cores\", \"4\") \\\n", "     .set(\"spark.executor.instances\", \"10\") \\\n", "     .set(\"spark.dynamicAllocation.enabled\", \"true\") \\\n", "     .set(\"spark.dynamicAllocation.maxExecutors\", \"20\") \\\n", "     .set(\"spark.driver.maxResultSize\", \"18g\") \\\n", "     .set(\"spark.sql.execution.arrow.enabled\", \"true\") \\\n", "     .set(\"spark.serializer\", \"org.apache.spark.serializer.KryoSerializer\") \\\n", "     .set(\"spark.kryoserializer.buffer.max\", \"512M\")\n", "  \n", "spark = SparkSession.builder.config(conf=sc).getOrCreate()"]}, {"cell_type": "code", "execution_count": 3, "id": "085852c2-0283-4058-8fa8-af2a63f4f903", "metadata": {}, "outputs": [], "source": ["sc = spark.sparkContext\n", "sc.setLogLevel(\"ERROR\")"]}, {"cell_type": "code", "execution_count": 4, "id": "98429c3f-b709-47b7-b367-9836087f44ad", "metadata": {}, "outputs": [], "source": ["from pyspark.sql import Row\n", "from pyspark.sql import functions as F\n", "from pyspark.sql import Window as W\n", "from pyspark.sql import types as T\n", "from pyspark.sql.functions import monotonically_increasing_id, min, max, sha2, conv, expr, lpad, rpad, abs, hash, concat_ws, substring, length, explode, array, array_union, array_distinct, size, flatten, collect_list, udf, count, col, struct, regexp_replace, lit, when, sum, round, avg, desc, concat, countDistinct, expr, coalesce, regexp_extract, min, rand\n", "from pyspark.sql.types import StructType, StructField, StringType, IntegerType, DoubleType"]}, {"cell_type": "markdown", "id": "204b4e9a-8b0c-472b-b636-96cdb2b8eef8", "metadata": {}, "source": ["## Ascend pinning output file"]}, {"cell_type": "code", "execution_count": 6, "id": "41409b91-6e76-412f-a7d5-c4b1d402692e", "metadata": {}, "outputs": [], "source": ["schema = StructType([\n", "    StructField(\"RECORD_NB\", IntegerType(), True),\n", "    StructField(\"PIN\", IntegerType(), True),\n", "    StructField(\"LST_NM\", StringType(), True),\n", "    StructField(\"FST_NM\", StringType(), True),\n", "    StructField(\"MID_NM\", StringType(), True),\n", "    StructField(\"GEN_CD\", StringType(), True),\n", "    StructField(\"PRIMARY_STR_ID\", StringType(), True),\n", "    StructField(\"STR_PRE_DIR\", StringType(), True),\n", "    StructField(\"STREET_NM\", StringType(), True),\n", "    StructField(\"STR_SFX\", StringType(), True),\n", "    StructField(\"STR_PST_DIR\", StringType(), True),\n", "    StructField(\"SSN\", IntegerType(), True),\n", "    StructField(\"STATE_CD\", StringType(), True),\n", "    StructField(\"ZIP_CD\", StringType(), True),\n", "    StructField(\"SCF\", StringType(), True),\n", "    StructField(\"ACCOM_ADDR_TYPE_CD\", StringType(), True),\n", "    StructField(\"ADDR_MISMATCH_IN\", StringType(), True),\n", "    StructField(\"VAL_CAT_CD\", StringType(), True),\n", "    StructField(\"VAL_CD\", StringType(), True),\n", "    StructField(\"DUP_PIN_IN\", StringType(), True)\n", "])"]}, {"cell_type": "code", "execution_count": 7, "id": "56b5502f-5eb3-4df7-98ef-19b5b95a5cce", "metadata": {}, "outputs": [], "source": ["file_loc = \"s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/final-output/pinning_results/*.csv.bz2\"\n", "df_results = spark.read.csv(file_loc, header=False, schema=schema, sep='|')"]}, {"cell_type": "code", "execution_count": 8, "id": "923e959c-baaa-4a0a-89b1-736648f31a5a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"data": {"text/plain": ["9552091"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df_results.count()"]}, {"cell_type": "code", "execution_count": 9, "id": "1e2928ad-3d49-4d2a-9f81-1c352d0ece67", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["+---------+----------+----------+---------+------+------+--------------+-----------+---------------------------+-------+-----------+----+--------+------+---+------------------+----------------+----------+------+----------+\n", "|RECORD_NB|PIN       |LST_NM    |FST_NM   |MID_NM|GEN_CD|PRIMARY_STR_ID|STR_PRE_DIR|STREET_NM                  |STR_SFX|STR_PST_DIR|SSN |STATE_CD|ZIP_CD|SCF|ACCOM_ADDR_TYPE_CD|ADDR_MISMATCH_IN|VAL_CAT_CD|VAL_CD|DUP_PIN_IN|\n", "+---------+----------+----------+---------+------+------+--------------+-----------+---------------------------+-------+-----------+----+--------+------+---+------------------+----------------+----------+------+----------+\n", "|168119079|62308085  |CROOK     |PAMELA   |null  |      |2025          |           |BROWNSBORO                 |RD     |null       |1106|KY      |40206 |402|null              |N               |S         |03    |N         |\n", "|247993916|32331226  |ROBERTSON |SUSAN    |null  |      |136           |           |TERRE MAR                  |DR     |null       |7538|RI      |02852 |028|null              |N               |S         |03    |N         |\n", "|461305181|128872980 |CAMPBELL  |CHERYL   |A     |      |1826          |           |PLANK                      |RD     |null       |9173|NY      |12935 |129|null              |N               |S         |03    |N         |\n", "|770060526|838823986 |LOPEZ     |CESAR    |null  |      |11567         |           |STEWART ROAD TANNER ALABAMA|null   |null       |2529|AL      |35611 |356|null              |N               |S         |03    |Y         |\n", "|946429195|167961384 |JACKSON   |SHIMICA  |null  |      |9609          |           |AUBS                       |LN     |null       |6184|VA      |23060 |230|null              |N               |S         |03    |N         |\n", "|599621139|1090650683|PAYNE     |VONETTA  |null  |      |11559         |           |MEXICO                     |ST     |null       |8849|NY      |11412 |114|null              |N               |S         |03    |N         |\n", "|179871938|1164234235|PLAZA     |ALYSSA   |null  |      |1401          |NE         |23RD                       |CT     |null       |2791|FL      |33064 |330|null              |N               |S         |03    |N         |\n", "|903526513|929873580 |KLOSSKI   |KEITH    |null  |      |217           |           |CHARLES                    |AVE    |SE         |4935|OH      |44646 |446|null              |N               |S         |03    |N         |\n", "|504192604|1216176390|SILVA     |WILLMER  |null  |      |1213          |S          |OCTAGON                    |RD     |null       |9349|NJ      |08104 |081|null              |N               |S         |03    |Y         |\n", "|673621224|1126529214|NELSON    |CHARRISSE|null  |      |180           |           |WORTMAN                    |AVE    |null       |6648|NY      |11207 |112|null              |N               |S         |03    |N         |\n", "|530482074|1092993545|FERGUSON  |ASIA     |null  |      |6912          |           |LACHLAN                    |CIR    |null       |3913|MD      |21239 |212|null              |N               |S         |03    |N         |\n", "|519769692|1211470669|ABDE      |ELBOU    |null  |      |125           |           |MALCOLM X                  |BLVD   |null       |4975|NY      |11221 |112|null              |N               |S         |03    |Y         |\n", "|722373057|1099859083|GOMEZ     |JORGE    |null  |      |190           |N          |CONTOR                     |AVE    |null       |6411|ID      |83401 |834|null              |N               |S         |03    |Y         |\n", "|355183674|159022042 |MCCABE    |JUSTIN   |null  |      |393           |           |GOULD HILL                 |RD     |null       |8592|VT      |05602 |056|null              |N               |S         |03    |N         |\n", "|328898533|152111075 |LEATHERMAN|JESSICA  |null  |      |7517          |           |FINAL TURN                 |DR     |null       |5786|CO      |80549 |805|null              |N               |S         |03    |N         |\n", "|998967110|1128510324|GIBSON    |DASHAWN  |J     |      |145           |           |HARTFORD                   |ST     |null       |6338|VA      |24540 |245|null              |N               |S         |03    |N         |\n", "|445777530|1068409968|RODRIGUEZ |IZAAC    |null  |      |1324          |           |VALLEY VIEW                |DR     |SW         |6022|NM      |87121 |871|null              |N               |S         |03    |N         |\n", "|669812846|30282058  |MARRAFFA  |JOHN     |null  |      |3287          |           |PHEASANT HILLS             |WAY    |null       |7550|NV      |89029 |890|null              |N               |S         |03    |N         |\n", "|998949962|1207220241|ESCALONA  |WIRGEN   |null  |      |6904          |           |GABELLA                    |ST     |null       |437 |MN      |55124 |551|null              |N               |S         |03    |Y         |\n", "|663961465|1215505626|NAVARRO   |ROGER    |null  |      |1145          |           |WHITEHAVEN                 |DR     |null       |3068|TX      |75218 |752|null              |N               |S         |03    |N         |\n", "+---------+----------+----------+---------+------+------+--------------+-----------+---------------------------+-------+-----------+----+--------+------+---+------------------+----------------+----------+------+----------+\n", "only showing top 20 rows\n", "\n"]}], "source": ["df_results.show(truncate=False)"]}, {"cell_type": "code", "execution_count": 10, "id": "8f1b12f9-f11f-4591-a19b-fedaff9bc7e4", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["df = spark.read.option(\"header\", \"true\").csv(\"s3://expn-da-prd-innovationlabs/p/telco/tmobile/raw/app_202401_202412/edlca6717_Experian File New_2024.csv\")"]}, {"cell_type": "code", "execution_count": 13, "id": "d64a02f4-d8f9-4d25-b9af-91fcdb8a7cd6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["df_map = spark.read.parquet(\"s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_files/tmobile_2024refreshed_4ssn_mapping_table\")"]}, {"cell_type": "code", "execution_count": 14, "id": "16276ef8-769b-4654-b775-60c8e7f92257", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"data": {"text/plain": ["[Row(IndividualSeq#=775944704, AppID='20241132006375')]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["df_map.take(1)"]}, {"cell_type": "code", "execution_count": 15, "id": "b6afe8cf-ba50-41a1-a9bc-0fb8ee841b74", "metadata": {}, "outputs": [], "source": ["df_joined = df.select(\"APP_YYYYMM_GMT\", \"APPLICATIONDATE_GMT\", \"APPLICATION_NUMBER\") \\\n", "             .join(df_map, df[\"APPLICATION_NUMBER\"] == df_map[\"AppID\"], how=\"left\")"]}, {"cell_type": "code", "execution_count": 16, "id": "e5701c43-06f7-4946-95c8-c3ce0dd09cdf", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"data": {"text/plain": ["[Row(APP_YYYYMM_GMT='2024-01', APPLICATIONDATE_GMT='2024-01-16 15:38:11.000', APPLICATION_NUMBER='20240162004701', IndividualSeq#=989280512, AppID='20240162004701')]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["df_joined.take(1)"]}, {"cell_type": "code", "execution_count": 18, "id": "25c092c8-7e1f-4b17-a2fc-4570b1cb9905", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["# df_joined.join(df_results.select(\"RECORD_NB\", \"PIN\"), df_joined[\"IndividualSeq#\"] == df_results[\"RECORD_NB\"], how=\"left\") \\\n", "#          .write.mode(\"overwrite\").parquet(\"s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/pinning_analysis/tmobile_refresh_with_PIN\")"]}, {"cell_type": "code", "execution_count": 35, "id": "780e7bad-1948-49f5-9aa1-c2a40f6ce2be", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["df_final = spark.read.parquet(\"s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_results/tmobile_refresh_11665548/pinning_analysis/tmobile_refresh_with_PIN\")"]}, {"cell_type": "code", "execution_count": 20, "id": "6cf6221f-baa2-463f-8f29-1e9ac8a3556f", "metadata": {}, "outputs": [{"data": {"text/plain": ["11665548"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["df_final.count()"]}, {"cell_type": "code", "execution_count": 21, "id": "e1c94919-045b-47b5-b436-fb253d2fdadf", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Row(APP_YYYYMM_GMT='2024-01', APPLICATIONDATE_GMT='2024-01-01 21:45:07.000', APPLICATION_NUMBER='20240013011699', IndividualSeq#=120390846, AppID='20240013011699', RECORD_NB=120390846, PIN=1197986243)]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["df_final.take(1)"]}, {"cell_type": "code", "execution_count": 36, "id": "47984505-62dd-4856-9195-9898d12baef1", "metadata": {}, "outputs": [], "source": ["from pyspark.sql.functions import substring\n", "\n", "df_all = df_final.withColumn(\"app_year\", substring(\"APPLICATIONDATE_GMT\", 1, 4)) \\\n", "                 .withColumn(\"app_month\", substring(\"APPLICATIONDATE_GMT\", 6, 2))"]}, {"cell_type": "code", "execution_count": 37, "id": "78a81f53-b20e-4f62-aa31-b805f8edd3f4", "metadata": {}, "outputs": [], "source": ["df_all = df_all.drop(\"APP_YYYYMM_GMT\", \"APPLICATIONDATE_GMT\", \"IndividualSeq#\", \"AppID\", \"RECORD_NB\")"]}, {"cell_type": "code", "execution_count": 24, "id": "12dd64ac-8709-4d21-bc7a-ed4d2cdd3a9c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"data": {"text/plain": ["[Row(APPLICATION_NUMBER='20240013011699', PIN=1197986243, app_year='2024', app_month='01')]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["df_all.take(1)"]}, {"cell_type": "markdown", "id": "965aa43a-4513-4f36-99e8-615b3af71a65", "metadata": {}, "source": ["## Retro pinning (if needed)"]}, {"cell_type": "code", "execution_count": 25, "id": "9b367050-ae65-452a-b906-036cd9d87486", "metadata": {"jupyter": {"source_hidden": true}}, "outputs": [], "source": ["# Dictionary mapping cluster version to corresponding year, month, max PIN by the end of that month of year.\n", "retro_max_pin = {'v26': ('2022', '04', 1186000000),\n", "                 'v27': ('2022', '05', 1187461690),\n", "                 'v28': ('2022', '06', 1188697016),\n", "                 'v29': ('2022', '07', 1190044176),\n", "                 'v30': ('2022', '08', 1191111493),\n", "                 'v31': ('2022', '09', 1192331915),\n", "                 'v32': ('2022', '10', 1193724553),\n", "                 'v33': ('2022', '11', 1194768017),\n", "                 'v34': ('2022', '12', 1196024945),\n", "                 'v35': ('2023', '01', 1197080963),\n", "                 'v36': ('2023', '02', 1198124013),\n", "                 'v37': ('2023', '03', 1199210007),\n", "                 'v38': ('2023', '04', 1200491881),\n", "                 'v39': ('2023', '05', 1201581557),\n", "                 'v40': ('2023', '06', 1202748976),\n", "                 'v41': ('2023', '07', 1204121661),\n", "                 'v42': ('2023', '08', 1205207732),\n", "                 'v43': ('2023', '09', 1206691150),\n", "                 'v44': ('2023', '10', 1207839617),\n", "                 'v45': ('2023', '11', 1208902787),\n", "                 'v46': ('2023', '12', 1210263589),\n", "                 'v47': ('2024', '01', 1211357937),\n", "                 'v48': ('2024', '02', 1212437302),\n", "                 'v49': ('2024', '03', 1213964842),\n", "                 'v50': ('2024', '04', 1215228186),\n", "                 'v51': ('2024', '05', 1216645860),\n", "                 'v52': ('2024', '06', 1217537233),\n", "                 'v53': ('2024', '07', 1218736954),\n", "                 'v54': ('2024', '08', 1220073569),\n", "                 'v55': ('2024', '09', 1221795239),\n", "                 'v56': ('2024', '10', 1223050024),\n", "                 'v57': ('2024', '11', 1224587692),\n", "                 'v58': ('2024', '12', 1225841896),\n", "                 'v59': ('2025', '01', 1227441967)}"]}, {"cell_type": "code", "execution_count": 26, "id": "71377253-ce98-4aca-a353-4081af773f7f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 35:====================================================> (128 + 3) / 131]"]}, {"name": "stdout", "output_type": "stream", "text": ["+----+-----+-------------+\n", "|year|month|PIN_threshold|\n", "+----+-----+-------------+\n", "|2022|   04|   1186000000|\n", "|2022|   05|   1187461690|\n", "|2022|   06|   1188697016|\n", "|2022|   07|   1190044176|\n", "|2022|   08|   1191111493|\n", "|2022|   09|   1192331915|\n", "|2022|   10|   1193724553|\n", "|2022|   11|   1194768017|\n", "|2022|   12|   1196024945|\n", "|2023|   01|   1197080963|\n", "|2023|   02|   1198124013|\n", "|2023|   03|   1199210007|\n", "|2023|   04|   1200491881|\n", "|2023|   05|   1201581557|\n", "|2023|   06|   1202748976|\n", "|2023|   07|   1204121661|\n", "|2023|   08|   1205207732|\n", "|2023|   09|   1206691150|\n", "|2023|   10|   1207839617|\n", "|2023|   11|   1208902787|\n", "|2023|   12|   1210263589|\n", "|2024|   01|   1211357937|\n", "|2024|   02|   1212437302|\n", "|2024|   03|   1213964842|\n", "|2024|   04|   1215228186|\n", "|2024|   05|   1216645860|\n", "|2024|   06|   1217537233|\n", "|2024|   07|   1218736954|\n", "|2024|   08|   1220073569|\n", "|2024|   09|   1221795239|\n", "|2024|   10|   1223050024|\n", "|2024|   11|   1224587692|\n", "|2024|   12|   1225841896|\n", "|2025|   01|   1227441967|\n", "+----+-----+-------------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["# Define the schema with specified data types\n", "schema = StructType([\n", "    StructField(\"year\", StringType(), True),\n", "    StructField(\"month\", StringType(), True),\n", "    StructField(\"PIN_threshold\", IntegerType(), True)\n", "])\n", "\n", "# Convert dictionary to DataFrame with specified schema\n", "retro_max_pin_df = spark.createDataFrame(\n", "    [(v[0], v[1], v[2]) for k, v in retro_max_pin.items()],\n", "    schema\n", ")\n", "\n", "retro_max_pin_df.show(50)"]}, {"cell_type": "code", "execution_count": 38, "id": "b3d3c7f9-3ffb-4abf-98a4-4cf8433c8637", "metadata": {}, "outputs": [], "source": ["df_all = df_all.join(\n", "    retro_max_pin_df,\n", "    (df_all[\"app_year\"] == retro_max_pin_df[\"year\"]) &\n", "    (df_all[\"app_month\"] == retro_max_pin_df[\"month\"]),\n", "    how=\"left\"\n", ")"]}, {"cell_type": "code", "execution_count": 39, "id": "c43d769d-859a-4d09-833c-b8a21af42c18", "metadata": {}, "outputs": [], "source": ["df_all = df_all.withColumn(\"PIN\", col(\"PIN\").cast(\"int\"))"]}, {"cell_type": "code", "execution_count": 40, "id": "4b06fd49-31e8-4821-9718-67512427f22c", "metadata": {}, "outputs": [], "source": ["df_all = df_all.withColumn(\n", "    \"retro_PIN\",\n", "    when(col(\"PIN\").isNull() | (col(\"PIN\") > col(\"PIN_threshold\")), None).otherwise(col(\"PIN\"))\n", ")"]}, {"cell_type": "code", "execution_count": 41, "id": "c549450e-4c9b-48eb-a6f8-cf494a0de518", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"data": {"text/plain": ["[Row(APPLICATION_NUMBER='20240013011699', PIN=1197986243, app_year='2024', app_month='01', year='2024', month='01', PIN_threshold=1211357937, retro_PIN=1197986243)]"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["df_all.take(1)"]}, {"cell_type": "code", "execution_count": 42, "id": "11305c61-db63-45dd-8692-737355b344e5", "metadata": {}, "outputs": [], "source": ["grouped_df = df_all.groupBy(\"app_year\", \"app_month\").agg(\n", "    count(\"*\").alias(\"total_records\"),\n", "    count(col(\"pin\")).alias(\"total_pinned\"),\n", "    count(col(\"retro_PIN\")).alias(\"total_retro_pinned\"),\n", "    round((count(col(\"pin\")) / count(\"*\")), 4).alias(\"ratio_of_pinned\"),\n", "    round((count(col(\"retro_PIN\")) / count(\"*\")), 4).alias(\"ratio_of_retro_pinned\"),\n", "    round(((count(col(\"retro_PIN\")) / count(\"*\")) - (count(col(\"pin\")) / count(\"*\"))), 4).alias(\"ratio_difference\")\n", ")"]}, {"cell_type": "code", "execution_count": 43, "id": "ba5906ad-5213-4593-be51-cbfd841671c3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 69:==============================================>         (10 + 2) / 12]"]}, {"name": "stdout", "output_type": "stream", "text": ["+--------+---------+-------------+------------+------------------+---------------+---------------------+----------------+\n", "|app_year|app_month|total_records|total_pinned|total_retro_pinned|ratio_of_pinned|ratio_of_retro_pinned|ratio_difference|\n", "+--------+---------+-------------+------------+------------------+---------------+---------------------+----------------+\n", "|    2024|       01|       901190|      741943|            711485|         0.8233|               0.7895|         -0.0338|\n", "|    2024|       02|       922215|      722474|            691982|         0.7834|               0.7503|         -0.0331|\n", "|    2024|       03|       915549|      754121|            724993|         0.8237|               0.7919|         -0.0318|\n", "|    2024|       04|       891953|      736923|            709116|         0.8262|                0.795|         -0.0312|\n", "|    2024|       05|       912877|      753023|            726370|         0.8249|               0.7957|         -0.0292|\n", "|    2024|       06|       930916|      766756|            738760|         0.8237|               0.7936|         -0.0301|\n", "|    2024|       07|       985206|      810706|            782746|         0.8229|               0.7945|         -0.0284|\n", "|    2024|       08|      1016261|      831919|            802942|         0.8186|               0.7901|         -0.0285|\n", "|    2024|       09|      1068844|      880181|            854016|         0.8235|                0.799|         -0.0245|\n", "|    2024|       10|      1016110|      835558|            810768|         0.8223|               0.7979|         -0.0244|\n", "|    2024|       11|      1016294|      831452|            810349|         0.8181|               0.7974|         -0.0208|\n", "|    2024|       12|      1087023|      886122|            865585|         0.8152|               0.7963|         -0.0189|\n", "|    2025|       01|         1110|         913|               908|         0.8225|                0.818|         -0.0045|\n", "+--------+---------+-------------+------------+------------------+---------------+---------------------+----------------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["grouped_df.orderBy(\"app_year\", \"app_month\").show(50)"]}, {"cell_type": "code", "execution_count": 44, "id": "899b62c7-0620-4ebc-9054-9dabcfe968bf", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"data": {"text/plain": ["11665548"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["df_all.count()"]}, {"cell_type": "code", "execution_count": 45, "id": "27459e91-149e-447c-929a-e7e177a8f998", "metadata": {}, "outputs": [], "source": ["df_tm_retro_pin = df_all.select(\n", "    col(\"APPLICATION_NUMBER\").alias(\"app_id\").cast(\"string\"),\n", "    col(\"retro_PIN\").alias(\"pin\").cast(\"long\"),\n", "    concat(col(\"app_year\"), col(\"app_month\")).alias(\"ts\")\n", ")"]}, {"cell_type": "code", "execution_count": 46, "id": "00e1caeb-2ffb-44d8-acae-928fd7a79782", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["root\n", " |-- app_id: string (nullable = true)\n", " |-- pin: long (nullable = true)\n", " |-- ts: string (nullable = true)\n", "\n"]}], "source": ["df_tm_retro_pin.printSchema()"]}, {"cell_type": "code", "execution_count": 47, "id": "e4b44e5e-8b8a-49fa-a092-79d19cb911d8", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 80:=============================================>       (220 + 36) / 256]"]}, {"name": "stdout", "output_type": "stream", "text": ["+--------------+----------+------+\n", "|        app_id|       pin|    ts|\n", "+--------------+----------+------+\n", "|20240022010656| 990927340|202401|\n", "|20240033001481| 162203521|202401|\n", "|20240033002928|  78171234|202401|\n", "|20240033003503|1187486915|202401|\n", "|20240042005114| 652482324|202401|\n", "|20240043017676|  21209935|202401|\n", "|20240053019552| 404524747|202401|\n", "|20240062012796|      null|202401|\n", "|20240102005504|      null|202401|\n", "|20240112000014|      null|202401|\n", "|20240122002413|      null|202401|\n", "|20240123018098|      null|202401|\n", "|20240132015041|      null|202401|\n", "|20240133008798|      null|202401|\n", "|20240143007388|1043830508|202401|\n", "|20240163007623|  29373264|202401|\n", "|20240163007626|1138435916|202401|\n", "|20240173008144|   2672730|202401|\n", "|20240173015907|      null|202401|\n", "|20240182008727| 525180185|202401|\n", "+--------------+----------+------+\n", "only showing top 20 rows\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["df_tm_retro_pin.show()"]}, {"cell_type": "code", "execution_count": 48, "id": "0ff4c6a8-2c03-4f39-9561-de5e851de91e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 90:==========================================>              (9 + 3) / 12]"]}, {"name": "stdout", "output_type": "stream", "text": ["+------+-------+\n", "|    ts|  count|\n", "+------+-------+\n", "|202401| 901190|\n", "|202402| 922215|\n", "|202403| 915549|\n", "|202404| 891953|\n", "|202405| 912877|\n", "|202406| 930916|\n", "|202407| 985206|\n", "|202408|1016261|\n", "|202409|1068844|\n", "|202410|1016110|\n", "|202411|1016294|\n", "|202412|1087023|\n", "|202501|   1110|\n", "+------+-------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["df_tm_retro_pin.groupby(\"ts\").count().orderBy(\"ts\").show(n=50)"]}, {"cell_type": "code", "execution_count": 49, "id": "92c682d4-db3b-4653-a488-17faa921f392", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["# df_tm_retro_pin.write.partitionBy('ts').mode('overwrite') \\\n", "#                 .parquet(\"s3://expn-da-prd-innovationlabs/p/telco/ascend_pinning/appid_pin_ap/tmobile_202401_202412/\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}