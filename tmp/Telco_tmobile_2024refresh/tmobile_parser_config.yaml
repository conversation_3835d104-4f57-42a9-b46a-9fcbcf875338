data_source:
  name: "tmobile"
  type: "id"
  buid_prefix: "TM"

  id_centric_column:
    lookup_type: 'column_name'
    lookup_ref: 'APPLICATION_NUMBER'

description: "Tmobile data parser"
snapshot_date_required: True #required
timestamp_type: "updatedt" #required

frequency:
  keys:
    - first_name
    - middle_name
    - last_name
    - address
    - ssn
    - phone
    - email

raw_file_info:
  - id: "tmobile"
    file_type: "parquet"

pre_process_data_frames:

transformations:
  - id: "tmobile"
    content_record_mappings:
      - column_name: "names"
        mappings:
          - mapping_type: "config"
            required_elements:
              - first_name
              - last_name
            config:
              first_name:
                lookup_type: "column_name"
                lookup_ref: "FIRST_NAME"
              middle_name:
                lookup_type: "column_name"
                lookup_ref: "MIDDLE_NAME"
              last_name:
                lookup_type: "column_name"
                lookup_ref: "LAST_NAME"
              generation_code:
                lookup_type: "column_name"
                lookup_ref: "SUFFIXNAME" 
      - column_name: "addresses"
        mappings:
          - mapping_type: "config"
            required_elements:
              - address
            config:
              address_type:
                lookup_type: "static"
                lookup_ref: "current"
              address_line_1:
                lookup_type: "column_name"
                lookup_ref: "ADRESS_LINE1"
              address_line_2:
                lookup_type: "column_name"
                lookup_ref: "ADRESS_LINE2"
              city_name:
                lookup_type: "column_name"
                lookup_ref: "ADDRESS_CITY"
              state:
                lookup_type: "column_name"
                lookup_ref: "ADDRESS_STATE"
              zip:
                lookup_type: "column_name"
                lookup_ref: "ADDRESS_ZIP"              
      - column_name: "phones"
        mappings:
          - mapping_type: "config"
            config:
              phone_number:
                lookup_type: "column_name"
                lookup_ref: "PHONE_NUMBER"
      - column_name: "emails"
        mappings:
          - mapping_type: "config"
            config:
              emails:
                lookup_type: "column_name"
                lookup_ref: "EMAIL"
      - column_name: "dobs"
        mappings:
          - mapping_type: "config"
            config:
              dob:
                lookup_type: "column_name"
                lookup_ref: "DOB"
                format: "%Y-%m-%d"
      - column_name: "ssns"
        mappings:
          - mapping_type: "config"
            config:
              ssn:
                lookup_type: "column_name"
                lookup_ref: "SSN"

#-------- Post-Process --------

#-------- Post-Process --------
post_process_data_frames:
  post_process_type: "reduce_by_key"
  reduce_by_key:
    deduping_columns: #TODO Rename this to be more meaningful
      - names
      - addresses
      - phones
      - emails
      - dobs
