{"cells": [{"cell_type": "code", "execution_count": 1, "id": "bb33da3f-f5fa-43d3-8f01-ddcf290a319d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3.9.23 | packaged by conda-forge | (main, Jun  4 2025, 17:57:12) \n", "[GCC 13.3.0]\n"]}], "source": ["# Config\n", "import os, sys, findspark\n", "spark_home= '/usr/lib/spark'\n", "findspark.init(spark_home)\n", "  \n", "print(sys.version)"]}, {"cell_type": "code", "execution_count": 2, "id": "0d939544-8296-4797-974f-d740fb57204a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "25/08/05 05:22:48 WARN Utils: Service 'SparkUI' could not bind on port 4040. Attempting port 4041.\n", "25/08/05 05:22:51 WARN Client: Neither spark.yarn.jars nor spark.yarn.archive is set, falling back to uploading libraries under SPARK_HOME.\n"]}], "source": ["os.environ['PYSPARK_PYTHON'] = \"/work/users/c21868e/.conda/envs/test/bin/python\"\n", " \n", "# or use this but you need to shut down kernel:       .set(\"spark.dynamicAllocation.enabled\", \"false\") \\\n", " \n", "from pyspark import SparkConf\n", "from pyspark.sql import SparkSession\n", "sc = SparkConf() \\\n", "     .set(\"spark.app.name\", \"Telco\") \\\n", "     .set(\"spark.executor.memory\", \"24g\") \\\n", "     .set(\"spark.executor.cores\", \"4\") \\\n", "     .set(\"spark.executor.instances\", \"10\") \\\n", "     .set(\"spark.dynamicAllocation.enabled\", \"true\") \\\n", "     .set(\"spark.dynamicAllocation.maxExecutors\", \"20\") \\\n", "     .set(\"spark.driver.maxResultSize\", \"18g\") \\\n", "     .set(\"spark.sql.execution.arrow.enabled\", \"true\") \\\n", "     .set(\"spark.serializer\", \"org.apache.spark.serializer.KryoSerializer\") \\\n", "     .set(\"spark.kryoserializer.buffer.max\", \"512M\")\n", "  \n", "spark = SparkSession.builder.config(conf=sc).getOrCreate()"]}, {"cell_type": "code", "execution_count": 3, "id": "d1b373dc-7f8d-479f-866f-95d7df007756", "metadata": {}, "outputs": [], "source": ["sc = spark.sparkContext\n", "sc.setLogLevel(\"ERROR\")"]}, {"cell_type": "code", "execution_count": 4, "id": "37032058-168c-4260-b855-ab43e4ff0056", "metadata": {}, "outputs": [], "source": ["from pyspark.sql import Row\n", "from pyspark.sql import functions as F\n", "from pyspark.sql import Window as W\n", "from pyspark.sql import types as T\n", "from pyspark.sql.functions import monotonically_increasing_id, min, max, sha2, conv, expr, lpad, rpad, abs, hash, concat_ws, substring, length, explode, array, array_union, array_distinct, size, flatten, collect_list, udf, count, col, struct, regexp_replace, lit, when, sum, round, avg, desc, concat, countDistinct, expr, coalesce, regexp_extract, min, rand\n", "from pyspark.sql.types import StructType, StructField, StringType, IntegerType, DoubleType"]}, {"cell_type": "markdown", "id": "1ffeb556-5e12-4492-b97b-7e70346e0775", "metadata": {}, "source": ["## Prepare pinning file"]}, {"cell_type": "code", "execution_count": 5, "id": "65b0c26d-90af-4df4-b80a-980b06d06806", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["df = spark.read.option(\"header\", \"true\").csv(\"s3://expn-da-prd-innovationlabs/p/telco/tmobile/raw/app_202401_202412/edlca6717_Experian File New_2024.csv\")"]}, {"cell_type": "code", "execution_count": 6, "id": "6e6d2dba-ed07-46c4-a0b3-c6b1144e2b40", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["root\n", " |-- CRID_ENCR: string (nullable = true)\n", " |-- ENTITYTYPE: string (nullable = true)\n", " |-- APP_TYPE: string (nullable = true)\n", " |-- IS_VALID: string (nullable = true)\n", " |-- SUB_APPTYPE: string (nullable = true)\n", " |-- APP_YYYYMM_GMT: string (nullable = true)\n", " |-- APPLICATION_NUMBER: string (nullable = true)\n", " |-- APPLICATIONDATE_GMT: string (nullable = true)\n", " |-- LAST_NAME: string (nullable = true)\n", " |-- FIRST_NAME: string (nullable = true)\n", " |-- MIDDLE_NAME: string (nullable = true)\n", " |-- SUFFIXNAME: string (nullable = true)\n", " |-- ADRESS_LINE1: string (nullable = true)\n", " |-- ADRESS_LINE2: string (nullable = true)\n", " |-- ADDRESS_ZIP: string (nullable = true)\n", " |-- ADDRESS_CITY: string (nullable = true)\n", " |-- ADDRESS_STATE: string (nullable = true)\n", " |-- DOB: string (nullable = true)\n", " |-- SSN: string (nullable = true)\n", " |-- IDENTITY_DOCUMENT_TYPE: string (nullable = true)\n", " |-- IDENTITY_DOCUMENT_NUMBER: string (nullable = true)\n", " |-- PHONE_NUMBER: string (nullable = true)\n", " |-- EMAIL: string (nullable = true)\n", " |-- IP: string (nullable = true)\n", " |-- BYOD: string (nullable = true)\n", " |-- SALES_CHANNEL: string (nullable = true)\n", " |-- SALES_CHANNEL_NAME: string (nullable = true)\n", " |-- SALES_CHANNEL_SEGMENT: string (nullable = true)\n", " |-- STORE_ADDRESS: string (nullable = true)\n", " |-- STORE_CITY: string (nullable = true)\n", " |-- STORE_STATE: string (nullable = true)\n", " |-- STORE_ZIP: string (nullable = true)\n", " |-- MR_FLAG_STRATEGY: string (nullable = true)\n", " |-- MR_FLAG: string (nullable = true)\n", " |-- MR_CLEARED: string (nullable = true)\n", " |-- ACTIVATIONFLAG: string (nullable = true)\n", " |-- HSI_FLAG: string (nullable = true)\n", " |-- ACCOUNT_NUMBER: string (nullable = true)\n", " |-- ACTIVATION_CHANNEL: string (nullable = true)\n", " |-- ACCOUNT_OPEN_DATETIME: string (nullable = true)\n", " |-- NUMBER_OF_LINES: string (nullable = true)\n", " |-- NUM_DEVICE_FINANCED: string (nullable = true)\n", " |-- TOT_FINANCING_AMT: string (nullable = true)\n", " |-- STATUS: string (nullable = true)\n", " |-- FRAUD: string (nullable = true)\n", " |-- FRAUD_TPF: string (nullable = true)\n", " |-- FRAUD_FPF: string (nullable = true)\n", " |-- FRAUD_DATE: string (nullable = true)\n", " |-- FRAUD_WO_AMT: string (nullable = true)\n", " |-- FPD: string (nullable = true)\n", " |-- FPD_DATE: string (nullable = true)\n", " |-- FPD_WO_AMT: string (nullable = true)\n", " |-- OTHER_WO: string (nullable = true)\n", " |-- OTHER_WO_DATE: string (nullable = true)\n", " |-- OTHER_WO_AMT: string (nullable = true)\n", " |-- NO_USAGE_NEW: string (nullable = true)\n", " |-- NO_USAGE_OLD: string (nullable = true)\n", "\n"]}], "source": ["df.printSchema()"]}, {"cell_type": "code", "execution_count": 7, "id": "25ba3f5f-3e3a-4099-844c-43feaaffe7d6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"data": {"text/plain": ["11665548"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df.count()"]}, {"cell_type": "code", "execution_count": 8, "id": "88b00508-cbb3-4a48-8ceb-048b5160adbd", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"data": {"text/plain": ["11665548"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df.select(\"APPLICATION_NUMBER\").distinct().count()"]}, {"cell_type": "code", "execution_count": 9, "id": "ab40310c-2c20-4970-8748-beb87f1a7d24", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 10:================================================>    (232 + 24) / 256]"]}, {"name": "stdout", "output_type": "stream", "text": ["+--------------+--------+\n", "|app_num_length|   count|\n", "+--------------+--------+\n", "|            14|11665548|\n", "+--------------+--------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["df.groupBy(F.length(df[\"APPLICATION_NUMBER\"]).alias(\"app_num_length\")).count().show()"]}, {"cell_type": "code", "execution_count": 10, "id": "585e2f40-9f33-440f-83ba-1764b847f980", "metadata": {}, "outputs": [], "source": ["# select relevant PII fields\n", "df_data = df.select(\"APPLICATION_NUMBER\", \"LAST_NAME\", \"FIRST_NAME\", \"MIDDLE_NAME\", \"SUFFIXNAME\", \n", "                    \"ADRESS_LINE1\", \"ADRESS_LINE2\", \"ADDRESS_ZIP\", \"ADDRESS_CITY\", \"ADDRESS_STATE\", \n", "                    \"PHONE_NUMBER\", \"DOB\", \"SSN\")"]}, {"cell_type": "code", "execution_count": 11, "id": "fc7003ec-7cb9-4d78-8d88-7bd7b99b1627", "metadata": {}, "outputs": [], "source": ["# PII field transformation\n", "df_data = df_data.withColumnRenamed(\"APPLICATION_NUMBER\", \"IndividualSeq#\") \\\n", "                    .withColumnRenamed(\"SUFFIXNAME\", \"Gen Code\") \\\n", "                    .withColumnRenamed(\"ADRESS_LINE1\", \"Current Address 1\") \\\n", "                    .withColumnRenamed(\"ADRESS_LINE2\", \"Current Address 2\") \\\n", "                    .withColumn(\"Current Address 3\", concat(\"ADDRESS_CITY\", lit(\" \"), \"ADDRESS_STATE\", lit(\" \"), \"ADDRESS_ZIP\")) \\\n", "                    .withColumn(\"YOB\", substring(\"DOB\", 1, 4)) \\\n", "                    .withColumnRenamed(\"PHONE_NUMBER\", \"Phone 1\") \\\n", "                    .withColumn(\"Address Count\", lit(1))"]}, {"cell_type": "code", "execution_count": 12, "id": "948c221b-d053-4952-bfba-f68dfe472f32", "metadata": {}, "outputs": [], "source": ["df_data = df_data.withColumn(\n", "    \"Name\",\n", "    when(\n", "        col(\"LAST_NAME\").isNull() & col(\"FIRST_NAME\").isNull() & col(\"MIDDLE_NAME\").isNull(),\n", "        None\n", "    ).otherwise(\n", "        concat_ws(\", \", col(\"LAST_NAME\"), concat_ws(\" \", col(\"FIRST_NAME\"), col(\"MIDDLE_NAME\")))\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": 13, "id": "d9de8b7d-5292-4143-aa2a-cc587928cb12", "metadata": {"jupyter": {"source_hidden": true}}, "outputs": [], "source": ["# Define the schema columns\n", "schema_columns = [\n", "    (\"Control Number\", \"int\"),\n", "    (\"Household Seq\", \"int\"),\n", "    (\"State Code\", \"string\"),\n", "    (\"IndividualSeq#\", \"int\"),\n", "    (\"PSS ID\", \"int\"),\n", "    (\"List Update Type\", \"string\"),\n", "    (\"Run Date\", \"int\"),\n", "    (\"List Source Code\", \"string\"),\n", "    (\"Key Filler\", \"string\"),\n", "    (\"SSN\", \"int\"),\n", "    (\"Gen Code\", \"string\"),\n", "    (\"YOB\", \"int\"),\n", "    (\"Spouse SSN\", \"int\"),\n", "    (\"Phone 1\", \"int\"),\n", "    (\"Phone 2\", \"int\"),\n", "    (\"Phone 3\", \"int\"),\n", "    (\"DL Number\", \"string\"),\n", "    (\"Name\", \"string\"),\n", "    (\"Spouse Name\", \"string\"),\n", "    (\"Address Count\", \"int\"),\n", "    (\"Current Address 1\", \"string\"),\n", "    (\"Current Address 2\", \"string\"),\n", "    (\"Current Address 3\", \"string\"),\n", "    (\"Previous 1 Address 1\", \"string\"),\n", "    (\"Previous 1 Address 2\", \"string\"),\n", "    (\"Previous 1 Address 3\", \"string\"),\n", "    (\"Previous 2 Address 1\", \"string\"),\n", "    (\"Previous 2 Address 2\", \"string\"),\n", "    (\"Previous 2 Address 3\", \"string\"),\n", "    (\"Filler\", \"string\")\n", "]"]}, {"cell_type": "code", "execution_count": 14, "id": "59ef7b26-520a-4d48-919d-2522c9a00081", "metadata": {}, "outputs": [], "source": ["for column_name, data_type in schema_columns:\n", "    if column_name not in df_data.columns:\n", "        df_data = df_data.withColumn(column_name, lit(None).cast(data_type))"]}, {"cell_type": "code", "execution_count": 15, "id": "b7c0922c-1bdf-4ec9-a462-e1990cc368c9", "metadata": {}, "outputs": [], "source": ["selected_columns = [column_name for column_name, _ in schema_columns if column_name in df_data.columns]\n", "df_selected = df_data.select(*selected_columns)"]}, {"cell_type": "code", "execution_count": 16, "id": "505acce5-92a8-4ce4-a539-ba2595e69f23", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["root\n", " |-- Control Number: integer (nullable = true)\n", " |-- Household Seq: integer (nullable = true)\n", " |-- State Code: string (nullable = true)\n", " |-- IndividualSeq#: string (nullable = true)\n", " |-- PSS ID: integer (nullable = true)\n", " |-- List Update Type: string (nullable = true)\n", " |-- Run Date: integer (nullable = true)\n", " |-- List Source Code: string (nullable = true)\n", " |-- Key Filler: string (nullable = true)\n", " |-- SSN: string (nullable = true)\n", " |-- Gen Code: string (nullable = true)\n", " |-- YOB: string (nullable = true)\n", " |-- Spouse SSN: integer (nullable = true)\n", " |-- Phone 1: string (nullable = true)\n", " |-- Phone 2: integer (nullable = true)\n", " |-- Phone 3: integer (nullable = true)\n", " |-- DL Number: string (nullable = true)\n", " |-- Name: string (nullable = true)\n", " |-- Spouse Name: string (nullable = true)\n", " |-- Address Count: integer (nullable = false)\n", " |-- Current Address 1: string (nullable = true)\n", " |-- Current Address 2: string (nullable = true)\n", " |-- Current Address 3: string (nullable = true)\n", " |-- Previous 1 Address 1: string (nullable = true)\n", " |-- Previous 1 Address 2: string (nullable = true)\n", " |-- Previous 1 Address 3: string (nullable = true)\n", " |-- Previous 2 Address 1: string (nullable = true)\n", " |-- Previous 2 Address 2: string (nullable = true)\n", " |-- Previous 2 Address 3: string (nullable = true)\n", " |-- Filler: string (nullable = true)\n", "\n"]}], "source": ["df_selected.printSchema()"]}, {"cell_type": "code", "execution_count": 17, "id": "66851890-1605-40fe-8ca0-19c44a67bfd4", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 13:==================================================>  (243 + 13) / 256]"]}, {"name": "stdout", "output_type": "stream", "text": ["+--------+--------+\n", "|Gen Code|   count|\n", "+--------+--------+\n", "|    null|11662671|\n", "|      II|     162|\n", "|      Jr|    2070|\n", "|     III|     309|\n", "|      Sr|     279|\n", "|      IV|      45|\n", "|     ESQ|       5|\n", "|       V|       5|\n", "|       I|       2|\n", "+--------+--------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["df_selected.groupby(\"Gen Code\").count().show()"]}, {"cell_type": "code", "execution_count": 18, "id": "346da37f-652a-4a15-b02d-bfc9a6053f26", "metadata": {}, "outputs": [], "source": ["# Map Gen Code field\n", "df_data = df_data.withColumn(\"Gen Code\", when(col(\"Gen Code\").isNull(), None) \\\n", "                   .when(col(\"Gen Code\").isin([\"Jr.\", \"Jr\"]), 'J') \\\n", "                   .when(col(\"Gen Code\").isin([\"Sr.\", \"Sr\"]), 'S') \\\n", "                   .when(col(\"Gen Code\") == \"I\", None) \\\n", "                   .when(col(\"Gen Code\") == \"II\", '2') \\\n", "                   .when(col(\"Gen Code\") == \"III\", '3') \\\n", "                   .when(col(\"Gen Code\") == \"IV\", '4') \\\n", "                   .when(col(\"Gen Code\") == \"V\", '5') \\\n", "                   .when(col(\"Gen Code\") == \"VI\", '6') \\\n", "                   .when(col(\"Gen Code\") == \"MD\", None) \\\n", "                   .when(col(\"Gen Code\").isin([\"Esq.\", \"ESQ\"]), None) \\\n", "                   .otherwise(col(\"Gen Code\")))"]}, {"cell_type": "code", "execution_count": 19, "id": "5d0778e9-ab47-47ce-9f30-b53db245963d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 16:====================================================> (247 + 9) / 256]"]}, {"name": "stdout", "output_type": "stream", "text": ["+--------+--------+\n", "|Gen Code|   count|\n", "+--------+--------+\n", "|    null|11662678|\n", "|       S|     279|\n", "|       3|     309|\n", "|       J|    2070|\n", "|       2|     162|\n", "|       4|      45|\n", "|       5|       5|\n", "+--------+--------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["df_data.groupby(\"Gen Code\").count().show()"]}, {"cell_type": "code", "execution_count": 20, "id": "c0999ec6-ec8a-4663-a668-fffa50ee0452", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 19:==================================================>  (242 + 14) / 256]"]}, {"name": "stdout", "output_type": "stream", "text": ["+----------+-------+\n", "|SSN_LENGTH|  count|\n", "+----------+-------+\n", "|      null|  46582|\n", "|         4|6307313|\n", "|         9|5311653|\n", "+----------+-------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["df_selected.groupBy(length(df_selected[\"SSN\"]).alias(\"SSN_LENGTH\")).count().show()"]}, {"cell_type": "code", "execution_count": 21, "id": "a76a6e49-6234-4fa3-873f-66ef6cef2f7e", "metadata": {}, "outputs": [], "source": ["df_selected = df_selected.withColumn(\"SSN\", substring(col(\"SSN\"), -4, 4))"]}, {"cell_type": "code", "execution_count": 22, "id": "fae28e3c-d48f-4018-874b-f8396c88c356", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 22:====================================================> (251 + 5) / 256]"]}, {"name": "stdout", "output_type": "stream", "text": ["+----------+--------+\n", "|SSN_LENGTH|   count|\n", "+----------+--------+\n", "|      null|   46582|\n", "|         4|11618966|\n", "+----------+--------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["df_selected.groupBy(length(df_selected[\"SSN\"]).alias(\"SSN_LENGTH\")).count().show()"]}, {"cell_type": "code", "execution_count": 23, "id": "89b31af8-bdbf-41ad-bf18-8ca9d05bbc14", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["+--------------+-------------+----------+--------------+------+----------------+--------+----------------+----------+----+--------+----+----------+----------+-------+-------+---------+---------------------------+-----------+-------------+------------------------+-----------------+-----------------------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------+\n", "|Control Number|Household Seq|State Code|IndividualSeq#|PSS ID|List Update Type|Run Date|List Source Code|Key Filler|SSN |Gen Code|YOB |Spouse SSN|Phone 1   |Phone 2|Phone 3|DL Number|Name                       |Spouse Name|Address Count|Current Address 1       |Current Address 2|Current Address 3                  |Previous 1 Address 1|Previous 1 Address 2|Previous 1 Address 3|Previous 2 Address 1|Previous 2 Address 2|Previous 2 Address 3|Filler|\n", "+--------------+-------------+----------+--------------+------+----------------+--------+----------------+----------+----+--------+----+----------+----------+-------+-------+---------+---------------------------+-----------+-------------+------------------------+-----------------+-----------------------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------+\n", "|null          |null         |null      |20243312013341|null  |null            |null    |null            |null      |6160|null    |1996|null      |1111111111|null   |null   |null     |RODRIGUEZ, JESUS           |null       |1            |451 W 27TH ST  APT 2R   |null             |CHICAGO Illinois 60616             |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |20242023010310|null  |null            |null    |null            |null      |3013|null    |2003|null      |7867128300|null   |null   |null     |ESPINOZA, SAMUEL           |null       |1            |20109 Sw 88th Ct        |null             |CUTLER BAY Florida 33189           |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |20240672004878|null  |null            |null    |null            |null      |8156|null    |1975|null      |5716530957|null   |null   |null     |CUCA QUINONES, OCTAVIO     |null       |1            |5069 TWO CHIMNEYS CT    |null             |WOODBRIDGE Virginia 22193          |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |20241243007317|null  |null            |null    |null            |null      |5121|null    |1964|null      |9376455993|null   |null   |null     |RICE, ANTHONY              |null       |1            |2847 STATE ROUTE 245    |null             |CABLE Ohio 43009                   |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |20240442016064|null  |null            |null    |null            |null      |9426|null    |1991|null      |1111111111|null   |null   |null     |MORENO CARRION, YORBIELTN  |null       |1            |2035 Joan Dr            |null             |Sarasota Florida 34231             |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |20241932011900|null  |null            |null    |null            |null      |3326|null    |1965|null      |8677685550|null   |null   |null     |<PERSON><PERSON><PERSON>, <PERSON>|null       |1            |257 SCHOOL ST           |null             |LOWELL Massachusetts 01854         |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |20241723014961|null  |null            |null    |null            |null      |3515|null    |1990|null      |3154167835|null   |null   |null     |COOPER, ROSS               |null       |1            |254 TENNYSON AVE        |null             |Syracuse New York 13204            |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |20242442013437|null  |null            |null    |null            |null      |2246|null    |1988|null      |7148251971|null   |null   |null     |Cervantes, Elizabeth       |null       |1            |1931 E Meats ave        |Apt 94           |orange California 92865            |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |20241193006627|null  |null            |null    |null            |null      |2220|null    |1993|null      |5852300697|null   |null   |null     |<PERSON>, <PERSON><PERSON><PERSON>             |null       |1            |537 MEIGS ST            |2                |ROCHESTER New York 14607           |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |20243303009382|null  |null            |null    |null            |null      |5925|null    |1959|null      |null      |null   |null   |null     |<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>            |null       |1            |43132 Hampton Street    |null             |Lancaster California 93536         |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |20243022012379|null  |null            |null    |null            |null      |8240|null    |1990|null      |null      |null   |null   |null     |Roberson, Lashonda         |null       |1            |2465 morning dew dr     |null             |Little Elm Texas 75068             |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |20241082014303|null  |null            |null    |null            |null      |6187|null    |1982|null      |4845540094|null   |null   |null     |DURAN, YADIRA              |null       |1            |739 PLYMOUTH ST         |null             |Allentown Pennsylvania 18109       |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |20240942000551|null  |null            |null    |null            |null      |2465|null    |1992|null      |6154339129|null   |null   |null     |Schneider, Alexandria      |null       |1            |101 7th St              |null             |Portland Tennessee 37148           |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |20241963007997|null  |null            |null    |null            |null      |8790|null    |1955|null      |1111111111|null   |null   |null     |VAUGHAN, CORDELL           |null       |1            |130 10  107TH  AVE      |null             |SOUTH RICHMOND HILL New York 11419 |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |20240373010640|null  |null            |null    |null            |null      |5777|null    |1973|null      |2061111111|null   |null   |null     |BORRERO MONGE, LUIS        |null       |1            |5056 42ND AVE SW        |null             |Seattle Washington 98136           |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |20241513009402|null  |null            |null    |null            |null      |9332|null    |1974|null      |8286541133|null   |null   |null     |fedarko, carolyn e         |null       |1            |816 32nd st ne          |null             |conover North Carolina 28613       |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |20241762011733|null  |null            |null    |null            |null      |6928|null    |1965|null      |null      |null   |null   |null     |<PERSON>, <PERSON>                |null       |1            |1002 W 4th Street       |null             |Marion Indiana 46952               |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |20243132017337|null  |null            |null    |null            |null      |1680|null    |1973|null      |7046924429|null   |null   |null     |<PERSON>n, DeAnna             |null       |1            |901 N CANSLER ST        |null             |Kings Mountain North Carolina 28086|null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |20240403001805|null  |null            |null    |null            |null      |7952|null    |2000|null      |2058623755|null   |null   |null     |cunningham, michael        |null       |1            |314 SOUTH 18TH ST       |null             |BESSEMER Alabama 35020             |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |20241903017561|null  |null            |null    |null            |null      |6902|null    |2001|null      |8087267163|null   |null   |null     |IGAFO, ARIEZ               |null       |1            |45 1015 KAMAU PL APT 16D|null             |KANEOHE Hawaii 96744               |null                |null                |null                |null                |null                |null                |null  |\n", "+--------------+-------------+----------+--------------+------+----------------+--------+----------------+----------+----+--------+----+----------+----------+-------+-------+---------+---------------------------+-----------+-------------+------------------------+-----------------+-----------------------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------+\n", "only showing top 20 rows\n", "\n"]}], "source": ["df_selected.show(truncate=False)"]}, {"cell_type": "code", "execution_count": 24, "id": "c790a878-8467-43bc-944c-a6c5af7cb96f", "metadata": {}, "outputs": [], "source": ["df_selected = df_selected.withColumnRenamed(\"IndividualSeq#\", \"AppID\")"]}, {"cell_type": "code", "execution_count": 25, "id": "494841fd-81d9-4f05-972a-76f43e7d6f58", "metadata": {}, "outputs": [], "source": ["# Generate unique 9-digit numbers\n", "df_selected = df_selected.withColumn(\"UniqueID\", (monotonically_increasing_id() % 900000000 + 100000000).cast(\"int\"))"]}, {"cell_type": "code", "execution_count": 26, "id": "cc4172db-f660-4ab6-a435-8033f020fd80", "metadata": {}, "outputs": [], "source": ["df_selected = df_selected.withColumnRenamed(\"UniqueID\", \"IndividualSeq#\")"]}, {"cell_type": "code", "execution_count": 27, "id": "021d215a-2a03-4961-ba98-f1fe2c040b47", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 26:>                                                         (0 + 1) / 1]"]}, {"name": "stdout", "output_type": "stream", "text": ["+--------------+\n", "|IndividualSeq#|\n", "+--------------+\n", "|     100000000|\n", "|     100000001|\n", "|     100000002|\n", "|     100000003|\n", "|     100000004|\n", "|     100000005|\n", "|     100000006|\n", "|     100000007|\n", "|     100000008|\n", "|     100000009|\n", "|     100000010|\n", "|     100000011|\n", "|     100000012|\n", "|     100000013|\n", "|     100000014|\n", "|     100000015|\n", "|     100000016|\n", "|     100000017|\n", "|     100000018|\n", "|     100000019|\n", "+--------------+\n", "only showing top 20 rows\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["df_selected.select(\"IndividualSeq#\").show()"]}, {"cell_type": "code", "execution_count": 28, "id": "8b955c3f-2c57-4c25-b81d-bc855cb91596", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 33:=====================================================>  (19 + 1) / 20]"]}, {"name": "stdout", "output_type": "stream", "text": ["+--------------+-----+\n", "|IndividualSeq#|count|\n", "+--------------+-----+\n", "+--------------+-----+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["df_selected.groupBy(\"IndividualSeq#\").count().filter(\"count > 1\").show()"]}, {"cell_type": "code", "execution_count": 29, "id": "5c4d0cc2-f169-49b0-a376-3dd13714d78e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 36:=============================================>       (220 + 36) / 256]"]}, {"name": "stdout", "output_type": "stream", "text": ["+---------+---------+\n", "| MinValue| MaxValue|\n", "+---------+---------+\n", "|100000000|998991619|\n", "+---------+---------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["df_selected.agg(min(col(\"IndividualSeq#\")).alias(\"MinValue\"), max(col(\"IndividualSeq#\")).alias(\"MaxValue\")).show()"]}, {"cell_type": "code", "execution_count": 30, "id": "2e71b9a0-c5f8-4c1b-b93d-c7d5c9782c84", "metadata": {}, "outputs": [], "source": ["df_IndividualSeq2AppID = df_selected.select(\"IndividualSeq#\", \"AppID\")"]}, {"cell_type": "code", "execution_count": 31, "id": "c6dfa1ba-cd3e-46e2-aac9-60a7fc79cb15", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["+--------------+--------------+\n", "|IndividualSeq#|         AppID|\n", "+--------------+--------------+\n", "|     100000000|20243312013341|\n", "|     100000001|20242023010310|\n", "|     100000002|20240672004878|\n", "|     100000003|20241243007317|\n", "|     100000004|20240442016064|\n", "|     100000005|20241932011900|\n", "|     100000006|20241723014961|\n", "|     100000007|20242442013437|\n", "|     100000008|20241193006627|\n", "|     100000009|20243303009382|\n", "|     100000010|20243022012379|\n", "|     100000011|20241082014303|\n", "|     100000012|20240942000551|\n", "|     100000013|20241963007997|\n", "|     100000014|20240373010640|\n", "|     100000015|20241513009402|\n", "|     100000016|20241762011733|\n", "|     100000017|20243132017337|\n", "|     100000018|20240403001805|\n", "|     100000019|20241903017561|\n", "+--------------+--------------+\n", "only showing top 20 rows\n", "\n"]}], "source": ["df_IndividualSeq2AppID.show()"]}, {"cell_type": "code", "execution_count": 32, "id": "064972db-88f2-40e4-a839-6cf40302cdb8", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["# df_IndividualSeq2AppID.write.mode(\"overwrite\").parquet(\"s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_files/tmobile_2024refreshed_4ssn_mapping_table\")"]}, {"cell_type": "code", "execution_count": 41, "id": "50c554d1-ae0f-4ba0-b53f-6d7b643b766f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["root\n", " |-- Control Number: integer (nullable = true)\n", " |-- Household Seq: integer (nullable = true)\n", " |-- State Code: string (nullable = true)\n", " |-- AppID: string (nullable = true)\n", " |-- PSS ID: integer (nullable = true)\n", " |-- List Update Type: string (nullable = true)\n", " |-- Run Date: integer (nullable = true)\n", " |-- List Source Code: string (nullable = true)\n", " |-- Key Filler: string (nullable = true)\n", " |-- SSN: string (nullable = true)\n", " |-- Gen Code: string (nullable = true)\n", " |-- YOB: string (nullable = true)\n", " |-- Spouse SSN: integer (nullable = true)\n", " |-- Phone 1: string (nullable = true)\n", " |-- Phone 2: integer (nullable = true)\n", " |-- Phone 3: integer (nullable = true)\n", " |-- DL Number: string (nullable = true)\n", " |-- Name: string (nullable = true)\n", " |-- Spouse Name: string (nullable = true)\n", " |-- Address Count: integer (nullable = false)\n", " |-- Current Address 1: string (nullable = true)\n", " |-- Current Address 2: string (nullable = true)\n", " |-- Current Address 3: string (nullable = true)\n", " |-- Previous 1 Address 1: string (nullable = true)\n", " |-- Previous 1 Address 2: string (nullable = true)\n", " |-- Previous 1 Address 3: string (nullable = true)\n", " |-- Previous 2 Address 1: string (nullable = true)\n", " |-- Previous 2 Address 2: string (nullable = true)\n", " |-- Previous 2 Address 3: string (nullable = true)\n", " |-- Filler: string (nullable = true)\n", " |-- IndividualSeq#: integer (nullable = true)\n", "\n"]}], "source": ["df_selected.printSchema()"]}, {"cell_type": "code", "execution_count": 43, "id": "f7cf3588-38f7-464c-b8fc-312b5540d171", "metadata": {}, "outputs": [], "source": ["selected_columns = [column_name for column_name, _ in schema_columns]\n", "df_2save = df_selected.select(*selected_columns)"]}, {"cell_type": "code", "execution_count": 44, "id": "393de833-de6e-424e-b92e-5b761917921b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"data": {"text/plain": ["11665548"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["df_2save.count()"]}, {"cell_type": "code", "execution_count": 46, "id": "e4ffc077-ae41-441f-a887-e0b94fcf8f2c", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["+--------------+-------------+----------+--------------+------+----------------+--------+----------------+----------+----+--------+----+----------+----------+-------+-------+---------+---------------------------+-----------+-------------+------------------------+-----------------+-----------------------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------+\n", "|Control Number|Household Seq|State Code|IndividualSeq#|PSS ID|List Update Type|Run Date|List Source Code|Key Filler|SSN |Gen Code|YOB |Spouse SSN|Phone 1   |Phone 2|Phone 3|DL Number|Name                       |Spouse Name|Address Count|Current Address 1       |Current Address 2|Current Address 3                  |Previous 1 Address 1|Previous 1 Address 2|Previous 1 Address 3|Previous 2 Address 1|Previous 2 Address 2|Previous 2 Address 3|Filler|\n", "+--------------+-------------+----------+--------------+------+----------------+--------+----------------+----------+----+--------+----+----------+----------+-------+-------+---------+---------------------------+-----------+-------------+------------------------+-----------------+-----------------------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------+\n", "|null          |null         |null      |100000000     |null  |null            |null    |null            |null      |6160|null    |1996|null      |1111111111|null   |null   |null     |RODRIGUEZ, JESUS           |null       |1            |451 W 27TH ST  APT 2R   |null             |CHICAGO Illinois 60616             |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |100000001     |null  |null            |null    |null            |null      |3013|null    |2003|null      |7867128300|null   |null   |null     |ESPINOZA, SAMUEL           |null       |1            |20109 Sw 88th Ct        |null             |CUTLER BAY Florida 33189           |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |100000002     |null  |null            |null    |null            |null      |8156|null    |1975|null      |5716530957|null   |null   |null     |CUCA QUINONES, OCTAVIO     |null       |1            |5069 TWO CHIMNEYS CT    |null             |WOODBRIDGE Virginia 22193          |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |100000003     |null  |null            |null    |null            |null      |5121|null    |1964|null      |9376455993|null   |null   |null     |RICE, ANTHONY              |null       |1            |2847 STATE ROUTE 245    |null             |CABLE Ohio 43009                   |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |100000004     |null  |null            |null    |null            |null      |9426|null    |1991|null      |1111111111|null   |null   |null     |MORENO CARRION, YORBIELTN  |null       |1            |2035 Joan Dr            |null             |Sarasota Florida 34231             |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |100000005     |null  |null            |null    |null            |null      |3326|null    |1965|null      |8677685550|null   |null   |null     |<PERSON><PERSON><PERSON>, <PERSON>|null       |1            |257 SCHOOL ST           |null             |LOWELL Massachusetts 01854         |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |100000006     |null  |null            |null    |null            |null      |3515|null    |1990|null      |3154167835|null   |null   |null     |COOPER, ROSS               |null       |1            |254 TENNYSON AVE        |null             |Syracuse New York 13204            |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |100000007     |null  |null            |null    |null            |null      |2246|null    |1988|null      |7148251971|null   |null   |null     |Cervantes, Elizabeth       |null       |1            |1931 E Meats ave        |Apt 94           |orange California 92865            |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |100000008     |null  |null            |null    |null            |null      |2220|null    |1993|null      |5852300697|null   |null   |null     |<PERSON>, <PERSON><PERSON><PERSON>             |null       |1            |537 MEIGS ST            |2                |ROCHESTER New York 14607           |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |100000009     |null  |null            |null    |null            |null      |5925|null    |1959|null      |null      |null   |null   |null     |<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>            |null       |1            |43132 Hampton Street    |null             |Lancaster California 93536         |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |100000010     |null  |null            |null    |null            |null      |8240|null    |1990|null      |null      |null   |null   |null     |Roberson, Lashonda         |null       |1            |2465 morning dew dr     |null             |Little Elm Texas 75068             |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |100000011     |null  |null            |null    |null            |null      |6187|null    |1982|null      |4845540094|null   |null   |null     |DURAN, YADIRA              |null       |1            |739 PLYMOUTH ST         |null             |Allentown Pennsylvania 18109       |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |100000012     |null  |null            |null    |null            |null      |2465|null    |1992|null      |6154339129|null   |null   |null     |Schneider, Alexandria      |null       |1            |101 7th St              |null             |Portland Tennessee 37148           |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |100000013     |null  |null            |null    |null            |null      |8790|null    |1955|null      |1111111111|null   |null   |null     |VAUGHAN, CORDELL           |null       |1            |130 10  107TH  AVE      |null             |SOUTH RICHMOND HILL New York 11419 |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |100000014     |null  |null            |null    |null            |null      |5777|null    |1973|null      |2061111111|null   |null   |null     |BORRERO MONGE, LUIS        |null       |1            |5056 42ND AVE SW        |null             |Seattle Washington 98136           |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |100000015     |null  |null            |null    |null            |null      |9332|null    |1974|null      |8286541133|null   |null   |null     |fedarko, carolyn e         |null       |1            |816 32nd st ne          |null             |conover North Carolina 28613       |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |100000016     |null  |null            |null    |null            |null      |6928|null    |1965|null      |null      |null   |null   |null     |<PERSON>, <PERSON>                |null       |1            |1002 W 4th Street       |null             |Marion Indiana 46952               |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |100000017     |null  |null            |null    |null            |null      |1680|null    |1973|null      |7046924429|null   |null   |null     |<PERSON>n, DeAnna             |null       |1            |901 N CANSLER ST        |null             |Kings Mountain North Carolina 28086|null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |100000018     |null  |null            |null    |null            |null      |7952|null    |2000|null      |2058623755|null   |null   |null     |cunningham, michael        |null       |1            |314 SOUTH 18TH ST       |null             |BESSEMER Alabama 35020             |null                |null                |null                |null                |null                |null                |null  |\n", "|null          |null         |null      |100000019     |null  |null            |null    |null            |null      |6902|null    |2001|null      |8087267163|null   |null   |null     |IGAFO, ARIEZ               |null       |1            |45 1015 KAMAU PL APT 16D|null             |KANEOHE Hawaii 96744               |null                |null                |null                |null                |null                |null                |null  |\n", "+--------------+-------------+----------+--------------+------+----------------+--------+----------------+----------+----+--------+----+----------+----------+-------+-------+---------+---------------------------+-----------+-------------+------------------------+-----------------+-----------------------------------+--------------------+--------------------+--------------------+--------------------+--------------------+--------------------+------+\n", "only showing top 20 rows\n", "\n"]}], "source": ["df_2save.show(truncate=False)"]}, {"cell_type": "code", "execution_count": 47, "id": "b605ccd8-c3f7-4a65-8c20-fb6d57a65298", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["# df_2save.coalesce(1).write.option(\"delimiter\", \"|\").option(\"header\", \"false\") \\\n", "#            .csv(\"s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_files/tmobile_refreshed_11665548.csv\", mode=\"overwrite\")"]}, {"cell_type": "markdown", "id": "5a14de4b-08a1-4122-a644-677c047c995e", "metadata": {}, "source": ["## Encryption and file transfer"]}, {"cell_type": "code", "execution_count": 48, "id": "0184fa94-43aa-4bcd-861f-70952005bf49", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-08-05 06:04:33,897 INFO util.S3CredentialsResolverUtils: Loading com.amazon.emr.s3ranger.SecretAgentS3CredentialsResolver class\n", "2025-08-05 06:04:34,131 INFO maintenance.MultipartUploadCleaner: Multipart upload cleanup is now enabled for bucket: expn-da-prd-innovationlabs\n", "Found 2 items\n", "-rw-rw-rw-   1 <EMAIL> domain users          0 2025-08-05 06:04 s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_files/tmobile_refreshed_11665548.csv/_SUCCESS\n", "-rw-rw-rw-   1 <EMAIL> domain users 1326438235 2025-08-05 06:02 s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_files/tmobile_refreshed_11665548.csv/part-00000-582e5c68-31a6-43fb-aeb8-0c61683ffd15-c000.csv\n", "2025-08-05 06:04:35,276 INFO maintenance.MultipartUploadCleaner: Shutting down multipart cleanup service\n"]}], "source": ["! hadoop fs -ls s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_files/tmobile_refreshed_11665548.csv"]}, {"cell_type": "code", "execution_count": 49, "id": "6b9828c3-81fc-41c6-a428-9dfee5930f3f", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Completed 1.2 GiB/1.2 GiB (244.1 MiB/s) with 1 file(s) remaining   pleted 5.5 MiB/1.2 GiB (22.8 MiB/s) with 1 file(s) remaining Completed 19.8 MiB/1.2 GiB (67.9 MiB/s) with 1 file(s) remainingCompleted 35.8 MiB/1.2 GiB (104.9 MiB/s) with 1 file(s) remainingCompleted 50.0 MiB/1.2 GiB (127.8 MiB/s) with 1 file(s) remainingCompleted 64.0 MiB/1.2 GiB (146.0 MiB/s) with 1 file(s) remainingCompleted 77.5 MiB/1.2 GiB (156.3 MiB/s) with 1 file(s) remainingCompleted 87.5 MiB/1.2 GiB (159.9 MiB/s) with 1 file(s) remainingCompleted 100.0 MiB/1.2 GiB (167.7 MiB/s) with 1 file(s) remainingCompleted 112.5 MiB/1.2 GiB (173.7 MiB/s) with 1 file(s) remainingCompleted 125.5 MiB/1.2 GiB (180.0 MiB/s) with 1 file(s) remainingCompleted 138.5 MiB/1.2 GiB (187.0 MiB/s) with 1 file(s) remainingCompleted 153.2 MiB/1.2 GiB (194.1 MiB/s) with 1 file(s) remainingCompleted 169.5 MiB/1.2 GiB (198.3 MiB/s) with 1 file(s) remainingCompleted 180.2 MiB/1.2 GiB (199.8 MiB/s) with 1 file(s) remainingCompleted 195.2 MiB/1.2 GiB (204.4 MiB/s) with 1 file(s) remainingCompleted 210.0 MiB/1.2 GiB (209.4 MiB/s) with 1 file(s) remainingCompleted 221.2 MiB/1.2 GiB (209.6 MiB/s) with 1 file(s) remainingCompleted 236.8 MiB/1.2 GiB (214.0 MiB/s) with 1 file(s) remainingCompleted 249.0 MiB/1.2 GiB (215.9 MiB/s) with 1 file(s) remainingCompleted 264.2 MiB/1.2 GiB (220.7 MiB/s) with 1 file(s) remainingCompleted 275.8 MiB/1.2 GiB (223.5 MiB/s) with 1 file(s) remainingCompleted 291.2 MiB/1.2 GiB (224.5 MiB/s) with 1 file(s) remainingCompleted 304.5 MiB/1.2 GiB (224.0 MiB/s) with 1 file(s) remainingCompleted 317.8 MiB/1.2 GiB (225.6 MiB/s) with 1 file(s) remainingCompleted 332.2 MiB/1.2 GiB (227.7 MiB/s) with 1 file(s) remainingCompleted 348.0 MiB/1.2 GiB (230.7 MiB/s) with 1 file(s) remainingCompleted 351.2 MiB/1.2 GiB (231.1 MiB/s) with 1 file(s) remainingCompleted 360.2 MiB/1.2 GiB (225.0 MiB/s) with 1 file(s) remainingCompleted 377.0 MiB/1.2 GiB (226.4 MiB/s) with 1 file(s) remainingCompleted 389.8 MiB/1.2 GiB (227.1 MiB/s) with 1 file(s) remainingCompleted 401.2 MiB/1.2 GiB (227.1 MiB/s) with 1 file(s) remainingCompleted 418.0 MiB/1.2 GiB (230.2 MiB/s) with 1 file(s) remainingCompleted 433.0 MiB/1.2 GiB (231.9 MiB/s) with 1 file(s) remainingCompleted 446.2 MiB/1.2 GiB (233.8 MiB/s) with 1 file(s) remainingCompleted 457.2 MiB/1.2 GiB (235.3 MiB/s) with 1 file(s) remainingCompleted 475.0 MiB/1.2 GiB (235.2 MiB/s) with 1 file(s) remainingCompleted 485.8 MiB/1.2 GiB (234.6 MiB/s) with 1 file(s) remainingCompleted 500.8 MiB/1.2 GiB (236.0 MiB/s) with 1 file(s) remainingCompleted 515.0 MiB/1.2 GiB (237.2 MiB/s) with 1 file(s) remainingCompleted 527.8 MiB/1.2 GiB (238.3 MiB/s) with 1 file(s) remainingCompleted 544.5 MiB/1.2 GiB (239.5 MiB/s) with 1 file(s) remainingCompleted 556.0 MiB/1.2 GiB (239.2 MiB/s) with 1 file(s) remainingCompleted 570.0 MiB/1.2 GiB (239.9 MiB/s) with 1 file(s) remainingCompleted 582.5 MiB/1.2 GiB (240.2 MiB/s) with 1 file(s) remainingCompleted 594.2 MiB/1.2 GiB (240.8 MiB/s) with 1 file(s) remainingCompleted 610.0 MiB/1.2 GiB (241.3 MiB/s) with 1 file(s) remainingCompleted 623.0 MiB/1.2 GiB (241.8 MiB/s) with 1 file(s) remainingCompleted 636.8 MiB/1.2 GiB (242.0 MiB/s) with 1 file(s) remainingCompleted 648.8 MiB/1.2 GiB (242.0 MiB/s) with 1 file(s) remainingCompleted 659.2 MiB/1.2 GiB (237.1 MiB/s) with 1 file(s) remainingCompleted 659.8 MiB/1.2 GiB (234.6 MiB/s) with 1 file(s) remainingCompleted 664.2 MiB/1.2 GiB (234.4 MiB/s) with 1 file(s) remainingCompleted 676.0 MiB/1.2 GiB (232.7 MiB/s) with 1 file(s) remainingCompleted 683.8 MiB/1.2 GiB (230.7 MiB/s) with 1 file(s) remainingCompleted 690.8 MiB/1.2 GiB (231.4 MiB/s) with 1 file(s) remainingCompleted 700.5 MiB/1.2 GiB (230.7 MiB/s) with 1 file(s) remainingCompleted 712.2 MiB/1.2 GiB (230.8 MiB/s) with 1 file(s) remainingCompleted 728.5 MiB/1.2 GiB (232.4 MiB/s) with 1 file(s) remainingCompleted 742.8 MiB/1.2 GiB (233.0 MiB/s) with 1 file(s) remainingCompleted 755.2 MiB/1.2 GiB (233.2 MiB/s) with 1 file(s) remainingCompleted 771.8 MiB/1.2 GiB (234.5 MiB/s) with 1 file(s) remainingCompleted 780.2 MiB/1.2 GiB (233.7 MiB/s) with 1 file(s) remainingCompleted 794.8 MiB/1.2 GiB (234.9 MiB/s) with 1 file(s) remainingCompleted 810.5 MiB/1.2 GiB (236.4 MiB/s) with 1 file(s) remainingCompleted 823.8 MiB/1.2 GiB (237.6 MiB/s) with 1 file(s) remainingCompleted 840.8 MiB/1.2 GiB (238.2 MiB/s) with 1 file(s) remainingCompleted 853.8 MiB/1.2 GiB (237.3 MiB/s) with 1 file(s) remainingCompleted 864.0 MiB/1.2 GiB (237.0 MiB/s) with 1 file(s) remainingCompleted 878.2 MiB/1.2 GiB (237.9 MiB/s) with 1 file(s) remainingCompleted 894.0 MiB/1.2 GiB (238.9 MiB/s) with 1 file(s) remainingCompleted 912.8 MiB/1.2 GiB (240.4 MiB/s) with 1 file(s) remainingCompleted 925.2 MiB/1.2 GiB (240.8 MiB/s) with 1 file(s) remainingCompleted 937.8 MiB/1.2 GiB (240.6 MiB/s) with 1 file(s) remainingCompleted 953.2 MiB/1.2 GiB (241.3 MiB/s) with 1 file(s) remainingCompleted 968.0 MiB/1.2 GiB (242.0 MiB/s) with 1 file(s) remainingCompleted 981.0 MiB/1.2 GiB (242.2 MiB/s) with 1 file(s) remainingCompleted 994.2 MiB/1.2 GiB (242.5 MiB/s) with 1 file(s) remainingCompleted 1007.2 MiB/1.2 GiB (242.6 MiB/s) with 1 file(s) remainingCompleted 1021.5 MiB/1.2 GiB (243.0 MiB/s) with 1 file(s) remainingCompleted 1.0 GiB/1.2 GiB (243.6 MiB/s) with 1 file(s) remaining   Completed 1.0 GiB/1.2 GiB (243.3 MiB/s) with 1 file(s) remaining   Completed 1.0 GiB/1.2 GiB (243.2 MiB/s) with 1 file(s) remaining   Completed 1.0 GiB/1.2 GiB (243.4 MiB/s) with 1 file(s) remaining   Completed 1.1 GiB/1.2 GiB (243.6 MiB/s) with 1 file(s) remaining   Completed 1.1 GiB/1.2 GiB (243.1 MiB/s) with 1 file(s) remaining   Completed 1.1 GiB/1.2 GiB (243.3 MiB/s) with 1 file(s) remaining   Completed 1.1 GiB/1.2 GiB (240.1 MiB/s) with 1 file(s) remaining   Completed 1.1 GiB/1.2 GiB (240.3 MiB/s) with 1 file(s) remaining   Completed 1.1 GiB/1.2 GiB (240.5 MiB/s) with 1 file(s) remaining   Completed 1.1 GiB/1.2 GiB (240.3 MiB/s) with 1 file(s) remaining   Completed 1.1 GiB/1.2 GiB (240.9 MiB/s) with 1 file(s) remaining   Completed 1.2 GiB/1.2 GiB (241.2 MiB/s) with 1 file(s) remaining   Completed 1.2 GiB/1.2 GiB (241.6 MiB/s) with 1 file(s) remaining   Completed 1.2 GiB/1.2 GiB (241.8 MiB/s) with 1 file(s) remaining   Completed 1.2 GiB/1.2 GiB (242.7 MiB/s) with 1 file(s) remaining   Completed 1.2 GiB/1.2 GiB (243.0 MiB/s) with 1 file(s) remaining   Completed 1.2 GiB/1.2 GiB (243.9 MiB/s) with 1 file(s) remaining   download: s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_files/tmobile_refreshed_11665548.csv/part-00000-582e5c68-31a6-43fb-aeb8-0c61683ffd15-c000.csv to ./pinning_file.csv\n"]}], "source": ["! aws s3 cp s3://expn-da-prd-innovationlabs/p/telco/study/ascend_pinning/pinning_files/tmobile_refreshed_11665548.csv/part-00000-582e5c68-31a6-43fb-aeb8-0c61683ffd15-c000.csv /work/users/c21868e/T-Mobile/pinning_file.csv"]}, {"cell_type": "code", "execution_count": 36, "id": "901de1a4-6f95-4379-b012-20d4b603354e", "metadata": {}, "outputs": [], "source": ["# ! gpg --output pinning_file.csv.enc --encrypt --recipient '<EMAIL>' pinning_file.csv"]}, {"cell_type": "code", "execution_count": 50, "id": "bc465c2b-6ce2-47dd-9ab8-38bc45873227", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Completed 498.3 MiB/498.3 MiB (206.8 MiB/s) with 1 file(s) remainingCompleted 1.8 MiB/498.3 MiB (6.3 MiB/s) with 1 file(s) remaining    Completed 8.2 MiB/498.3 MiB (23.2 MiB/s) with 1 file(s) remaining   Completed 14.5 MiB/498.3 MiB (37.9 MiB/s) with 1 file(s) remaining  Completed 26.5 MiB/498.3 MiB (61.2 MiB/s) with 1 file(s) remaining  Completed 40.2 MiB/498.3 MiB (83.2 MiB/s) with 1 file(s) remaining  Completed 52.2 MiB/498.3 MiB (98.7 MiB/s) with 1 file(s) remaining  Completed 65.5 MiB/498.3 MiB (111.9 MiB/s) with 1 file(s) remaining Completed 80.5 MiB/498.3 MiB (126.7 MiB/s) with 1 file(s) remaining Completed 95.5 MiB/498.3 MiB (138.9 MiB/s) with 1 file(s) remaining Completed 97.8 MiB/498.3 MiB (131.2 MiB/s) with 1 file(s) remaining Completed 108.5 MiB/498.3 MiB (136.3 MiB/s) with 1 file(s) remainingCompleted 120.5 MiB/498.3 MiB (143.5 MiB/s) with 1 file(s) remainingCompleted 133.5 MiB/498.3 MiB (149.9 MiB/s) with 1 file(s) remainingCompleted 148.2 MiB/498.3 MiB (157.5 MiB/s) with 1 file(s) remainingCompleted 161.0 MiB/498.3 MiB (162.2 MiB/s) with 1 file(s) remainingCompleted 171.5 MiB/498.3 MiB (164.4 MiB/s) with 1 file(s) remainingCompleted 181.2 MiB/498.3 MiB (165.5 MiB/s) with 1 file(s) remainingCompleted 191.8 MiB/498.3 MiB (167.5 MiB/s) with 1 file(s) remainingCompleted 203.8 MiB/498.3 MiB (170.4 MiB/s) with 1 file(s) remainingCompleted 217.2 MiB/498.3 MiB (174.3 MiB/s) with 1 file(s) remainingCompleted 230.8 MiB/498.3 MiB (177.6 MiB/s) with 1 file(s) remainingCompleted 243.8 MiB/498.3 MiB (180.8 MiB/s) with 1 file(s) remainingCompleted 255.8 MiB/498.3 MiB (182.1 MiB/s) with 1 file(s) remainingCompleted 264.5 MiB/498.3 MiB (182.5 MiB/s) with 1 file(s) remainingCompleted 276.5 MiB/498.3 MiB (184.3 MiB/s) with 1 file(s) remainingCompleted 290.8 MiB/498.3 MiB (187.3 MiB/s) with 1 file(s) remainingCompleted 301.8 MiB/498.3 MiB (188.4 MiB/s) with 1 file(s) remainingCompleted 314.2 MiB/498.3 MiB (189.8 MiB/s) with 1 file(s) remainingCompleted 326.0 MiB/498.3 MiB (191.3 MiB/s) with 1 file(s) remainingCompleted 335.0 MiB/498.3 MiB (191.0 MiB/s) with 1 file(s) remainingCompleted 348.0 MiB/498.3 MiB (192.8 MiB/s) with 1 file(s) remainingCompleted 361.0 MiB/498.3 MiB (194.5 MiB/s) with 1 file(s) remainingCompleted 373.5 MiB/498.3 MiB (196.0 MiB/s) with 1 file(s) remainingCompleted 385.0 MiB/498.3 MiB (196.8 MiB/s) with 1 file(s) remainingCompleted 396.8 MiB/498.3 MiB (197.1 MiB/s) with 1 file(s) remainingCompleted 407.0 MiB/498.3 MiB (197.7 MiB/s) with 1 file(s) remainingCompleted 420.8 MiB/498.3 MiB (199.5 MiB/s) with 1 file(s) remainingCompleted 433.2 MiB/498.3 MiB (200.6 MiB/s) with 1 file(s) remainingCompleted 444.8 MiB/498.3 MiB (201.2 MiB/s) with 1 file(s) remainingCompleted 458.8 MiB/498.3 MiB (202.8 MiB/s) with 1 file(s) remainingCompleted 471.8 MiB/498.3 MiB (204.0 MiB/s) with 1 file(s) remainingCompleted 488.0 MiB/498.3 MiB (206.5 MiB/s) with 1 file(s) remainingupload: ./pinning_file.csv.enc to s3://expn-cis-pinning-prod-s3-bfc-input-enc/A993540.P993540.E20250804000000_000000002.A/pinning_file.csv.enc\n"]}], "source": ["! aws s3 cp pinning_file.csv.enc s3://expn-cis-pinning-prod-s3-bfc-input-enc/A993540.P993540.E20250804000000_000000002.A/pinning_file.csv.enc"]}, {"cell_type": "code", "execution_count": 51, "id": "de484ffc-12ce-4fd2-9366-d99e22a98f81", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["upload: ./manifest.csv to s3://expn-cis-pinning-prod-s3-bfc-input-enc/A993540.P993540.E20250804000000_000000002.A/manifest.csv\n"]}], "source": ["! aws s3 cp manifest.csv s3://expn-cis-pinning-prod-s3-bfc-input-enc/A993540.P993540.E20250804000000_000000002.A/manifest.csv"]}, {"cell_type": "code", "execution_count": 52, "id": "e78b9a6f-e625-4b8f-a26b-fb9c5577d3b5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-08-05 06:10:08        221 manifest.csv\n", "2025-08-05 06:10:02  522545353 pinning_file.csv.enc\n"]}], "source": ["! aws s3 ls s3://expn-cis-pinning-prod-s3-bfc-input-enc/A993540.P993540.E20250804000000_000000002.A/"]}, {"cell_type": "code", "execution_count": 1, "id": "11b8fa46-55e1-49ee-97aa-7eabe831a13a", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["download: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/A993540.P993540.E20250804000000_000000002.A.COMPLETED to ./A993540.P993540.E20250804000000_000000002.A.COMPLETED\n", "Completed 0 Bytes/~830.3 MiB (0 Bytes/s) with ~21 file(s) remaining (calculating...)download: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/A993540.P993540.E20250804000000_000000002.A.COMPLETED to process-output/final-output/A993540.P993540.E20250804000000_000000002.A.COMPLETED\n", "download: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/_SUCCESS_TAGGING to ./_SUCCESS_TAGGING\n", "Completed 0 Bytes/1.1 GiB (0 Bytes/s) with 28 file(s) remainingdownload: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/csl/_SUCCESS to process-output/final-output/csl/_SUCCESS\n", "download: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/csl/part-00001-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to process-output/final-output/csl/part-00001-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2MiB/1.1 GiB (83.2 MiB/s) with 27 file(s) remaining   Completed 45.2 MiB/1.1 GiB (109.3 MiB/s) with 27 file(s) remaining  Completed 60.5 MiB/1.1 GiB (129.0 MiB/s) with 27 file(s) remaining  Completed 71.8 MiB/1.1 GiB (138.9 MiB/s) with 27 file(s) remaining  Completed 83.8 MiB/1.1 GiB (146.5 MiB/s) with 27 file(s) remaining  Completed 95.8 MiB/1.1 GiB (153.7 MiB/s) with 27 file(s) remaining  Completed 104.5 MiB/1.1 GiB (155.3 MiB/s) with 27 file(s) remaining Completed 118.8 MiB/1.1 GiB (165.1 MiB/s) with 27 file(s) remaining Completed 136.2 MiB/1.1 GiB (176.0 MiB/s) with 27 file(s) remaining Completed 151.0 MiB/1.1 GiB (183.6 MiB/s) with 27 file(s) remaining Completed 164.5 MiB/1.1 GiB (187.9 MiB/s) with 27 file(s) remaining Completed 177.5 MiB/1.1 GiB (191.6 MiB/s) with 27 file(s) remaining Completed 189.0 MiB/1.1 GiB (193.3 MiB/s) with 27 file(s) remaining Completed 194.5 MiB/1.1 GiB (189.2 MiB/s) with 27 file(s) remaining Completed 211.0 MiB/1.1 GiB (195.5 MiB/s) with 27 file(s) remaining Completed 227.5 MiB/1.1 GiB (201.7 MiB/s) with 27 file(s) remaining Completed 238.0 MiB/1.1 GiB (202.0 MiB/s) with 27 file(s) remaining Completed 251.2 MiB/1.1 GiB (204.0 MiB/s) with 27 file(s) remaining Completed 261.9 MiB/1.1 GiB (204.3 MiB/s) with 27 file(s) remaining Completed 271.1 MiB/1.1 GiB (203.3 MiB/s) with 27 file(s) remaining Completed 284.2 MiB/1.1 GiB (205.5 MiB/s) with 27 file(s) remaining Completed 299.4 MiB/1.1 GiB (208.7 MiB/s) with 27 file(s) remaining Completed 315.4 MiB/1.1 GiB (212.3 MiB/s) with 27 file(s) remaining \n", "download: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/csl/part-00002-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to process-output/final-output/csl/part-00002-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz27 MiB/1.1 GiB (218.8 MiB/s) with 26 file(s) remainingCompleted 371.2 MiB/1.1 GiB (213.9 MiB/s) with 26 file(s) remainingCompleted 382.2 MiB/1.1 GiB (215.7 MiB/s) with 26 file(s) remaining\n", "download: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/csl/part-00000-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to process-output/final-output/csl/part-00000-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz26 MiB/1.1 GiB (215.9 MiB/s) with 25 file(s) remainingCompleted 443.8 MiB/1.1 GiB (217.2 MiB/s) with 25 file(s) remainingCompleted 458.6 MiB/1.1 GiB (219.1 MiB/s) with 25 file(s) remainingCompleted 472.6 MiB/1.1 GiB (220.3 MiB/s) with 25 file(s) remainingCompleted 486.3 MiB/1.1 GiB (221.4 MiB/s) with 25 file(s) remainingCompleted 497.6 MiB/1.1 GiB (221.4 MiB/s) with 25 file(s) remainingCompleted 510.6 MiB/1.1 GiB (222.3 MiB/s) with 25 file(s) remainingCompleted 522.3 MiB/1.1 GiB (222.4 MiB/s) with 25 file(s) remainingCompleted 534.1 MiB/1.1 GiB (222.5 MiB/s) with 25 file(s) remainingCompleted 535.1 MiB/1.1 GiB (218.2 MiB/s) with 25 file(s) remaining\n", "Completed 539.3 MiB/1.1 GiB (216.4 MiB/s) with 24 file(s) remainingdownload: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/csl/part-00003-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to process-output/final-output/csl/part-00003-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2\n", "Completed 547.1 MiB/1.1 GiB (200.0 MiB/s) with 23 file(s) remainingCompleted 542.4 MiB/1.1 GiB (200.7 MiB/s) with 23 file(s) remainingCompleted 547.3 MiB/1.1 GiB (193.5 MiB/s) with 23 file(s) remainingdownload: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/csl/part-00004-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to process-output/final-output/csl/part-00004-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2\n", "download: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/no_pins_in_dw/part-00000-14789ece-bdb6-4fda-8373-b4fb6e10119a-c000.csv.bz2 to process-output/final-output/no_pins_in_dw/part-00000-14789ece-bdb6-4fda-8373-b4fb6e10119a-c000.csv.bz2 MiB/s) with 22 file(s) remainingCompleted 599.9 MiB/1.1 GiB (189.8 MiB/s) with 22 file(s) remaining\n", "download: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/no_pins_in_dw/_SUCCESS to process-output/final-output/no_pins_in_dw/_SUCCESS\n", "download: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/pin_analysis/_SUCCESS to process-output/final-output/pin_analysis/_SUCCESS\n", "download: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/no_pins_in_dw/993540_B_993540_pinning_file.no_pins_in_dw.manifest to process-output/final-output/no_pins_in_dw/993540_B_993540_pinning_file.no_pins_in_dw.manifest\n", "Completed 677.2 MiB/1.1 GiB (193.1 MiB/s) with 18 file(s) remainingCompleted 604.6 MiB/1.1 GiB (188.3 MiB/s) with 18 file(s) remainingCompleted 617.9 MiB/1.1 GiB (189.5 MiB/s) with 18 file(s) remainingCompleted 630.5 MiB/1.1 GiB (190.5 MiB/s) with 18 file(s) remainingCompleted 644.5 MiB/1.1 GiB (191.8 MiB/s) with 18 file(s) remainingCompleted 660.2 MiB/1.1 GiB (193.4 MiB/s) with 18 file(s) remainingCompleted 676.0 MiB/1.1 GiB (195.2 MiB/s) with 18 file(s) remainingdownload: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/csl/part-00005-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to process-output/final-output/csl/part-00005-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2\n", "download: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/csl/part-00007-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to process-output/final-output/csl/part-00007-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz20 MiB/1.1 GiB (195.6 MiB/s) with 17 file(s) remainingCompleted 737.2 MiB/1.1 GiB (195.7 MiB/s) with 17 file(s) remainingCompleted 750.1 MiB/1.1 GiB (196.4 MiB/s) with 17 file(s) remainingCompleted 763.8 MiB/1.1 GiB (197.4 MiB/s) with 17 file(s) remainingCompleted 775.8 MiB/1.1 GiB (197.9 MiB/s) with 17 file(s) remainingCompleted 788.1 MiB/1.1 GiB (198.4 MiB/s) with 17 file(s) remainingCompleted 802.1 MiB/1.1 GiB (199.4 MiB/s) with 17 file(s) remainingCompleted 814.8 MiB/1.1 GiB (200.1 MiB/s) with 17 file(s) remainingCompleted 822.2 MiB/1.1 GiB (199.2 MiB/s) with 17 file(s) remainingCompleted 831.8 MiB/1.1 GiB (199.0 MiB/s) with 17 file(s) remainingCompleted 832.5 MiB/1.1 GiB (197.0 MiB/s) with 17 file(s) remainingCompleted 833.0 MiB/1.1 GiB (194.8 MiB/s) with 17 file(s) remaining\n", "Completed 833.5 MiB/1.1 GiB (194.4 MiB/s) with 16 file(s) remainingdownload: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/csl/part-00008-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to process-output/final-output/csl/part-00008-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2\n", "Completed 842.3 MiB/1.1 GiB (184.4 MiB/s) with 15 file(s) remainingdownload: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/csl/part-00006-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to process-output/final-output/csl/part-00006-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2\n", "Completed 849.8 MiB/1.1 GiB (177.8 MiB/s) with 14 file(s) remainingCompleted 850.0 MiB/1.1 GiB (177.1 MiB/s) with 14 file(s) remainingdownload: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/csl/part-00009-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to process-output/final-output/csl/part-00009-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2\n", "Completed 899.5 MiB/1.1 GiB (171.1 MiB/s) with 13 file(s) remainingCompleted 855.0 MiB/1.1 GiB (171.6 MiB/s) with 13 file(s) remainingCompleted 869.3 MiB/1.1 GiB (172.7 MiB/s) with 13 file(s) remainingCompleted 883.3 MiB/1.1 GiB (173.8 MiB/s) with 13 file(s) remainingCompleted 896.0 MiB/1.1 GiB (174.5 MiB/s) with 13 file(s) remainingCompleted 898.8 MiB/1.1 GiB (173.2 MiB/s) with 13 file(s) remainingCompleted 899.0 MiB/1.1 GiB (171.3 MiB/s) with 13 file(s) remainingdownload: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/csl/part-00010-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2 to process-output/final-output/csl/part-00010-71d3086a-6e34-418b-999a-90327d1b4c68-c000.csv.bz2\n", "Completed 1022.8 MiB/1.1 GiB (174.0 MiB/s) with 12 file(s) remainingompleted 902.8 MiB/1.1 GiB (169.1 MiB/s) with 12 file(s) remainingCompleted 915.0 MiB/1.1 GiB (169.8 MiB/s) with 12 file(s) remainingCompleted 928.0 MiB/1.1 GiB (170.6 MiB/s) with 12 file(s) remainingCompleted 942.5 MiB/1.1 GiB (171.7 MiB/s) with 12 file(s) remainingCompleted 952.1 MiB/1.1 GiB (171.8 MiB/s) with 12 file(s) remainingCompleted 965.6 MiB/1.1 GiB (172.7 MiB/s) with 12 file(s) remainingCompleted 978.8 MiB/1.1 GiB (173.5 MiB/s) with 12 file(s) remainingCompleted 990.8 MiB/1.1 GiB (174.0 MiB/s) with 12 file(s) remainingCompleted 1004.6 MiB/1.1 GiB (174.9 MiB/s) with 12 file(s) remainingCompleted 1015.3 MiB/1.1 GiB (175.2 MiB/s) with 12 file(s) remainingCompleted 1021.6 MiB/1.1 GiB (174.8 MiB/s) with 12 file(s) remainingdownload: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/pinning_results/_SUCCESS to process-output/final-output/pinning_results/_SUCCESS\n", "download: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/reports/993540_B_993540_pinning_file.rtf to process-output/final-output/reports/993540_B_993540_pinning_file.rtf\n", "download: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/pin_analysis/part-00002-9c1258f0-5509-4d26-8649-d50ab39fbcec-c000.csv.bz2 to process-output/final-output/pin_analysis/part-00002-9c1258f0-5509-4d26-8649-d50ab39fbcec-c000.csv.bz2\n", "download: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/reports/993540_B_993540_pinning_file.csv to process-output/final-output/reports/993540_B_993540_pinning_file.csv\n", "download: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/reports/993540_B_993540_pinning_file.pdf to process-output/final-output/reports/993540_B_993540_pinning_file.pdf\n", "download: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/valcodes/_SUCCESS to process-output/final-output/valcodes/_SUCCESSningCompleted 1.0 GiB/1.1 GiB (170.8 MiB/s) with 7 file(s) remaining\n", "Completed 1.0 GiB/1.1 GiB (171.8 MiB/s) with 6 file(s) remainingCompleted 1.0 GiB/1.1 GiB (171.2 MiB/s) with 6 file(s) remainingdownload: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/pin_analysis/part-00000-9c1258f0-5509-4d26-8649-d50ab39fbcec-c000.csv.bz2 to process-output/final-output/pin_analysis/part-00000-9c1258f0-5509-4d26-8649-d50ab39fbcec-c000.csv.bz2\n", "Completed 1.1 GiB/1.1 GiB (168.1 MiB/s) with 5 file(s) remainingCompleted 1.1 GiB/1.1 GiB (166.9 MiB/s) with 5 file(s) remainingCompleted 1.1 GiB/1.1 GiB (167.5 MiB/s) with 5 file(s) remainingCompleted 1.1 GiB/1.1 GiB (168.7 MiB/s) with 5 file(s) remainingdownload: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/pin_analysis/part-00001-9c1258f0-5509-4d26-8649-d50ab39fbcec-c000.csv.bz2 to process-output/final-output/pin_analysis/part-00001-9c1258f0-5509-4d26-8649-d50ab39fbcec-c000.csv.bz2\n", "Completed 1.1 GiB/1.1 GiB (165.3 MiB/s) with 4 file(s) remainingCompleted 1.1 GiB/1.1 GiB (164.2 MiB/s) with 4 file(s) remainingdownload: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/pinning_results/part-00000-7d965150-61ad-4ff3-b0ef-e8d54a124cba-c000.csv.bz2 to process-output/final-output/pinning_results/part-00000-7d965150-61ad-4ff3-b0ef-e8d54a124cba-c000.csv.bz2\n", "Completed 1.1 GiB/1.1 GiB (165.3 MiB/s) with 3 file(s) remainingdownload: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/pinning_results/part-00001-7d965150-61ad-4ff3-b0ef-e8d54a124cba-c000.csv.bz2 to process-output/final-output/pinning_results/part-00001-7d965150-61ad-4ff3-b0ef-e8d54a124cba-c000.csv.bz2\n", "Completed 1.1 GiB/1.1 GiB (165.3 MiB/s) with 2 file(s) remainingdownload: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/pinning_results/part-00002-7d965150-61ad-4ff3-b0ef-e8d54a124cba-c000.csv.bz2 to process-output/final-output/pinning_results/part-00002-7d965150-61ad-4ff3-b0ef-e8d54a124cba-c000.csv.bz2\n", "Completed 1.1 GiB/1.1 GiB (165.3 MiB/s) with 1 file(s) remainingdownload: s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/process-output/final-output/valcodes/part-00000-d0d2821c-870c-401d-9e86-907d45b37a09-c000.csv.bz2 to process-output/final-output/valcodes/part-00000-d0d2821c-870c-401d-9e86-907d45b37a09-c000.csv.bz2\n"]}], "source": ["# ! aws s3 cp s3://expn-cis-pinning-prod-s3-fc-output/A993540.P993540.E20250804000000_000000002.A/ /work/users/c21868e/Telco_tmobile/ --recursive"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}