{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7edbd2de-ff84-4249-9a6e-b9def7b80476", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3.9.23 | packaged by conda-forge | (main, Jun  4 2025, 17:57:12) \n", "[GCC 13.3.0]\n"]}], "source": ["# Config\n", "import os, sys, findspark\n", "spark_home= '/usr/lib/spark'\n", "findspark.init(spark_home)\n", "  \n", "print(sys.version)"]}, {"cell_type": "code", "execution_count": 2, "id": "06346bb0-15ce-4693-91d3-5b93b17be4e9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "25/08/05 22:00:01 WARN Utils: Service 'SparkUI' could not bind on port 4040. Attempting port 4041.\n", "25/08/05 22:00:01 WARN Utils: Service 'SparkUI' could not bind on port 4041. Attempting port 4042.\n", "25/08/05 22:00:01 WARN Utils: Service 'SparkUI' could not bind on port 4042. Attempting port 4043.\n", "25/08/05 22:00:01 WARN Utils: Service 'SparkUI' could not bind on port 4043. Attempting port 4044.\n", "25/08/05 22:00:04 WARN Client: Neither spark.yarn.jars nor spark.yarn.archive is set, falling back to uploading libraries under SPARK_HOME.\n"]}], "source": ["os.environ['PYSPARK_PYTHON'] = \"/work/users/c21868e/.conda/envs/test/bin/python\"\n", " \n", "# or use this but you need to shut down kernel:       .set(\"spark.dynamicAllocation.enabled\", \"false\") \\\n", " \n", "from pyspark import SparkConf\n", "from pyspark.sql import SparkSession\n", "sc = SparkConf() \\\n", "     .set(\"spark.app.name\", \"Telco\") \\\n", "     .set(\"spark.executor.memory\", \"24g\") \\\n", "     .set(\"spark.executor.cores\", \"4\") \\\n", "     .set(\"spark.executor.instances\", \"10\") \\\n", "     .set(\"spark.dynamicAllocation.enabled\", \"true\") \\\n", "     .set(\"spark.dynamicAllocation.maxExecutors\", \"20\") \\\n", "     .set(\"spark.driver.maxResultSize\", \"18g\") \\\n", "     .set(\"spark.sql.execution.arrow.enabled\", \"true\") \\\n", "     .set(\"spark.serializer\", \"org.apache.spark.serializer.KryoSerializer\") \\\n", "     .set(\"spark.kryoserializer.buffer.max\", \"512M\")\n", "  \n", "spark = SparkSession.builder.config(conf=sc).getOrCreate()"]}, {"cell_type": "code", "execution_count": 3, "id": "07c3355f-ec3e-4739-86a6-50d586624ddd", "metadata": {}, "outputs": [], "source": ["sc = spark.sparkContext\n", "sc.setLogLevel(\"ERROR\")"]}, {"cell_type": "code", "execution_count": 4, "id": "80b69184-411f-4656-b1fb-3add02457270", "metadata": {}, "outputs": [], "source": ["from pyspark.sql import Row\n", "from pyspark.sql import functions as F\n", "from pyspark.sql import Window as W\n", "from pyspark.sql import types as T\n", "from pyspark.sql.functions import col, when, lit, concat, concat_ws, to_timestamp, expr, date_format"]}, {"cell_type": "markdown", "id": "efcd6d67-06b7-45bb-acce-60873eca2ce6", "metadata": {}, "source": ["## Prepare file for parsing"]}, {"cell_type": "code", "execution_count": 5, "id": "84d04a7f-9f86-4697-8134-88f89797d407", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["df = spark.read.option(\"header\", \"true\").csv(\"s3://expn-da-prd-innovationlabs/p/telco/tmobile/raw/app_202401_202412/edlca6717_Experian File New_2024.csv\")"]}, {"cell_type": "code", "execution_count": 6, "id": "e103c6ab-a4b1-4259-9a3d-91b1c820ac8f", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["root\n", " |-- CRID_ENCR: string (nullable = true)\n", " |-- ENTITYTYPE: string (nullable = true)\n", " |-- APP_TYPE: string (nullable = true)\n", " |-- IS_VALID: string (nullable = true)\n", " |-- SUB_APPTYPE: string (nullable = true)\n", " |-- APP_YYYYMM_GMT: string (nullable = true)\n", " |-- APPLICATION_NUMBER: string (nullable = true)\n", " |-- APPLICATIONDATE_GMT: string (nullable = true)\n", " |-- LAST_NAME: string (nullable = true)\n", " |-- FIRST_NAME: string (nullable = true)\n", " |-- MIDDLE_NAME: string (nullable = true)\n", " |-- SUFFIXNAME: string (nullable = true)\n", " |-- ADRESS_LINE1: string (nullable = true)\n", " |-- ADRESS_LINE2: string (nullable = true)\n", " |-- ADDRESS_ZIP: string (nullable = true)\n", " |-- ADDRESS_CITY: string (nullable = true)\n", " |-- ADDRESS_STATE: string (nullable = true)\n", " |-- DOB: string (nullable = true)\n", " |-- SSN: string (nullable = true)\n", " |-- IDENTITY_DOCUMENT_TYPE: string (nullable = true)\n", " |-- IDENTITY_DOCUMENT_NUMBER: string (nullable = true)\n", " |-- PHONE_NUMBER: string (nullable = true)\n", " |-- EMAIL: string (nullable = true)\n", " |-- IP: string (nullable = true)\n", " |-- BYOD: string (nullable = true)\n", " |-- SALES_CHANNEL: string (nullable = true)\n", " |-- SALES_CHANNEL_NAME: string (nullable = true)\n", " |-- SALES_CHANNEL_SEGMENT: string (nullable = true)\n", " |-- STORE_ADDRESS: string (nullable = true)\n", " |-- STORE_CITY: string (nullable = true)\n", " |-- STORE_STATE: string (nullable = true)\n", " |-- STORE_ZIP: string (nullable = true)\n", " |-- MR_FLAG_STRATEGY: string (nullable = true)\n", " |-- MR_FLAG: string (nullable = true)\n", " |-- MR_CLEARED: string (nullable = true)\n", " |-- ACTIVATIONFLAG: string (nullable = true)\n", " |-- HSI_FLAG: string (nullable = true)\n", " |-- ACCOUNT_NUMBER: string (nullable = true)\n", " |-- ACTIVATION_CHANNEL: string (nullable = true)\n", " |-- ACCOUNT_OPEN_DATETIME: string (nullable = true)\n", " |-- NUMBER_OF_LINES: string (nullable = true)\n", " |-- NUM_DEVICE_FINANCED: string (nullable = true)\n", " |-- TOT_FINANCING_AMT: string (nullable = true)\n", " |-- STATUS: string (nullable = true)\n", " |-- FRAUD: string (nullable = true)\n", " |-- FRAUD_TPF: string (nullable = true)\n", " |-- FRAUD_FPF: string (nullable = true)\n", " |-- FRAUD_DATE: string (nullable = true)\n", " |-- FRAUD_WO_AMT: string (nullable = true)\n", " |-- FPD: string (nullable = true)\n", " |-- FPD_DATE: string (nullable = true)\n", " |-- FPD_WO_AMT: string (nullable = true)\n", " |-- OTHER_WO: string (nullable = true)\n", " |-- OTHER_WO_DATE: string (nullable = true)\n", " |-- OTHER_WO_AMT: string (nullable = true)\n", " |-- NO_USAGE_NEW: string (nullable = true)\n", " |-- NO_USAGE_OLD: string (nullable = true)\n", "\n"]}], "source": ["df.printSchema()"]}, {"cell_type": "code", "execution_count": 8, "id": "47253c5f-88e9-44f5-8358-3a33528a53e7", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 2:>                                                          (0 + 1) / 1]"]}, {"name": "stdout", "output_type": "stream", "text": ["+-----------------------+------+\n", "|APPLICATIONDATE_GMT    |month |\n", "+-----------------------+------+\n", "|2024-11-26 20:55:25.000|202411|\n", "|2024-07-20 16:30:03.000|202407|\n", "|2024-03-07 18:33:56.000|202403|\n", "|2024-05-03 14:17:02.000|202405|\n", "|2024-02-13 23:43:47.000|202402|\n", "|2024-07-11 20:17:47.000|202407|\n", "|2024-06-20 20:44:31.000|202406|\n", "|2024-08-31 20:33:45.000|202408|\n", "|2024-04-28 15:16:42.000|202404|\n", "|2024-11-25 15:51:43.000|202411|\n", "|2024-10-28 21:10:26.000|202410|\n", "|2024-04-17 22:35:48.000|202404|\n", "|2024-04-03 00:30:46.000|202404|\n", "|2024-07-14 17:15:47.000|202407|\n", "|2024-02-06 18:19:09.000|202402|\n", "|2024-05-30 16:58:00.000|202405|\n", "|2024-06-24 20:21:46.000|202406|\n", "|2024-11-08 23:16:25.000|202411|\n", "|2024-02-09 02:00:33.000|202402|\n", "|2024-07-08 22:33:32.000|202407|\n", "+-----------------------+------+\n", "only showing top 20 rows\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["df = df.withColumn(\n", "    \"month\", date_format(\"APPLICATIONDATE_GMT\", \"yyyyMM\")\n", ")\n", "\n", "df.select(\"APPLICATIONDATE_GMT\", \"month\").show(truncate=False)"]}, {"cell_type": "code", "execution_count": 10, "id": "a381d432-777a-4930-b68d-28f912cb3a56", "metadata": {}, "outputs": [], "source": ["df_2parse = df.select(\"APPLICATION_NUMBER\",\n", "                     \"LAST_NAME\", \"FIRST_NAME\", \"MIDDLE_NAME\", \"SUFFIXNAME\",\n", "                     \"ADRESS_LINE1\", \"ADRESS_LINE2\", \"ADDRESS_ZIP\", \"ADDRESS_CITY\", \"ADDRESS_STATE\",\n", "                     \"PHONE_NUMBER\", \"DOB\", \"SSN\", \"IP\", \"EMAIL\")"]}, {"cell_type": "code", "execution_count": 11, "id": "9dc03893-d048-4c1e-bace-b439928061ba", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"data": {"text/plain": ["11665548"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df_2parse.count()"]}, {"cell_type": "code", "execution_count": 13, "id": "036e459f-9d14-4c29-a6b8-f557f0dd7295", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 8:>                                                          (0 + 1) / 1]"]}, {"name": "stdout", "output_type": "stream", "text": ["+------------------+--------------------+----------+-----------+----------+--------------------+------------+-----------+-------------------+--------------+------------+----------+---------+--------------------+--------------------+\n", "|APPLICATION_NUMBER|           LAST_NAME|FIRST_NAME|MIDDLE_NAME|SUFFIXNAME|        ADRESS_LINE1|ADRESS_LINE2|ADDRESS_ZIP|       ADDRESS_CITY| ADDRESS_STATE|PHONE_NUMBER|       DOB|      SSN|                  IP|               EMAIL|\n", "+------------------+--------------------+----------+-----------+----------+--------------------+------------+-----------+-------------------+--------------+------------+----------+---------+--------------------+--------------------+\n", "|    20243312013341|           RODRIGUEZ|     JESUS|       null|      null|451 W 27TH ST  AP...|        null|      60616|            CHICAGO|      Illinois|  1111111111|1996-06-03|885486160|                null|                null|\n", "|    20242023010310|            ESPINOZA|    SAMUEL|       null|      null|    20109 Sw 88th Ct|        null|      33189|         CUTLER BAY|       Florida|  7867128300|2003-08-14|766243013|                null|samuel.espinoza12...|\n", "|    20240672004878|       CUCA QUINONES|   OCTAVIO|       null|      null|5069 TWO CHIMNEYS CT|        null|      22193|         WOODBRIDGE|      Virginia|  5716530957|1975-01-22|994948156|                null|                null|\n", "|    20241243007317|                RICE|   ANTHONY|       null|      null|2847 STATE ROUTE 245|        null|      43009|              CABLE|          Ohio|  9376455993|1964-08-09|269765121|                null|<EMAIL>|\n", "|    20240442016064|      MORENO CARRION| YORBIELTN|       null|      null|        2035 Joan <PERSON>|        null|      34231|           Sarasota|       Florida|  1111111111|1991-12-13|842449426|                null|                null|\n", "|    20241932011900|<PERSON><PERSON><PERSON>|     <PERSON><PERSON>|       null|      null|       257 SCHOOL ST|        null|      01854|             LOWELL| Massachusetts|  8677685550|1965-09-07|985983326|                null|                null|\n", "|    20241723014961|              COOPER|      ROSS|       null|      null|    254 TENNYSON AVE|        null|      13204|           Syracuse|      New York|  3154167835|1990-05-22|     3515|                null|                null|\n", "|    20242442013437|           Cervantes| Elizabeth|       null|      null|    1931 E Meats ave|      Apt 94|      92865|             orange|    California|  7148251971|1988-10-09|614182246|                null|cervantesliz1998@...|\n", "|    20241193006627|               <PERSON>|     <PERSON><PERSON><PERSON>|          S|      null|        537 MEIGS ST|           2|      14607|          ROCHESTER|      New York|  5852300697|1993-10-01|059842220|                null|ieshajames01@yaho...|\n", "|    20243303009382|             <PERSON><PERSON><PERSON><PERSON><PERSON>|    <PERSON>|       null|      null|43132 Hampton Street|        null|      93536|          Lancaster|    California|        null|1959-06-20|     5925|      12.160.226.211|Sweetpeaa@verizon...|\n", "|    20243022012379|            <PERSON><PERSON>|  Lashonda|       null|      null| 2465 morning dew dr|        null|      75068|         Little Elm|         Texas|        null|1990-01-04|     8240|2600:100c:b248:79...|lashonda.roberson...|\n", "|    20241082014303|               DURAN|    YADIRA|       null|      null|     739 PLYMOUTH ST|        null|      18109|          Allentown|  Pennsylvania|  4845540094|1982-12-20|     6187|                null|                null|\n", "|    20240942000551|           Schneider|Alexandria|       null|      null|          101 7th St|        null|      37148|           Portland|     Tennessee|  6154339129|1992-04-11|530672465|       107.77.236.26|alialischneider@h...|\n", "|    20241963007997|             VAUGHAN|   CORDELL|       null|      null|  130 10  107TH  AVE|        null|      11419|SOUTH RICHMOND HILL|      New York|  1111111111|1955-08-24|077508790|                null|                null|\n", "|    20240373010640|       BORRERO MONGE|      LUIS|       null|      null|    5056 42ND AVE SW|        null|      98136|            Seattle|    Washington|  2061111111|1973-08-16|     5777|                null|                null|\n", "|    20241513009402|             fedarko|   carolyn|          e|      null|      816 32nd st ne|        null|      28613|            conover|North Carolina|  8286541133|1974-01-13|284829332|                null|carolynkay74@gmai...|\n", "|    20241762011733|               <PERSON>|      <PERSON>|       null|      null|   1002 W 4th Street|        null|      46952|             Marion|       Indiana|        null|1965-05-03|     6928|       24.123.30.226|mark<PERSON><PERSON>_65@yaho...|\n", "|    20243132017337|              <PERSON><PERSON>|    De<PERSON><PERSON>|       null|      null|    901 N CANSLER ST|        null|      28086|     Kings Mountain|North Carolina|  7046924429|1973-02-12|     1680| 2600:387:15:1919::4|  <EMAIL>|\n", "|    20240403001805|          cunningham|   michael|       null|      null|   314 SOUTH 18TH ST|        null|      35020|           BESSEMER|       Alabama|  2058623755|2000-03-02|051907952|       68.207.146.97|immichaelcunningh...|\n", "|    20241903017561|               IGAFO|     ARIEZ|       null|      null|45 1015 KAMAU PL ...|        null|      96744|            KANEOHE|        Hawaii|  8087267163|2001-11-03|575916902|                null|                null|\n", "+------------------+--------------------+----------+-----------+----------+--------------------+------------+-----------+-------------------+--------------+------------+----------+---------+--------------------+--------------------+\n", "only showing top 20 rows\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["df_2parse.show()"]}, {"cell_type": "code", "execution_count": 14, "id": "3771b569-068a-4720-8041-5ca4489fba7d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["# df_2parse.write.mode(\"overwrite\").parquet(\"s3://signal-hub-365007379098-us-east-1/inquiry/oeidp/data/inquiry_requests/tmobile_refresh\")"]}, {"cell_type": "markdown", "id": "901bfcae-2496-4a20-b4b0-4b07e92c3f33", "metadata": {}, "source": ["## Load parsed content"]}, {"cell_type": "code", "execution_count": 16, "id": "35735699-d29c-4695-a4f6-d267305174fd", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["parsed = spark.read.parquet(\"s3://signal-hub-365007379098-us-east-1/inquiry/oeidp/data/parsed/tmobile_refresh/tmobile.processed\")"]}, {"cell_type": "code", "execution_count": 17, "id": "e32ef9d0-8e4a-4323-b839-279ca26227f5", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"data": {"text/plain": ["11665548"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["parsed.count()"]}, {"cell_type": "code", "execution_count": 18, "id": "4480549c-c4e2-4c32-b925-059947ae0ba9", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["root\n", " |-- APPLICATION_NUMBER: string (nullable = true)\n", " |-- BUID: string (nullable = true)\n", " |-- addresses: array (nullable = true)\n", " |    |-- element: struct (containsNull = true)\n", " |    |    |-- address_id: string (nullable = true)\n", " |    |    |-- address_type: string (nullable = true)\n", " |    |    |-- city_name: string (nullable = true)\n", " |    |    |-- country: string (nullable = true)\n", " |    |    |-- dwelling_type: string (nullable = true)\n", " |    |    |-- error_code: string (nullable = true)\n", " |    |    |-- frequency: string (nullable = true)\n", " |    |    |-- full_address: string (nullable = true)\n", " |    |    |-- last_update_date: string (nullable = true)\n", " |    |    |-- raw: string (nullable = true)\n", " |    |    |-- soft_del_cd: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- state_abbr: string (nullable = true)\n", " |    |    |-- street_name: string (nullable = true)\n", " |    |    |-- street_number: string (nullable = true)\n", " |    |    |-- street_post_directional: string (nullable = true)\n", " |    |    |-- street_pre_directional: string (nullable = true)\n", " |    |    |-- street_suffix: string (nullable = true)\n", " |    |    |-- to_database_date: string (nullable = true)\n", " |    |    |-- type: string (nullable = true)\n", " |    |    |-- unit_id: string (nullable = true)\n", " |    |    |-- unit_type: string (nullable = true)\n", " |    |    |-- urbanization: string (nullable = true)\n", " |    |    |-- validity: string (nullable = true)\n", " |    |    |-- zip5: string (nullable = true)\n", " |    |    |-- zip_plus_4: string (nullable = true)\n", " |-- counter: string (nullable = true)\n", " |-- data_source: string (nullable = true)\n", " |-- dobs: array (nullable = true)\n", " |    |-- element: struct (containsNull = true)\n", " |    |    |-- dob: string (nullable = true)\n", " |    |    |-- error_code: string (nullable = true)\n", " |    |    |-- last_update_date: string (nullable = true)\n", " |    |    |-- to_database_date: string (nullable = true)\n", " |    |    |-- type: string (nullable = true)\n", " |    |    |-- validity: string (nullable = true)\n", " |-- emails: array (nullable = true)\n", " |    |-- element: struct (containsNull = true)\n", " |    |    |-- email: string (nullable = true)\n", " |    |    |-- error_code: string (nullable = true)\n", " |    |    |-- frequency: string (nullable = true)\n", " |    |    |-- last_update_date: string (nullable = true)\n", " |    |    |-- raw: string (nullable = true)\n", " |    |    |-- to_database_date: string (nullable = true)\n", " |    |    |-- type: string (nullable = true)\n", " |    |    |-- validity: string (nullable = true)\n", " |-- names: array (nullable = true)\n", " |    |-- element: struct (containsNull = true)\n", " |    |    |-- best_ind: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- best_ssn_ind: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- con_ident_nb: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- error_code: string (nullable = true)\n", " |    |    |-- first_name: string (nullable = true)\n", " |    |    |-- frequency: string (nullable = true)\n", " |    |    |-- generation_code: string (nullable = true)\n", " |    |    |-- last_name: string (nullable = true)\n", " |    |    |-- last_update_date: string (nullable = true)\n", " |    |    |-- middle_name: string (nullable = true)\n", " |    |    |-- raw: string (nullable = true)\n", " |    |    |-- second_last_name: string (nullable = true)\n", " |    |    |-- soft_del_cd: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- ssn: string (nullable = true)\n", " |    |    |-- to_database_date: string (nullable = true)\n", " |    |    |-- trade_count: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- type: string (nullable = true)\n", " |    |    |-- updt_db_id: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- validity: string (nullable = true)\n", " |-- phones: array (nullable = true)\n", " |    |-- element: struct (containsNull = true)\n", " |    |    |-- area_code: string (nullable = true)\n", " |    |    |-- error_code: string (nullable = true)\n", " |    |    |-- frequency: string (nullable = true)\n", " |    |    |-- last7digits: string (nullable = true)\n", " |    |    |-- last_update_date: string (nullable = true)\n", " |    |    |-- phone_number: string (nullable = true)\n", " |    |    |-- phone_rank_cd: string (nullable = true)\n", " |    |    |-- phone_type: string (nullable = true)\n", " |    |    |-- raw: string (nullable = true)\n", " |    |    |-- soft_del_cd: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- to_database_date: string (nullable = true)\n", " |    |    |-- type: string (nullable = true)\n", " |    |    |-- upd_db_id: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- validity: string (nullable = true)\n", " |-- ssns: array (nullable = true)\n", " |    |-- element: struct (containsNull = true)\n", " |    |    |-- best_ind: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- error_code: string (nullable = true)\n", " |    |    |-- frequency: string (nullable = true)\n", " |    |    |-- last_update_date: string (nullable = true)\n", " |    |    |-- soft_del_cd: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- ssn: string (nullable = true)\n", " |    |    |-- to_database_date: string (nullable = true)\n", " |    |    |-- trade_count: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- type: string (nullable = true)\n", " |    |    |-- updt_db_id: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- validity: string (nullable = true)\n", " |-- timestamp_type: string (nullable = true)\n", " |-- timestamps: array (nullable = true)\n", " |    |-- element: string (containsNull = true)\n", "\n"]}], "source": ["parsed.printSchema()"]}, {"cell_type": "code", "execution_count": 21, "id": "94dfa0e8-ed69-4899-879f-9625324ccb7b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["# parsed.join(df.select(\"APPLICATION_NUMBER\", \"APPLICATIONDATE_GMT\", \"month\"), on=\"APPLICATION_NUMBER\", how=\"inner\") \\\n", "# .write.partitionBy(\"month\").mode(\"overwrite\").parquet(\"s3://expn-da-prd-innovationlabs/p/telco/tmobile/parsed/refresh_202401_202412\")"]}, {"cell_type": "code", "execution_count": 22, "id": "f6e8ba11-a2ab-4992-946f-4ecdc74bb833", "metadata": {}, "outputs": [], "source": ["df_content = spark.read.parquet(\"s3://expn-da-prd-innovationlabs/p/telco/tmobile/parsed/refresh_202401_202412\")"]}, {"cell_type": "code", "execution_count": 23, "id": "dce031e4-6cc7-4558-aec6-c3b403a3e38a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"data": {"text/plain": ["11665548"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["df_content.count()"]}, {"cell_type": "code", "execution_count": 24, "id": "9699d52f-8a8a-4cfd-9eee-30e08f087d82", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["root\n", " |-- APPLICATION_NUMBER: string (nullable = true)\n", " |-- BUID: string (nullable = true)\n", " |-- addresses: array (nullable = true)\n", " |    |-- element: struct (containsNull = true)\n", " |    |    |-- address_id: string (nullable = true)\n", " |    |    |-- address_type: string (nullable = true)\n", " |    |    |-- city_name: string (nullable = true)\n", " |    |    |-- country: string (nullable = true)\n", " |    |    |-- dwelling_type: string (nullable = true)\n", " |    |    |-- error_code: string (nullable = true)\n", " |    |    |-- frequency: string (nullable = true)\n", " |    |    |-- full_address: string (nullable = true)\n", " |    |    |-- last_update_date: string (nullable = true)\n", " |    |    |-- raw: string (nullable = true)\n", " |    |    |-- soft_del_cd: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- state_abbr: string (nullable = true)\n", " |    |    |-- street_name: string (nullable = true)\n", " |    |    |-- street_number: string (nullable = true)\n", " |    |    |-- street_post_directional: string (nullable = true)\n", " |    |    |-- street_pre_directional: string (nullable = true)\n", " |    |    |-- street_suffix: string (nullable = true)\n", " |    |    |-- to_database_date: string (nullable = true)\n", " |    |    |-- type: string (nullable = true)\n", " |    |    |-- unit_id: string (nullable = true)\n", " |    |    |-- unit_type: string (nullable = true)\n", " |    |    |-- urbanization: string (nullable = true)\n", " |    |    |-- validity: string (nullable = true)\n", " |    |    |-- zip5: string (nullable = true)\n", " |    |    |-- zip_plus_4: string (nullable = true)\n", " |-- counter: string (nullable = true)\n", " |-- data_source: string (nullable = true)\n", " |-- dobs: array (nullable = true)\n", " |    |-- element: struct (containsNull = true)\n", " |    |    |-- dob: string (nullable = true)\n", " |    |    |-- error_code: string (nullable = true)\n", " |    |    |-- last_update_date: string (nullable = true)\n", " |    |    |-- to_database_date: string (nullable = true)\n", " |    |    |-- type: string (nullable = true)\n", " |    |    |-- validity: string (nullable = true)\n", " |-- emails: array (nullable = true)\n", " |    |-- element: struct (containsNull = true)\n", " |    |    |-- email: string (nullable = true)\n", " |    |    |-- error_code: string (nullable = true)\n", " |    |    |-- frequency: string (nullable = true)\n", " |    |    |-- last_update_date: string (nullable = true)\n", " |    |    |-- raw: string (nullable = true)\n", " |    |    |-- to_database_date: string (nullable = true)\n", " |    |    |-- type: string (nullable = true)\n", " |    |    |-- validity: string (nullable = true)\n", " |-- names: array (nullable = true)\n", " |    |-- element: struct (containsNull = true)\n", " |    |    |-- best_ind: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- best_ssn_ind: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- con_ident_nb: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- error_code: string (nullable = true)\n", " |    |    |-- first_name: string (nullable = true)\n", " |    |    |-- frequency: string (nullable = true)\n", " |    |    |-- generation_code: string (nullable = true)\n", " |    |    |-- last_name: string (nullable = true)\n", " |    |    |-- last_update_date: string (nullable = true)\n", " |    |    |-- middle_name: string (nullable = true)\n", " |    |    |-- raw: string (nullable = true)\n", " |    |    |-- second_last_name: string (nullable = true)\n", " |    |    |-- soft_del_cd: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- ssn: string (nullable = true)\n", " |    |    |-- to_database_date: string (nullable = true)\n", " |    |    |-- trade_count: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- type: string (nullable = true)\n", " |    |    |-- updt_db_id: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- validity: string (nullable = true)\n", " |-- phones: array (nullable = true)\n", " |    |-- element: struct (containsNull = true)\n", " |    |    |-- area_code: string (nullable = true)\n", " |    |    |-- error_code: string (nullable = true)\n", " |    |    |-- frequency: string (nullable = true)\n", " |    |    |-- last7digits: string (nullable = true)\n", " |    |    |-- last_update_date: string (nullable = true)\n", " |    |    |-- phone_number: string (nullable = true)\n", " |    |    |-- phone_rank_cd: string (nullable = true)\n", " |    |    |-- phone_type: string (nullable = true)\n", " |    |    |-- raw: string (nullable = true)\n", " |    |    |-- soft_del_cd: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- to_database_date: string (nullable = true)\n", " |    |    |-- type: string (nullable = true)\n", " |    |    |-- upd_db_id: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- validity: string (nullable = true)\n", " |-- ssns: array (nullable = true)\n", " |    |-- element: struct (containsNull = true)\n", " |    |    |-- best_ind: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- error_code: string (nullable = true)\n", " |    |    |-- frequency: string (nullable = true)\n", " |    |    |-- last_update_date: string (nullable = true)\n", " |    |    |-- soft_del_cd: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- ssn: string (nullable = true)\n", " |    |    |-- to_database_date: string (nullable = true)\n", " |    |    |-- trade_count: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- type: string (nullable = true)\n", " |    |    |-- updt_db_id: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- validity: string (nullable = true)\n", " |-- timestamp_type: string (nullable = true)\n", " |-- timestamps: array (nullable = true)\n", " |    |-- element: string (containsNull = true)\n", " |-- APPLICATIONDATE_GMT: string (nullable = true)\n", " |-- month: integer (nullable = true)\n", "\n"]}], "source": ["df_content.printSchema()"]}, {"cell_type": "markdown", "id": "f736f09e-2560-443d-8d8f-1f31824954da", "metadata": {}, "source": ["## Generate and save UID"]}, {"cell_type": "code", "execution_count": 26, "id": "ccacf197-5952-4f7a-a342-e79343e949c9", "metadata": {}, "outputs": [], "source": ["df_with_uids = df_content \\\n", ".withColumn(\n", "    \"email_uid\",\n", "    when(\n", "        (col(\"emails\")[0][\"email\"].isNotNull()) & (col(\"emails\")[0][\"email\"] != \"\"),\n", "        col(\"emails\")[0][\"email\"]\n", "    ).otherwise(lit(None))\n", ") \\\n", ".withColumn(\n", "    \"ssn_uid\",\n", "    when(\n", "        (col(\"ssns\")[0][\"ssn\"].isNotNull()) & (col(\"ssns\")[0][\"ssn\"] != \"\") & (~col(\"ssns\")[0][\"ssn\"].startswith(\"00000\")),\n", "        col(\"ssns\")[0][\"ssn\"]\n", "    ).otherwise(lit(None))\n", ") \\\n", ".withColumn(\n", "    \"phone_uid\",\n", "    when(\n", "        (col(\"phones\")[0][\"area_code\"].isNotNull()) & (col(\"phones\")[0][\"area_code\"] != \"\") &\n", "        (col(\"phones\")[0][\"last7digits\"].isNotNull()) & (col(\"phones\")[0][\"last7digits\"] != \"\"),\n", "        concat(\n", "            col(\"phones\")[0][\"area_code\"],\n", "            col(\"phones\")[0][\"last7digits\"]\n", "        )\n", "    ).otherwise(lit(None))\n", ") \\\n", ".withColumn(\n", "    \"addr_uid\",\n", "    when(\n", "        (col(\"addresses\")[0][\"street_number\"].isNotNull()) & (col(\"addresses\")[0][\"street_number\"] != \"\") &\n", "        (col(\"addresses\")[0][\"street_name\"].isNotNull()) & (col(\"addresses\")[0][\"street_name\"] != \"\"),\n", "        concat_ws(\n", "            \";\",\n", "            col(\"addresses\")[0][\"urbanization\"],\n", "            col(\"addresses\")[0][\"street_number\"],\n", "            col(\"addresses\")[0][\"street_pre_directional\"],\n", "            col(\"addresses\")[0][\"street_name\"],\n", "            col(\"addresses\")[0][\"street_post_directional\"],\n", "            col(\"addresses\")[0][\"street_suffix\"],\n", "            col(\"addresses\")[0][\"unit_id\"],\n", "            col(\"addresses\")[0][\"zip5\"],\n", "            col(\"addresses\")[0][\"zip_plus_4\"]\n", "        )\n", "    ).otherwise(lit(None))\n", ")"]}, {"cell_type": "code", "execution_count": 27, "id": "98c847f3-239e-4308-8fde-6ebf862c7929", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["+------------------+-----------------+--------------------------------+---------+----------+-------------------------------------------+-----------------------+------+\n", "|APPLICATION_NUMBER|BUID             |email_uid                       |ssn_uid  |phone_uid |addr_uid                                   |APPLICATIONDATE_GMT    |month |\n", "+------------------+-----------------+--------------------------------+---------+----------+-------------------------------------------+-----------------------+------+\n", "|20243522007122    |TM_20243522007122|null                            |null     |5702748406|;704;W;EMAUS;;AVE;;18103;6773              |2024-12-17 16:09:47.000|202412|\n", "|20243463018931    |TM_20243463018931|null                            |630900015|null      |;4563;;BUCKLERIDGE;;RD;;77053;4746         |2024-12-11 21:56:00.000|202412|\n", "|20243592003338    |TM_20243592003338|<EMAIL>         |615988951|7252965863|;9744;;WAILINGS;;AVE;;89148;5729           |2024-12-24 03:59:01.000|202412|\n", "|20243592013313    |TM_20243592013313|<EMAIL>          |null     |null      |;800;;INDIANA HIGHWAY 212;;;N2;46360;3032  |2024-12-24 20:10:53.000|202412|\n", "|20243592015279    |TM_20243592015279|null                            |null     |3125220245|;9201;S;DOBSON;;AVE;;60619;7819            |2024-12-24 21:25:37.000|202412|\n", "|20243522007012    |TM_20243522007012|null                            |null     |9039341051|;706;;MOCKINGBIRD;;LN;;75701;2925          |2024-12-17 16:05:11.000|202412|\n", "|20243462016549    |TM_20243462016549|null                            |null     |6452234967|;1419;NW;62ND;;ST;;33147;7919              |2024-12-11 23:25:59.000|202412|\n", "|20243612003569    |TM_20243612003569|<EMAIL>        |659071189|3372635876|;211;;LOUIE;;ST;49;70601;7294              |2024-12-26 14:36:58.000|202412|\n", "|20243623016991    |TM_20243623016991|null                            |null     |7083624831|;13936;;LEGEND;;TRL;;80023;4239            |2024-12-27 19:46:12.000|202412|\n", "|20243393021052    |TM_20243393021052|<EMAIL>                    |089467818|3103451348|;2050;W;DUNLAP;;AVE;;85021;2904            |2024-12-04 22:31:20.000|202412|\n", "|20243412012302    |TM_20243412012302|null                            |614552869|3136483546|;3909;;LAWNDALE;;ST;;48210;2005            |2024-12-06 19:54:56.000|202412|\n", "|20243533015078    |TM_20243533015078|null                            |582870452|4079731215|;692;;GRIFFON;;AVE;;33850;2583             |2024-12-18 18:58:50.000|202412|\n", "|20243633017656    |TM_20243633017656|null                            |null     |9046131592|;2919;;BENT BOW;;LN;;32068;8249            |2024-12-28 19:28:30.000|202412|\n", "|20243642007872    |TM_20243642007872|null                            |089880429|null      |;368R;E;9TH;;ST;2;11218;                   |2024-12-29 18:21:42.000|202412|\n", "|20243393019425    |TM_20243393019425|null                            |683105303|null      |;5400;;MEMORIAL;;DR;;30083;3235            |2024-12-04 21:25:11.000|202412|\n", "|20243632000537    |TM_20243632000537|<EMAIL>        |null     |null      |;301;;PLAZA;;DR;;15236;5103                |2024-12-28 00:28:34.000|202412|\n", "|20243382010105    |TM_20243382010105|<EMAIL>               |238756746|7575897717|;1325;;HAWK;;AVE;;23453;2911               |2024-12-03 18:11:57.000|202412|\n", "|20243652007624    |TM_20243652007624|null                            |705208314|2672353256|;1238;;STANWOOD;;ST;;19111;1513            |2024-12-30 17:59:15.000|202412|\n", "|20243422015637    |TM_20243422015637|null                            |null     |9181828282|;14655;W;OVERLOOK;;DR;;74063;8392          |2024-12-07 22:26:45.000|202412|\n", "|20243422006226    |TM_20243422006226|null                            |null     |7402215500|;8605;;CARSON;;RD;;43777;9544              |2024-12-07 16:07:15.000|202412|\n", "|20243642009127    |TM_20243642009127|null                            |null     |8034406072|;370;;SEASE HILL;;RD;;29073;8977           |2024-12-29 19:22:14.000|202412|\n", "|20243363015795    |TM_20243363015795|null                            |null     |3854017524|;3933;S;6285;W;;;84128;7037                |2024-12-01 22:59:01.000|202412|\n", "|20243453014992    |TM_20243453014992|null                            |null     |7149890210|;11035;;WESTMINSTER;;AVE;;92843;3640       |2024-12-10 21:51:41.000|202412|\n", "|20243392012141    |TM_20243392012141|null                            |null     |5633400985|;1733;;KIRKWOOD;;BLVD;;52803;3828          |2024-12-04 19:31:34.000|202412|\n", "|20243452008130    |TM_20243452008130|null                            |null     |2482211574|;18716;;AVON;;AVE;;48219;2972              |2024-12-10 17:21:20.000|202412|\n", "|20243412016050    |TM_20243412016050|null                            |null     |5593471714|;843;;P;;ST;;93622;2135                    |2024-12-06 22:23:26.000|202412|\n", "|20243532005484    |TM_20243532005484|<EMAIL>           |null     |null      |;230;E;MADISON;;ST;;17225;1024             |2024-12-18 13:48:49.000|202412|\n", "|20243633014185    |TM_20243633014185|null                            |null     |7655207067|;8747;;BROWNS VALLEY;;CT;;46113;8819       |2024-12-28 17:28:42.000|202412|\n", "|20243572017197    |TM_20243572017197|null                            |599578797|9197417929|;740;;SMALLWOOD;;DR;33;27605;1363          |2024-12-22 22:57:13.000|202412|\n", "|20243413013197    |TM_20243413013197|<EMAIL>           |null     |null      |;3436;;BAYOU FOREST;;DR;;77571;7171        |2024-12-06 17:06:26.000|202412|\n", "|20243642005587    |TM_20243642005587|<EMAIL>        |002926848|6037176928|;54;;GARVINS FALLS;;RD;;03301;5174         |2024-12-29 16:07:40.000|202412|\n", "|20243383024299    |TM_20243383024299|null                            |null     |8705560089|;3020;W;40TH;;AVE;;71603;6286              |2024-12-03 23:50:08.000|202412|\n", "|20243462007353    |TM_20243462007353|null                            |040688583|8603293620|;133;;SHUTTLEMEADOW;;RD;;06062;3248        |2024-12-11 16:56:46.000|202412|\n", "|20243492008003    |TM_20243492008003|null                            |null     |6053665156|;4120;S;FAIRHALL;;AVE;;57106;1743          |2024-12-14 17:05:07.000|202412|\n", "|20243542008051    |TM_20243542008051|null                            |162135795|null      |;4030;;82TH;;ST;;11373;                    |2024-12-19 16:20:39.000|202412|\n", "|20243652014471    |TM_20243652014471|null                            |null     |4438264648|;12311;;BONMOT;;PL;;21136;1701             |2024-12-30 22:12:56.000|202412|\n", "|20243492010054    |TM_20243492010054|<EMAIL>       |334988257|3126858807|;5322;W;ADAMS;;ST;;60644;4059              |2024-12-14 18:33:22.000|202412|\n", "|20243503012972    |TM_20243503012972|<EMAIL>           |null     |null      |;6219;S;hwy 51;;;293;53546;                |2024-12-15 19:08:12.000|202412|\n", "|20243542008053    |TM_20243542008053|<EMAIL>     |null     |null      |;1363;;BAYOU;;ST;;71640;2152               |2024-12-19 16:20:43.000|202412|\n", "|20243403018323    |TM_20243403018323|<EMAIL>           |563694872|8053426035|;341;N;14TH;;ST;;93060;2342                |2024-12-05 23:51:39.000|202412|\n", "|20243653012583    |TM_20243653012583|<EMAIL>              |221522359|3034478569|;605;;DUNBARTON;;;;19947;1178              |2024-12-30 17:54:20.000|202412|\n", "|20243632016940    |TM_20243632016940|<EMAIL>         |600187044|4804531270|;1775;E;MCKINLEY;;ST;256;85006;3735        |2024-12-28 21:54:21.000|202412|\n", "|20243612000970    |TM_20243612000970|<EMAIL>         |479214178|8155357045|;1216;;TILTON PARK;;DR;;61021;1450         |2024-12-26 02:40:34.000|202412|\n", "|20243363010056    |TM_20243363010056|null                            |null     |0000000000|;411;;RIDGES;;DR;;33838;4627               |2024-12-01 19:29:42.000|202412|\n", "|20243632017529    |TM_20243632017529|<EMAIL>      |null     |null      |;1211;;JACQUELINE;;ST;;76541;3770          |2024-12-28 22:12:09.000|202412|\n", "|20243402017100    |TM_20243402017100|<EMAIL>     |587534302|6018149810|;208;;CASTON;;AVE;;39648;3214              |2024-12-05 23:15:26.000|202412|\n", "|20243382018872    |TM_20243382018872|null                            |null     |null      |;1520;;38TH;;ST;2;92105;5947               |2024-12-03 23:54:52.000|202412|\n", "|20243532000593    |TM_20243532000593|<EMAIL>             |251337617|8542143197|;4;;CROMWELL;;ALY;B;29401;1973             |2024-12-18 00:22:47.000|202412|\n", "|20243632012783    |TM_20243632012783|null                            |null     |4709007200|;240;;PINNACLE;;WAY;;30143;5082            |2024-12-28 19:44:45.000|202412|\n", "|20243422004929    |TM_20243422004929|null                            |null     |7622108846|;1532;;ASKA;;RD;;30513;5702                |2024-12-07 14:14:30.000|202412|\n", "|20243363002044    |TM_20243363002044|null                            |null     |8085612484|;901;N;PECAN;;CT;20;97132;1960             |2024-12-01 02:11:29.000|202412|\n", "|20243453003979    |TM_20243453003979|null                            |null     |5732122009|;752;;BRANCH;;LN;;63089;2275               |2024-12-10 06:21:52.000|202412|\n", "|20243492017544    |TM_20243492017544|null                            |null     |3022528118|;5400;;HARBOUR POINTE;;BLVD;F201;98275;5165|2024-12-14 22:48:59.000|202412|\n", "|20243363008534    |TM_20243363008534|<EMAIL>         |null     |null      |;500;E;HARGETT;;ST;;27601;1553             |2024-12-01 18:23:57.000|202412|\n", "|20243633019084    |TM_20243633019084|null                            |null     |null      |;6105;;EYSTER;;AVE;;17362;8890             |2024-12-28 20:10:26.000|202412|\n", "|20243362018543    |TM_20243362018543|null                            |null     |0000000000|;126;;LA LOMA;;ST;;78852;1105              |2024-12-01 23:53:47.000|202412|\n", "|20243502014290    |TM_20243502014290|null                            |null     |8186219301|;10635;;REDMONT;;AVE;;91042;1531           |2024-12-15 22:13:17.000|202412|\n", "|20243612003396    |TM_20243612003396|<EMAIL>      |null     |null      |;55;;CHELSEA;;AVE;;07106;1213              |2024-12-26 14:15:30.000|202412|\n", "|20243573003552    |TM_20243573003552|<EMAIL>        |null     |null      |;2711;N;87TH;;AVE;;85037;3502              |2024-12-22 05:14:14.000|202412|\n", "|20243592011075    |TM_20243592011075|null                            |null     |4105709911|;2519;E;14TH;;ST;1;11235;3903              |2024-12-24 18:47:41.000|202412|\n", "|20243493009967    |TM_20243493009967|<EMAIL>            |null     |null      |;436;E;62ND;;ST;;60637;7180                |2024-12-14 13:21:19.000|202412|\n", "|20243533000685    |TM_20243533000685|<EMAIL>    |299941287|null      |;2368;;HARRYWOOD;;CT;;45239;6804           |2024-12-18 00:36:10.000|202412|\n", "|20243632012295    |TM_20243632012295|null                            |null     |3212434594|;1245;;SEABOLD;SW;RD;;32908;1813           |2024-12-28 19:25:52.000|202412|\n", "|20243513011027    |TM_20243513011027|null                            |null     |8433731462|;468;;MOORE;;RD;;29560;5525                |2024-12-16 17:09:08.000|202412|\n", "|20243452003347    |TM_20243452003347|<EMAIL>        |null     |null      |;2224;;PONDEROSA;;PL;F;47714;9323          |2024-12-10 04:21:33.000|202412|\n", "|20243422005245    |TM_20243422005245|null                            |null     |4109645786|;11219;;PEARTREE;;WAY;;21044;4339          |2024-12-07 15:02:13.000|202412|\n", "|20243632010996    |TM_20243632010996|null                            |null     |3092614805|;221;S;MISSOURI;;AVE;;61550;2719           |2024-12-28 18:42:30.000|202412|\n", "|20243453009472    |TM_20243453009472|null                            |null     |5705349632|;404;;CAMPSTEAD;;CIR;;18610;7511           |2024-12-10 18:09:13.000|202412|\n", "|20243532004724    |TM_20243532004724|<EMAIL>     |null     |null      |;2340;N;51ST;;ST;3;53210;2821              |2024-12-18 09:29:43.000|202412|\n", "|20243612011793    |TM_20243612011793|null                            |null     |3137705958|;9000;E;JEFFERSON;;AVE;;48214;4188         |2024-12-26 20:01:25.000|202412|\n", "|20243523010517    |TM_20243523010517|null                            |null     |9999999999|;342;;BURKE;;ST;;28601;7243                |2024-12-17 15:46:31.000|202412|\n", "|20243403009343    |TM_20243403009343|lind<PERSON><EMAIL>|296863248|3304323444|;52567;;STATE ROUTE 651;;;;43804;9520      |2024-12-05 18:02:51.000|202412|\n", "|20243562005152    |TM_20243562005152|<EMAIL>           |024709354|4136577886|;31;;HARLAN;;ST;;01056;3123                |2024-12-21 12:04:43.000|202412|\n", "|20243642004510    |TM_20243642004510|<EMAIL>          |null     |null      |;633;;JAMES;;ST;;46516;4529                |2024-12-29 12:20:42.000|202412|\n", "|20243462012635    |TM_20243462012635|null                            |null     |4156383827|;3402;;26TH;;ST;;94110;4506                |2024-12-11 20:40:05.000|202412|\n", "|20243462006890    |TM_20243462006890|null                            |640020473|8066809960|;400;;ROSEWOOD;;DR;;73069;6541             |2024-12-11 16:28:37.000|202412|\n", "|20243663004433    |TM_20243663004433|<EMAIL>           |226894282|8043124502|;900;;PINE;;AVE;A;23860;5017               |2024-12-31 07:22:02.000|202412|\n", "|20243462006883    |TM_20243462006883|<EMAIL>           |593667256|8502920246|;334;N;S;;ST;;32505;7933                   |2024-12-11 16:28:17.000|202412|\n", "|20243392010895    |TM_20243392010895|null                            |null     |7875073225|;15;;ELMWOOD;;DR;;07013;1241               |2024-12-04 18:53:34.000|202412|\n", "|20243553010526    |TM_20243553010526|<EMAIL>              |361920767|7738192627|;5342;W;KINZIE;;ST;;60644;1949             |2024-12-20 08:29:00.000|202412|\n", "|20243553025481    |TM_20243553025481|null                            |668240959|null      |;17202;NE;85TH;;PL;;98052;6638             |2024-12-20 22:56:52.000|202412|\n", "|20243432005908    |TM_20243432005908|null                            |111111111|6468417614|;1712;;HARTMAN;;ST;;11385;                 |2024-12-08 17:08:48.000|202412|\n", "|20243632011389    |TM_20243632011389|<EMAIL>       |null     |null      |;1115;;HUMPHREY;;ST;;71742;2611            |2024-12-28 18:53:56.000|202412|\n", "|20243443028651    |TM_20243443028651|null                            |null     |5029990924|;3272;;TAYLOR;;BLVD;;40215;2652            |2024-12-09 23:10:33.000|202412|\n", "|20243582015765    |TM_20243582015765|<EMAIL>            |595629664|4072979273|;6955;;COMPASS;;CT;;32810;3657             |2024-12-23 21:44:20.000|202412|\n", "|20243423020860    |TM_20243423020860|<EMAIL>          |365232463|8139660899|;3202;E;IDA;;ST;;33610;7843                |2024-12-07 22:00:34.000|202412|\n", "|20243652010321    |TM_20243652010321|null                            |null     |7162999443|;1334;;NIAGARA;;AVE;;14305;2746            |2024-12-30 19:44:30.000|202412|\n", "|20243502015634    |TM_20243502015634|<EMAIL>            |560151647|6027707070|;6550;N;47TH;;AVE;193;85301;4143           |2024-12-15 23:08:12.000|202412|\n", "|20243502012083    |TM_20243502012083|null                            |304822218|3172497252|;2417;;BLOOMINGTON;;DR;;89134;0436         |2024-12-15 20:43:41.000|202412|\n", "|20243363008689    |TM_20243363008689|null                            |null     |7086967450|;426;E;22ND;;ST;;60411;4309                |2024-12-01 18:28:27.000|202412|\n", "|20243553019604    |TM_20243553019604|null                            |204614773|2015396566|;2163;;HAVENTREE;;CT;;30043;5220           |2024-12-20 19:58:26.000|202412|\n", "|20243573003080    |TM_20243573003080|<EMAIL>            |null     |null      |;2921;;MAX;;DR;;70058;5613                 |2024-12-22 03:39:36.000|202412|\n", "|20243583012389    |TM_20243583012389|<EMAIL>              |913867500|9172457606|;11473;;DALIAN;;CT;;11356;1574             |2024-12-23 17:02:44.000|202412|\n", "|20243453017705    |TM_20243453017705|<EMAIL>   |617473764|9094046450|;1205;E;9TH;;ST;;91786;8124                |2024-12-10 23:38:07.000|202412|\n", "|20243523003010    |TM_20243523003010|<EMAIL>          |null     |null      |;4713;;WHITEHILL;;TRL;;75071;4457          |2024-12-17 03:21:06.000|202412|\n", "|20243502005788    |TM_20243502005788|<EMAIL>          |627200636|8173505746|;9109;;HOLLY;;RD;;76009;6244               |2024-12-15 15:56:55.000|202412|\n", "|20243533017434    |TM_20243533017434|<EMAIL>          |487159857|5026799886|;405;;LEXINGTON;;AVE;5;42701;1564          |2024-12-18 20:18:43.000|202412|\n", "|20243543011950    |TM_20243543011950|<EMAIL>       |null     |null      |;601;;BILL FRANCE;;BLVD;;32114;7409        |2024-12-19 15:48:09.000|202412|\n", "|20243623023884    |TM_20243623023884|<EMAIL>              |111111111|9564850820|;167;W;GARFIELD;;AVE;;83623;2329           |2024-12-27 23:14:23.000|202412|\n", "|20243543018665    |TM_20243543018665|null                            |null     |null      |;3604;E;SAN PEDRO;;PL;;85249;5282          |2024-12-19 20:18:14.000|202412|\n", "+------------------+-----------------+--------------------------------+---------+----------+-------------------------------------------+-----------------------+------+\n", "only showing top 100 rows\n", "\n"]}], "source": ["df_with_uids.select(\"APPLICATION_NUMBER\", \"BUID\", \"email_uid\", \"ssn_uid\", \"phone_uid\", \"addr_uid\", \"APPLICATIONDATE_GMT\", \"month\").show(truncate=False,n=100)"]}, {"cell_type": "code", "execution_count": 28, "id": "46269db7-394c-4f08-9e9b-75334e8abb48", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 26:====================================================> (254 + 6) / 260]"]}, {"name": "stdout", "output_type": "stream", "text": ["+------+-------+\n", "| month|  count|\n", "+------+-------+\n", "|202401| 901190|\n", "|202402| 922215|\n", "|202403| 915549|\n", "|202404| 891953|\n", "|202405| 912877|\n", "|202406| 930916|\n", "|202407| 985206|\n", "|202408|1016261|\n", "|202409|1068844|\n", "|202410|1016110|\n", "|202411|1016294|\n", "|202412|1087023|\n", "|202501|   1110|\n", "+------+-------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["df_with_uids.groupby(\"month\").count().orderBy(\"month\").show(n=100)"]}, {"cell_type": "code", "execution_count": 29, "id": "3d2b6172-8449-4f97-83e9-8998da703711", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["# df_with_uids.select(\"APPLICATION_NUMBER\", \"BUID\", \"email_uid\", \"ssn_uid\", \"phone_uid\", \"addr_uid\", \"APPLICATIONDATE_GMT\", \"month\") \\\n", "# .write.partitionBy(\"month\").mode(\"overwrite\").parquet(\"s3://expn-da-prd-innovationlabs/p/telco/attributes/uid/tmobile_202401_202412\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}