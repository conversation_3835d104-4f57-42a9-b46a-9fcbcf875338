{"cells": [{"cell_type": "code", "execution_count": 54, "id": "d5c6550b-ab64-4532-a33b-790c3c9b8f6a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3.9.23 | packaged by conda-forge | (main, Jun  4 2025, 17:57:12) \n", "[GCC 13.3.0]\n"]}], "source": ["# Config\n", "import os, sys, findspark\n", "spark_home= '/usr/lib/spark'\n", "findspark.init(spark_home)\n", "  \n", "print(sys.version)"]}, {"cell_type": "code", "execution_count": 55, "id": "e4877acc-cda3-43f6-a244-6e412238cb6f", "metadata": {}, "outputs": [], "source": ["os.environ['PYSPARK_PYTHON'] = \"/work/users/c21868e/.conda/envs/test/bin/python\"\n", " \n", "from pyspark import SparkConf\n", "from pyspark.sql import SparkSession\n", "sc = SparkConf() \\\n", "     .set(\"spark.app.name\", \"Telco\") \\\n", "     .set(\"spark.executor.memory\", \"24g\") \\\n", "     .set(\"spark.executor.cores\", \"4\") \\\n", "     .set(\"spark.executor.instances\", \"10\") \\\n", "     .set(\"spark.dynamicAllocation.enabled\", \"true\") \\\n", "     .set(\"spark.dynamicAllocation.maxExecutors\", \"20\") \\\n", "     .set(\"spark.driver.maxResultSize\", \"18g\") \\\n", "     .set(\"spark.sql.execution.arrow.enabled\", \"true\") \\\n", "     .set(\"spark.serializer\", \"org.apache.spark.serializer.KryoSerializer\") \\\n", "     .set(\"spark.kryoserializer.buffer.max\", \"512M\")\n", "  \n", "spark = SparkSession.builder.config(conf=sc).getOrCreate()"]}, {"cell_type": "code", "execution_count": 56, "id": "dae82a3c-8fa8-49e6-8fef-d182d5658634", "metadata": {}, "outputs": [], "source": ["sc = spark.sparkContext\n", "sc.setLogLevel(\"ERROR\")"]}, {"cell_type": "code", "execution_count": 57, "id": "555a8942-383d-4a8a-8d7b-b682766a3d5d", "metadata": {}, "outputs": [], "source": ["from pyspark.sql import Row\n", "from pyspark.sql import SparkSession\n", "from operator import add\n", "from pyspark.sql import functions as F\n", "from pyspark.sql import Window as W\n", "from pyspark.sql import types as T"]}, {"cell_type": "code", "execution_count": 58, "id": "e64bac3a-ff7b-4bc9-80d8-fab47f15cadd", "metadata": {}, "outputs": [{"data": {"text/plain": ["<module 'general_utils' from '/mnt/tmp/spark-ad034847-721f-4d6d-ae10-165647cb7666/userFiles-a1f172be-f592-4cd6-9f99-e547a9a6a19a/general_utils.py'>"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["import importlib\n", "spark.sparkContext.addPyFile(\"/work/users/c21868e/Telco/scripts/utils/attributes.py\")\n", "import attributes\n", "importlib.reload(attributes)\n", "\n", "spark.sparkContext.addPyFile(\"/work/users/c21868e/Telco/scripts/utils/general_utils.py\")\n", "import general_utils\n", "importlib.reload(general_utils)"]}, {"cell_type": "code", "execution_count": 59, "id": "2fc2b7cc-2edb-4061-9132-b6c13c9caaa1", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from datetime import datetime\n", "from itertools import chain\n", "from general_utils import log, check_distribution"]}, {"cell_type": "code", "execution_count": 60, "id": "de999248-029a-4e36-a6b3-f528e79d0682", "metadata": {}, "outputs": [], "source": ["REVIEW_FLAG_COLS = ['REVIEW_FLAG', 'REVIEW_PASS_FLAG', 'FRAUD_REVIEW_FLAG', 'FRAUD_REVIEW_PASS_FLAG']\n", "\n", "def get_review_flag_tm(df):\n", "    return df\\\n", "        .withColumn('REVIEW_FLAG', F.when(F.col('MR_FLAG') == '1', F.lit('Y')).otherwise(F.lit('N')))\\\n", "        .withColumn('REVIEW_PASS_FLAG', F.when(<PERSON><PERSON>col('MR_CLEARED') == '1', F.lit('Y'))\\\n", "                                         .when(F.col('MR_CLEARED') == '0', F.lit('N'))\\\n", "                                         .otherwise(F.lit(None)))\\\n", "        .withColumn('FRAUD_REVIEW_FLAG', F.lit(None).cast('string'))\\\n", "        .withColumn('FRAUD_REVIEW_PASS_FLAG', F.lit(None).cast('string'))\\\n", "        .select('APP_NUMBER', *REVIEW_FLAG_COLS)\n", "\n", "\n", "fraud_fillna_dict = {\n", "     'FPF_TAG': 0,\n", "     'TPF_TAG': 0,\n", "     'FRAUD_TAG': 0,\n", "     'FPD_TAG': 0,\n", "     'BAD_TAG': 0,\n", "     'OTHER_BAD_TAG': 0,\n", "     'GOOD_TAG': 0,\n", "     'LOSS_OR_BAL_AMT': 0,\n", "     'NO_USAGE': 0}\n", "\n", "def adjust_tags(df_app):\n", "    '''\n", "    Calculate pseudo-FPF and augmented FPF tag, adjust FRAUD and FPD tag using the pseudo-FPF tag\n", "    '''\n", "    return df_app.fillna(fraud_fillna_dict)\\\n", "                 .withColumn('PSEUDO_FPF_TAG', \n", "                                 F.when(F.col('client_name') != 'verizon', F.col('is_activated') * F.col('FPD_TAG') * F.col('NO_USAGE'))\\\n", "                                  .otherwise(F.col('FPF_TAG')))\\\n", "                 .withColumn('FPF_TAG', F.when(F.col('client_name') != 'verizon', F.col('FPF_TAG')).otherwise(F.lit(0)))\\\n", "                 .withColumnRenamed('FPF_TAG', 'ORIGINAL_FPF_TAG')\\\n", "                 .withColumnRenamed('FPD_TAG', 'ORIGINAL_FPD_TAG')\\\n", "                 .withColumn('FPF_TAG',((<PERSON>.col('ORIGINAL_FPF_TAG') == 1) | (F.col('PSEUDO_FPF_TAG') == 1)).cast('int'))\\\n", "                 .withColumn('FRAUD_TAG',((F.col('FRAUD_TAG') == 1) | (F.col('PSEUDO_FPF_TAG') == 1)).cast('int'))\\\n", "                 .withColumn('FPD_TAG', ((<PERSON><PERSON>col('ORIGINAL_FPD_TAG') == 1) & (<PERSON>.col('PSEUDO_FPF_TAG') == 0)).cast('int'))\n", "\n", "\n", "def calc_tag_stats(df_app, groupby=None, fillna=True):\n", "    agg_cols = [F.count(F.lit(1)).alias('num_app'),\n", "             F.sum('is_activated').alias('num_acct'),\n", "             F.sum(F.col('is_activated') * F.col('ORIGINAL_FPF_TAG')).alias('num_original_fpf'),\n", "             F.sum(F.col('is_activated') * F.col('PSEUDO_FPF_TAG')).alias('num_pseudo_fpf'),\n", "             F.sum(F.col('is_activated') * F.col('FPF_TAG')).alias('num_fpf'),\n", "             F.sum(F.col('is_activated') * F.col('TPF_TAG')).alias('num_tpf'),\n", "             F.sum(F.col('is_activated') * F.col('FRAUD_TAG')).alias('num_fraud'),\n", "             F.sum(F.col('is_activated') * F.col('ORIGINAL_FPD_TAG')).alias('num_original_fpd'),\n", "             F.sum(F.col('is_activated') * F.col('FPD_TAG')).alias('num_fpd'),\n", "             F.sum(F.col('is_activated') * F.col('BAD_TAG')).alias('num_bad'),\n", "             F.sum(F.col('is_activated') * F.col('OTHER_BAD_TAG')).alias('num_other_bad'),\n", "             F.sum(F.col('is_activated') * F.col('GOOD_TAG')).alias('num_good')]\n", "    \n", "    if fillna:\n", "        df_app = df_app.fillna(fraud_fillna_dict)\n", "    \n", "    if groupby is None:\n", "        df_app.select(*agg_cols).show()\n", "    else:\n", "        df_app.groupBy(groupby).agg(*agg_cols).sort(groupby).show(100)\n", "    \n", "    \n", "def calc_loss_stats(df_app, groupby=None, fillna=True):\n", "    agg_cols = [F.count(F.lit(1)).alias('num_app'),\n", "             F.sum('is_activated').alias('num_acct'),\n", "             F.sum(F.col('is_activated') * F.col('ORIGINAL_FPF_TAG') * F.col('LOSS_OR_BAL_AMT')).alias('original_fpf_loss'),\n", "             F.sum(F.col('is_activated') * F.col('PSEUDO_FPF_TAG') * F.col('LOSS_OR_BAL_AMT')).alias('pseudo_fpf_loss'),\n", "             F.sum(F.col('is_activated') * F.col('FPF_TAG') * F.col('LOSS_OR_BAL_AMT')).alias('fpf_loss'),\n", "             F.sum(F.col('is_activated') * F.col('TPF_TAG') * F.col('LOSS_OR_BAL_AMT')).alias('tpf_loss'),\n", "             F.sum(F.col('is_activated') * F.col('FRAUD_TAG') * F.col('LOSS_OR_BAL_AMT')).alias('fraud_loss'),\n", "             F.sum(F.col('is_activated') * F.col('ORIGINAL_FPD_TAG') * F.col('LOSS_OR_BAL_AMT')).alias('original_fpd_loss'),\n", "             F.sum(F.col('is_activated') * F.col('FPD_TAG') * F.col('LOSS_OR_BAL_AMT')).alias('fpd_loss'),\n", "             F.sum(F.col('is_activated') * F.col('BAD_TAG') * F.col('LOSS_OR_BAL_AMT')).alias('total_bad_loss'),\n", "             F.sum(F.col('is_activated') * F.col('OTHER_BAD_TAG') * F.col('LOSS_OR_BAL_AMT')).alias('other_bad_loss')]\n", "    \n", "    if fillna:\n", "        df_app = df_app.fillna(fraud_fillna_dict)\n", "    \n", "    if groupby is None:\n", "        df_app.select(*agg_cols).show()\n", "    else:\n", "        df_app.groupBy(groupby).agg(*agg_cols).sort(groupby).show(100)"]}, {"cell_type": "code", "execution_count": 61, "id": "6c38b748-4d8b-4834-9bb5-7ea808e231ae", "metadata": {}, "outputs": [], "source": ["## blacklist of non-personal phone and likely fake emails\n", "non_personal_phone_list = attributes.read_lines_from_text_file('/work/projects/telco/files/telco_non_personal_phone_list.txt')\n", "non_personal_email_list = attributes.read_lines_from_text_file('/work/projects/telco/files/telco_non_personal_email_list.txt')"]}, {"cell_type": "code", "execution_count": 62, "id": "aeb9c996-b698-4711-9638-e5b2b1175cdc", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["## Fixed formatting issue\n", "tm_app = spark.read.option(\"header\", \"true\").csv(\"s3://expn-da-prd-innovationlabs/p/telco/tmobile/raw/app_202401_202412/edlca6717_Experian File New_2024.csv\")"]}, {"cell_type": "code", "execution_count": 63, "id": "68085abf-b8a7-432c-9253-d3d9bc2447f3", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["root\n", " |-- CRID_ENCR: string (nullable = true)\n", " |-- ENTITYTYPE: string (nullable = true)\n", " |-- APP_TYPE: string (nullable = true)\n", " |-- IS_VALID: string (nullable = true)\n", " |-- SUB_APPTYPE: string (nullable = true)\n", " |-- APP_YYYYMM_GMT: string (nullable = true)\n", " |-- APPLICATION_NUMBER: string (nullable = true)\n", " |-- APPLICATIONDATE_GMT: string (nullable = true)\n", " |-- LAST_NAME: string (nullable = true)\n", " |-- FIRST_NAME: string (nullable = true)\n", " |-- MIDDLE_NAME: string (nullable = true)\n", " |-- SUFFIXNAME: string (nullable = true)\n", " |-- ADRESS_LINE1: string (nullable = true)\n", " |-- ADRESS_LINE2: string (nullable = true)\n", " |-- ADDRESS_ZIP: string (nullable = true)\n", " |-- ADDRESS_CITY: string (nullable = true)\n", " |-- ADDRESS_STATE: string (nullable = true)\n", " |-- DOB: string (nullable = true)\n", " |-- SSN: string (nullable = true)\n", " |-- IDENTITY_DOCUMENT_TYPE: string (nullable = true)\n", " |-- IDENTITY_DOCUMENT_NUMBER: string (nullable = true)\n", " |-- PHONE_NUMBER: string (nullable = true)\n", " |-- EMAIL: string (nullable = true)\n", " |-- IP: string (nullable = true)\n", " |-- BYOD: string (nullable = true)\n", " |-- SALES_CHANNEL: string (nullable = true)\n", " |-- SALES_CHANNEL_NAME: string (nullable = true)\n", " |-- SALES_CHANNEL_SEGMENT: string (nullable = true)\n", " |-- STORE_ADDRESS: string (nullable = true)\n", " |-- STORE_CITY: string (nullable = true)\n", " |-- STORE_STATE: string (nullable = true)\n", " |-- STORE_ZIP: string (nullable = true)\n", " |-- MR_FLAG_STRATEGY: string (nullable = true)\n", " |-- MR_FLAG: string (nullable = true)\n", " |-- MR_CLEARED: string (nullable = true)\n", " |-- ACTIVATIONFLAG: string (nullable = true)\n", " |-- HSI_FLAG: string (nullable = true)\n", " |-- ACCOUNT_NUMBER: string (nullable = true)\n", " |-- ACTIVATION_CHANNEL: string (nullable = true)\n", " |-- ACCOUNT_OPEN_DATETIME: string (nullable = true)\n", " |-- NUMBER_OF_LINES: string (nullable = true)\n", " |-- NUM_DEVICE_FINANCED: string (nullable = true)\n", " |-- TOT_FINANCING_AMT: string (nullable = true)\n", " |-- STATUS: string (nullable = true)\n", " |-- FRAUD: string (nullable = true)\n", " |-- FRAUD_TPF: string (nullable = true)\n", " |-- FRAUD_FPF: string (nullable = true)\n", " |-- FRAUD_DATE: string (nullable = true)\n", " |-- FRAUD_WO_AMT: string (nullable = true)\n", " |-- FPD: string (nullable = true)\n", " |-- FPD_DATE: string (nullable = true)\n", " |-- FPD_WO_AMT: string (nullable = true)\n", " |-- OTHER_WO: string (nullable = true)\n", " |-- OTHER_WO_DATE: string (nullable = true)\n", " |-- OTHER_WO_AMT: string (nullable = true)\n", " |-- NO_USAGE_NEW: string (nullable = true)\n", " |-- NO_USAGE_OLD: string (nullable = true)\n", "\n"]}], "source": ["tm_app.printSchema()"]}, {"cell_type": "code", "execution_count": 64, "id": "6ca5f68e-22ba-4b4b-8aa7-812694929bca", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 167:===================================================> (248 + 8) / 256]"]}, {"name": "stdout", "output_type": "stream", "text": ["+----------+-------+\n", "|SSN_Length|  count|\n", "+----------+-------+\n", "|      null|  46582|\n", "|         4|6307313|\n", "|         9|5311653|\n", "+----------+-------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["tm_app.groupBy(F.length(tm_app[\"SSN\"]).alias(\"SSN_Length\")).count().show()"]}, {"cell_type": "code", "execution_count": 65, "id": "e5f006d1-ca36-4cd1-9208-a37a4a5898a1", "metadata": {}, "outputs": [], "source": ["# Change: only keep the last 4 digits of SSN for T-Mobile\n", "tm_app = tm_app.withColumn(\"SSN\", <PERSON><PERSON>substring(<PERSON><PERSON>col(\"SSN\"), -4, 4))"]}, {"cell_type": "code", "execution_count": 66, "id": "04027fba-d08c-4bb8-9f96-85cacd4e9070", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 170:================================================>   (237 + 19) / 256]"]}, {"name": "stdout", "output_type": "stream", "text": ["+----------+--------+\n", "|SSN_Length|   count|\n", "+----------+--------+\n", "|      null|   46582|\n", "|         4|11618966|\n", "+----------+--------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["tm_app.groupBy(F.length(tm_app[\"SSN\"]).alias(\"SSN_Length\")).count().show()"]}, {"cell_type": "code", "execution_count": 67, "id": "a01b8f2e-36c9-4cbc-b56d-5d6a2e20800e", "metadata": {}, "outputs": [], "source": ["## map field names to be consistent with previous POV so as to minimize code change\n", "df_name_map = pd.read_csv('/work/projects/telco/files/config/TMO_catchup_field_name_map.csv')\n", "name_map_dict = dict(zip(df_name_map['new_name'], df_name_map['old_name']))\n", "\n", "for col in name_map_dict:\n", "    tm_app = tm_app.withColumnRenamed(col, name_map_dict[col])"]}, {"cell_type": "code", "execution_count": 68, "id": "f1c3fb42-f80d-4045-a460-efc82172c80b", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'APPLICATION_NUMBER': 'APP_NUMBER',\n", " 'ACCOUNT_NUMBER': 'ACCOUNT_NUMBER',\n", " 'ACTIVATIONFLAG': 'ACTIVATIONFLAG',\n", " 'SALES_CHANNEL': 'SALES_CHANNEL_CODE',\n", " 'SALES_CHANNEL_NAME': 'SALES_CHANNEL',\n", " 'FRAUD': 'FRAUD',\n", " 'FPD': 'FPD',\n", " 'FRAUD_WO_AMT': 'FRAUD_WO_AMT',\n", " 'FPD_WO_AMT': 'FPD_WO_AMT',\n", " 'MR_FLAG': 'MR_FLAG',\n", " 'MR_CLEARED': 'MR_CLEARED',\n", " 'APPLICATIONDATE_GMT': 'APPDATETIME_GMT',\n", " 'FIRST_NAME': 'FIRSTNAME',\n", " 'LAST_NAME': 'LASTNAME',\n", " 'MIDDLE_NAME': 'MIDDLENAME',\n", " 'ADRESS_LINE1': 'ADDRESSLINE1',\n", " 'ADRESS_LINE2': 'ADDRESSLINE2',\n", " 'ADDRESS_CITY': 'ADDRESSCITY',\n", " 'ADDRESS_STATE': 'ADDRESSSTATE',\n", " 'ADDRESS_ZIP': 'ZIP',\n", " 'SSN': 'SSN',\n", " 'DOB': 'DOB',\n", " 'PHONE_NUMBER': 'CONTACTPHONE',\n", " 'EMAIL': 'EMAILADDRESS',\n", " 'IDENTITY_DOCUMENT_TYPE': 'IDENTIFICATIONTYPE',\n", " 'IDENTITY_DOCUMENT_NUMBER': 'IDENTIFICATIONNUMBER',\n", " 'APP_TYPE': 'APPTYPE',\n", " 'STORE_ADDRESS': 'STORE_ADDRESS',\n", " 'STORE_CITY': 'STORE_CITY',\n", " 'STORE_STATE': 'STORE_STATE',\n", " 'STORE_ZIP': 'STORE_ZIP',\n", " 'FRAUD_TPF': 'TPF',\n", " 'FRAUD_FPF': 'FPF',\n", " 'OTHER_WO': 'WO_OTHERS',\n", " 'FRAUD_DATE': 'FRAUD_DATE',\n", " 'FPD_DATE': 'FPD_DATE',\n", " 'OTHER_WO_DATE': 'WO_OTHERS_DATE',\n", " 'NO_USAGE_NEW': 'NO_USAGE',\n", " 'ACCOUNT_OPEN_DATETIME': 'START_SERVICE_DATE',\n", " 'ACTIVATION_CHANNEL': 'ACTIVATION_CHANNEL',\n", " 'NUMBER_OF_LINES': 'NUM_LINES',\n", " 'NUM_DEVICE_FINANCED': 'DEVICE_FINANCED',\n", " 'TOT_FINANCING_AMT': 'AMT_FINANCED'}"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["name_map_dict"]}, {"cell_type": "code", "execution_count": 69, "id": "62476ef8-f32e-41c2-a078-cbbc236f7620", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"data": {"text/plain": ["********"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["tm_app.count()"]}, {"cell_type": "markdown", "id": "d98578e6-25b3-4fa6-ad49-b7df7ff00e38", "metadata": {}, "source": ["# Collect external data"]}, {"cell_type": "code", "execution_count": 70, "id": "9e6e343d-6cfb-4d44-b3db-b30cb7fd8aa2", "metadata": {}, "outputs": [], "source": ["# IDX_RETRO_PATH = 's3://expn-da-prd-innovationlabs/p/telco/attributes/idx_attrs/'\n", "TELCO_DATA_PATH = 's3://expn-da-prd-innovationlabs/p/telco/'"]}, {"cell_type": "code", "execution_count": 71, "id": "51d006bf-b87d-475b-85e0-cbb5c80903d2", "metadata": {}, "outputs": [], "source": ["# ! hadoop fs -ls s3://expn-da-prd-innovationlabs/p/telco/attributes/pin_uid_vantage/"]}, {"cell_type": "code", "execution_count": 72, "id": "d49104aa-93a8-4bd8-aaec-e93918453437", "metadata": {"scrolled": true}, "outputs": [], "source": ["# ## merge all external data by month\n", "month_start = '202401'\n", "month_end = '202412'\n", "for month in pd.date_range(start=month_start+'01', end=month_end+'01', freq='MS').strftime('%Y%m').tolist():\n", "    print(f'====== Gathering external information for month {month} ======')\n", "    # df_fcra_pin = spark.read.parquet(f'{TELCO_DATA_PATH}/FCRA_pinning/appid_pin_eid_fcra/{period}/month={month}')\n", "    df_ascend_pin = spark.read.parquet(f'{TELCO_DATA_PATH}/ascend_pinning/appid_pin_ap/tmobile_202401_202412/ts={month}')\n", "    df_uid = spark.read.parquet(f'{TELCO_DATA_PATH}/attributes/uid/tmobile_202401_202412/month={month}')\n", "    # Change: set all ssn_uid to None\n", "    df_uid = df_uid.withColumn(\"ssn_uid\", F.lit(None).cast(\"string\")) \n", "\n", "    df_ascend_pin.selectExpr(\n", "        'app_id as APP_NUMBER', \n", "        'cast(pin as string) as opin')\\\n", "    .join(\n", "        df_uid.select(\n", "            F.split('buid', '_')[1].alias('APP_NUMBER'), \n", "            'addr_uid',\n", "            'ssn_uid',  \n", "            'phone_uid', \n", "            'email_uid'),\n", "        on='APP_NUMBER')\\\n", "#     .write.mode('overwrite')\\\n", "#     .parquet(f\"{TELCO_DATA_PATH}/attributes/pin_uid_vantage/tmobile_202401_202412/month={month}\")"]}, {"cell_type": "code", "execution_count": 73, "id": "4e0ff645-197f-4dda-bbd2-395a4878a0c0", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["df_external = spark.read.parquet(f\"{TELCO_DATA_PATH}/attributes/pin_uid_vantage/tmobile_202401_202412/\")"]}, {"cell_type": "code", "execution_count": 74, "id": "529c4b4b-**************-8228f136ea1a", "metadata": {}, "outputs": [], "source": ["## mask non-personal phone and emails\n", "# df_external = \\\n", "#     df_external.withColumn(\n", "#         'phone_uid', F.when(F.col('phone_uid').isin(non_personal_phone_list), F.lit(None)).otherwise(F.col('phone_uid'))\n", "#     ).withColumn(\n", "#         'email_uid', F.when(F.col('email_uid').isin(non_personal_email_list), F.lit(None)).otherwise(F.col('email_uid'))\n", "# )"]}, {"cell_type": "code", "execution_count": 75, "id": "85b91c86-e679-4ebc-a486-cb818e6afcf2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"data": {"text/plain": ["[Row(APP_NUMBER='20243362000002', opin='405205327', addr_uid=';2217;;NORWOOD;;DR;;77630;2587', ssn_uid=None, phone_uid='4096597133', email_uid=None, month=202412),\n", " Row(APP_NUMBER='20243362000048', opin='66807396', addr_uid=';2708;;BRALORNE;;CT;;93309;8878', ssn_uid=None, phone_uid=None, email_uid=None, month=202412),\n", " Row(APP_NUMBER='20243362000074', opin=None, addr_uid=';7342;E;50TH;;PL;;74145;6804', ssn_uid=None, phone_uid='9188826757', email_uid=None, month=202412),\n", " Row(APP_NUMBER='20243362000127', opin=None, addr_uid=';561;;WOFFORD;;ST;;29301;1906', ssn_uid=None, phone_uid=None, email_uid='<EMAIL>', month=202412),\n", " Row(APP_NUMBER='20243362000213', opin=None, addr_uid=';10831;SW;57TH;;PL;;97219;6671', ssn_uid=None, phone_uid='9714177544', email_uid=None, month=202412)]"]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["df_external.take(5)"]}, {"cell_type": "code", "execution_count": 76, "id": "84059da5-c144-48f4-906a-fd9c2a728e28", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"name": "stdout", "output_type": "stream", "text": ["+------+-------+\n", "|month |count  |\n", "+------+-------+\n", "|202401|901190 |\n", "|202402|922215 |\n", "|202403|915549 |\n", "|202404|891953 |\n", "|202405|912877 |\n", "|202406|930916 |\n", "|202407|985206 |\n", "|202408|1016261|\n", "|202409|1068844|\n", "|202410|1016110|\n", "|202411|1016294|\n", "|202412|1087023|\n", "+------+-------+\n", "\n"]}], "source": ["check_distribution(df_external, 'month')"]}, {"cell_type": "code", "execution_count": 77, "id": "52b19634-8edc-4a6e-9fc5-87ddf409c210", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"data": {"text/plain": ["9229112"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["df_external.filter(<PERSON>.col(\"opin\").isNotNull()).count()"]}, {"cell_type": "markdown", "id": "da18cbea-8bc4-43d0-9292-d9fe489bf6e9", "metadata": {}, "source": ["# Application attributes"]}, {"cell_type": "code", "execution_count": 78, "id": "341ff785-deb4-4484-b8fa-9398fbf3bc95", "metadata": {}, "outputs": [], "source": ["state_abbr_dict = {\n", " 'Alabama': 'AL',\n", " 'Alaska': 'AK',\n", " 'Arizona': 'AZ',\n", " 'Arkansas': 'AR',\n", " 'American Samoa': 'AS',\n", " 'California': 'CA',\n", " 'Colorado': 'CO',\n", " 'Connecticut': 'CT',\n", " 'Delaware': 'DE',\n", " 'District of Columbia': 'DC',\n", " 'Florida': 'FL',\n", " 'Georgia': 'GA',\n", " 'Guam': 'GU',\n", " 'Hawaii': 'HI',\n", " 'Idaho': 'ID',\n", " 'Illinois': 'IL',\n", " 'Indiana': 'IN',\n", " 'Iowa': 'IA',\n", " 'Kansas': 'KS',\n", " 'Kentucky': 'KY',\n", " 'Louisiana': 'LA',\n", " 'Maine': 'ME',\n", " 'Maryland': '<PERSON>',\n", " 'Massachusetts': 'MA',\n", " 'Michigan': 'MI',\n", " 'Minnesota': 'MN',\n", " 'Mississippi': 'MS',\n", " 'Missouri': 'MO',\n", " 'Montana': 'MT',\n", " 'Nebraska': 'NE',\n", " 'Nevada': 'NV',\n", " 'New Hampshire': 'NH',\n", " 'New Jersey': 'NJ',\n", " 'New Mexico': 'NM',\n", " 'New York': 'NY',\n", " 'North Carolina': 'NC',\n", " 'North Dakota': 'ND',\n", " 'Northern Mariana Islands': 'MP',\n", " 'Ohio': 'OH',\n", " 'Oklahoma': 'OK',\n", " 'Oregon': 'OR',\n", " 'Pennsylvania': 'PA',\n", " 'Puerto Rico': 'PR',\n", " 'Rhode Island': 'RI',\n", " 'South Carolina': 'SC',\n", " 'South Dakota': 'SD',\n", " 'Tennessee': 'TN',\n", " 'Texas': 'TX',\n", " 'Trust Territories': 'TT',\n", " 'Utah': 'UT',\n", " 'Vermont': 'VT',\n", " 'Virginia': 'VA',\n", " 'Virgin Islands': 'VI',\n", " 'Washington': 'WA',\n", " 'West Virginia': 'WV',\n", " 'Wisconsin': 'WI',\n", " 'Wyoming': 'WY'}\n", "\n", "state_abbr_dict_spark = source_map = F.create_map(*chain.from_iterable((F.lit(k), F.lit(v)) for k, v in state_abbr_dict.items()))"]}, {"cell_type": "code", "execution_count": 79, "id": "303ecf50-8008-4560-8909-e5eb7937d6b8", "metadata": {}, "outputs": [], "source": ["originalChannel = \"\"\"Web\n", "TPR\n", "Retail\n", "Care\n", "Telesales\n", "Walmart\n", "Costco\n", "General_Business\n", "Dealer\n", "Value_Added_Reseller\n", "Venicom\n", "Other\"\"\"\n", "\n", "mappedChannel =\"\"\"WEB\n", "A_TPR\n", "A_RETAIL\n", "A_CARE\n", "A_TELESALE\n", "NULL\n", "NULL\n", "NULL\n", "NULL\n", "NULL\n", "NULL\n", "A_OTHER\"\"\"\n", "\n", "originalChannel = [x.strip() for x in originalChannel.split('\\n')]\n", "mappedChannel = [x.strip() for x in mappedChannel.split('\\n')]\n", "sales_channel_map = dict(zip(originalChannel, mappedChannel))\n", "\n", "def map_sales_channel(x):\n", "    if x is None:\n", "        return None\n", "    return sales_channel_map.get(x, 'A_OTHER')\n", "\n", "map_sales_channel_udf = F.udf(map_sales_channel, T.StringType())\n", "\n", "\n", "originalIdType = \"\"\"US Driver's License\n", "State ID\n", "US Passport\n", "Military ID\n", "Consular ID \n", "US Permanent Resident Card \n", "Visa\n", "Student Visa\n", "Federally Issued Disability ID\n", "Puerto Rico Voter ID \n", "Tribal Issued ID \n", "Foreign Passport \n", "Other\"\"\"\n", "\n", "mappedIdType = \"\"\"DRIVER_LICENSE\n", "STATE_ID\n", "PASSPORT\n", "MILITARY\n", "NULL\n", "NULL\n", "NULL\n", "NULL\n", "NULL\n", "NULL\n", "NULL\n", "NULL\n", "NULL\"\"\"\n", "\n", "originalIdType = [x.strip() for x in originalIdType.split('\\n')]\n", "mappedIdType = [x.strip() for x in mappedIdType.split('\\n')]\n", "id_type_map = dict(zip(originalIdType, mappedIdType))\n", "\n", "def map_id_type(x):\n", "    if x is None:\n", "        return None\n", "    return id_type_map.get(x, 'A_OTHER')\n", "\n", "map_id_type_udf = F.udf(map_id_type, T.StringType())\n", "\n", "tm_corporate_addr = ['1 Ravina Drive;Atlanta;GA;30346', '12920 SE 38th St 1st Floor Lab;Bellevue;WA;98006']"]}, {"cell_type": "code", "execution_count": 80, "id": "638780ef-e67d-4234-ab70-a1dd3bfb524e", "metadata": {}, "outputs": [], "source": ["def append_fraud_tag(df):\n", "    return df.withColumn('FRAUD_TYPE', F.when(F.col('FPF') == '1', F.lit('1'))\\\n", "                                        .when(F.col('TPF') == '1', F.lit('2'))\\\n", "                                        .when(F.col('FPD') == '1', F.lit('3'))\\\n", "                                        .when(F.col('WO_OTHERS') == '1', F.lit('4'))\\\n", "                                        .otherwise(F.lit('0')))\\\n", "             .withColumn('FPF_TAG', (<PERSON><PERSON>col('FRAUD_TYPE') == '1').cast('int'))\\\n", "             .withColumn('TPF_TAG', (<PERSON><PERSON>col('FRAUD_TYPE') == '2').cast('int'))\\\n", "             .withColumn('FRAUD_TAG', <PERSON><PERSON>col('FRAUD_TYPE').isin(['1','2']).astype('int'))\\\n", "             .withColumn('FPD_TAG', (<PERSON><PERSON>col('FRAUD_TYPE') == '3').astype('int'))\\\n", "             .withColumn('BAD_TAG', ((F.col('FRAUD_TAG') == 1) | (F.col('FPD_TAG') == 1)).cast('int'))\\\n", "             .withColumn('OTHER_BAD_TAG', (<PERSON><PERSON>col('FRAUD_TYPE') == '4').astype('int'))\\\n", "             .drop('FPF', 'TPF', 'FPD', 'WO_OTHERS')\n", "\n", "def convert_to_spark_datetime(dt_str, app_time_format='%Y-%m-%d %H:%M:%S.%f'):\n", "    dt = datetime.strptime(dt_str, app_time_format)\n", "    return dt.strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "convert_to_spark_datetime_udf = F.udf(convert_to_spark_datetime, T.StringType())"]}, {"cell_type": "code", "execution_count": 81, "id": "3c016651-9997-4877-9a43-7d406325202d", "metadata": {}, "outputs": [], "source": ["raw_fields_to_standard = ['APP_NUMBER', \n", "                          'LASTNAME', 'FIRSTNAME', \n", "                          'ADDRESSLINE1 as ADDR_LINE_1', 'ADDRESSLINE2 as ADDR_LINE_2', 'ZIP as ZIPCODE', 'ADDRESSCITY as CITY', 'ADDRESSSTATE as STATE',\n", "                          'STORE_ADDRESS as STORE_ADDR_LINE_1',  'cast(NULL as string) as STORE_ADDR_LINE_2',  'STORE_ZIP as STORE_ZIPCODE', 'STORE_CITY as STORE_CITY', 'STORE_STATE as store_state',\n", "                          'DOB', 'EMAILADDRESS as EMAIL',\n", "                          'cast(NUM_LINES as int) as NUM_LINES', \n", "                          'cast(DEVICE_FINANCED as int) as DEVICE_FINANCED', \n", "                          'cast(AMT_FINANCED as double) as AMT_FINANCED', \n", "                          'IDENTIFICATIONTYPE as ID_TYPE', 'SALES_CHANNEL as CHANNEL', \n", "                          'cast(NO_USAGE as int) as NO_USAGE']\n", "                        #   'addr_uid', 'phone_uid', 'email_uid', 'ssn_uid', 'eid', 'opin', 'cluster_score', 'vantage_v4_score']\n", "\n", "raw_fields_to_immediate = ['IS_VALID', 'HSI_FLAG', 'CONTACTPHONE', 'SSN', 'APPDATETIME_GMT', 'ACTIVATIONFLAG', 'FPF', 'TPF', 'FPD', 'WO_OTHERS', \n", "                           'cast(FRAUD_WO_AMT as double) as FRAUD_WO_AMT', 'cast(FPD_WO_AMT as double) as FPD_WO_AMT', 'cast(OTHER_WO_AMT as double) as OTHER_WO_AMT']\n", "\n", "store_addr_fields = ['STORE_ADDR_LINE_1', 'STORE_CITY', 'STORE_STATE', 'STORE_ZIPCODE']"]}, {"cell_type": "code", "execution_count": 82, "id": "a8d92ba7-412d-4ac8-9434-2ba210e50047", "metadata": {}, "outputs": [], "source": ["df_tm_mapped = tm_app\\\n", ".selectExpr(*raw_fields_to_standard, *raw_fields_to_immediate)\n", "\n", "df_tm_mapped = append_fraud_tag(df_tm_mapped)\\\n", ".withColumn(\n", "    'client_name', F.lit('tmobile')\n", ").withColumn(\n", "    'store_addr_valid', (~<PERSON><PERSON>col('CHANNEL').isin(['Web']) \\\n", "                      & (~F.concat_ws(';', *[F.trim(F.col(col)) for col in store_addr_fields]).isin(tm_corporate_addr))).cast('int')\n", ").withColumn(\n", "    'store_state', F.when(F.col('store_addr_valid') == 1, F.col('STORE_STATE')).otherwise(F.lit(''))\n", ").withColumn(\n", "    'STORE_ZIPCODE', F.when(F.col('store_addr_valid') == 1, F.substring(<PERSON>.col('STORE_ZIPCODE'), 1, 5)).otherwise(F.lit(''))\n", ").withColumn(\n", "    'home_state', state_abbr_dict_spark[F.col('STATE')]\n", ").withColumn(\n", "    'HOME_ZIPCODE', <PERSON><PERSON>substring(<PERSON><PERSON>col('ZIPCODE'), 1, 5)\n", ").withColumn(\n", "    'PHONE', F.when(F.length(F.col('CONTACTPHONE')) == 10, F.col('CONTACTPHONE')).otherwise(F.lit(None))\n", ").withColumn(\n", "    'SSN', F.when((F.length(F.col('SSN')) == 9) | (F.length(F.col('SSN')) == 4), F.col('SSN')).otherwise(F.lit(None))\n", ").withColumn(\n", "    'is_activated', (<PERSON><PERSON>col('ACTIVATIONFLAG') == '1').cast('int')\n", ").fillna({'is_activated': 0}\n", ").withColumn(\n", "    'LOSS_OR_BAL_AMT', F.when(F.col('FRAUD_TAG') == 1, F.col('FRAUD_WO_AMT'))\\\n", "                        .when(F.col('FPD_TAG') == 1, F.col('FPD_WO_AMT'))\\\n", "                        .when(F.col('OTHER_BAD_TAG') == 1, F.col('OTHER_WO_AMT'))\\\n", "                        .otherwise(F.lit(0))\n", ").withColumn(\n", "    'GOOD_TAG', F.expr(\"is_activated = 1 and FRAUD_TYPE = '0'\").cast('int')\n", ").withColumn(\n", "    'CHANNEL', map_sales_channel_udf(<PERSON><PERSON>col('CHANNEL'))\n", ").withColumn(\n", "    'ID_TYPE', map_id_type_udf(F.col('ID_TYPE'))\n", ").withColumn(\n", "    'app_gmt_time_str', convert_to_spark_datetime_udf(<PERSON><PERSON>col('APPDATETIME_GMT'))\n", ").withColumn(\n", "    'app_gmt_time', F.to_utc_timestamp(F.col('app_gmt_time_str'), 'GMT')\n", ").withColumn(\n", "    'ts', F.date_format('app_gmt_time', 'yyyyMM')\n", ").withColumn(\n", "    'app_local_time_str', attributes.parse_app_time_udf(\n", "        <PERSON><PERSON>col('APPDATETIME_GMT'),\n", "        <PERSON>.col('store_state'),\n", "        <PERSON>.col('home_state'),\n", "        F.lit('%Y-%m-%d %H:%M:%S.%f'),\n", "        F.lit('GMT')\n", "    )\n", ").withColumn(\n", "    'app_local_time', F.to_timestamp('app_local_time_str', 'yyyy-MM-dd HH:mm:ss')\n", ").withColumn(\n", "    'app_local_time', F.when(F.col('app_local_time').isNull(), F.col('app_gmt_time'))\\\n", "                       .otherwise(<PERSON>.col('app_local_time'))\n", ") ## when app_local_time cannot be determined due to missing both store and home address, just use the original app time (in CST)\n", "\n", "## Adjust FPF and FPD tags using no usage flag\n", "df_tm_mapped = adjust_tags(df_tm_mapped)"]}, {"cell_type": "code", "execution_count": 83, "id": "862e24d2-3b6c-467b-aed2-0cd804f6b155", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["root\n", " |-- APP_NUMBER: string (nullable = true)\n", " |-- LASTNAME: string (nullable = true)\n", " |-- FIRSTNAME: string (nullable = true)\n", " |-- ADDR_LINE_1: string (nullable = true)\n", " |-- ADDR_LINE_2: string (nullable = true)\n", " |-- ZIPCODE: string (nullable = true)\n", " |-- CITY: string (nullable = true)\n", " |-- STATE: string (nullable = true)\n", " |-- STORE_ADDR_LINE_1: string (nullable = true)\n", " |-- STORE_ADDR_LINE_2: string (nullable = true)\n", " |-- STORE_ZIPCODE: string (nullable = true)\n", " |-- STORE_CITY: string (nullable = true)\n", " |-- store_state: string (nullable = true)\n", " |-- DOB: string (nullable = true)\n", " |-- EMAIL: string (nullable = true)\n", " |-- NUM_LINES: integer (nullable = true)\n", " |-- DEVICE_FINANCED: integer (nullable = true)\n", " |-- AMT_FINANCED: double (nullable = true)\n", " |-- ID_TYPE: string (nullable = true)\n", " |-- CHANNEL: string (nullable = true)\n", " |-- NO_USAGE: integer (nullable = false)\n", " |-- IS_VALID: string (nullable = true)\n", " |-- HSI_FLAG: string (nullable = true)\n", " |-- CONTACTPHONE: string (nullable = true)\n", " |-- SSN: string (nullable = true)\n", " |-- APPDATETIME_GMT: string (nullable = true)\n", " |-- ACTIVATIONFLAG: string (nullable = true)\n", " |-- FRAUD_WO_AMT: double (nullable = true)\n", " |-- FPD_WO_AMT: double (nullable = true)\n", " |-- OTHER_WO_AMT: double (nullable = true)\n", " |-- FRAUD_TYPE: string (nullable = false)\n", " |-- ORIGINAL_FPF_TAG: integer (nullable = false)\n", " |-- TPF_TAG: integer (nullable = false)\n", " |-- FRAUD_TAG: integer (nullable = false)\n", " |-- ORIGINAL_FPD_TAG: integer (nullable = false)\n", " |-- BAD_TAG: integer (nullable = false)\n", " |-- OTHER_BAD_TAG: integer (nullable = false)\n", " |-- client_name: string (nullable = false)\n", " |-- store_addr_valid: integer (nullable = true)\n", " |-- home_state: string (nullable = true)\n", " |-- HOME_ZIPCODE: string (nullable = true)\n", " |-- PHONE: string (nullable = true)\n", " |-- is_activated: integer (nullable = false)\n", " |-- LOSS_OR_BAL_AMT: double (nullable = false)\n", " |-- GOOD_TAG: integer (nullable = false)\n", " |-- app_gmt_time_str: string (nullable = true)\n", " |-- app_gmt_time: timestamp (nullable = true)\n", " |-- ts: string (nullable = true)\n", " |-- app_local_time_str: string (nullable = true)\n", " |-- app_local_time: timestamp (nullable = true)\n", " |-- PSEUDO_FPF_TAG: integer (nullable = false)\n", " |-- FPF_TAG: integer (nullable = false)\n", " |-- FPD_TAG: integer (nullable = false)\n", "\n"]}], "source": ["df_tm_mapped.printSchema()"]}, {"cell_type": "code", "execution_count": 84, "id": "4555207c-ceb4-4ed6-8a98-2b51d41e734e", "metadata": {}, "outputs": [], "source": ["BASE_TIMESTAMP = '01JAN1970:00:00:00'\n", "\n", "df_tm_attr = df_tm_mapped\\\n", "    .withColumn(\n", "        'app_date', F.to_date('app_local_time')\n", "    ).withColumn(\n", "        'month', F.date_format('app_date', 'yyyyMM')\n", "    ).withColumn(\n", "        'app_datetime_unix', F.unix_timestamp('app_gmt_time').cast('long') - F.to_timestamp(F.lit(BASE_TIMESTAMP), 'ddMMMyyyy:HH:mm:ss').cast('long')\n", "    ).withColumn(\n", "        'day_of_week', attributes.day_of_week_udf('app_local_time')\n", "    ).withColumn(\n", "        'time_of_day', attributes.part_of_day_udf('app_local_time')\n", "    ).withColumn(\n", "        'during_office_hour', attributes.during_office_hour_udf('app_local_time')\n", "    ).withColumn(\n", "        'sales_channel', attributes.sales_channel_updated_udf('CHANNEL')\n", "    ).withColumn(\n", "        'has_ssn', F.when(F.length(F.col('SSN')) == 9, F.lit(1))\\\n", "                    .when(F.length(F.col('SSN')) == 4, F.lit(2))\\\n", "                    .otherwise(F.lit(0))\n", "    ).withColumn(\n", "        'has_dob', (<PERSON><PERSON>col('DOB').isNotNull() & (<PERSON><PERSON>trim(F.col('DOB')) != '')).cast('int')\n", "    ).withColumn(\n", "        'has_phone', (<PERSON>.col('PHONE').isNotNull() & (<PERSON><PERSON>trim(F.col('PHONE')) != '')).cast('int')\n", "    ).withColumn(\n", "        'has_email', (<PERSON><PERSON>col('EMAIL').isNotNull() & (<PERSON><PERSON>trim(F.col('EMAIL')) != '')).cast('int')\n", "    ).withColumn(\n", "        'id_type', attributes.id_type_updated_udf(F.col('ID_TYPE'))\n", "    ).withColumn(\n", "        'has_id', (<PERSON>.col('id_type') >= 0).cast('int')\n", "    ).withColumn(\n", "        'portin_ind', F<PERSON>lit(None).cast('int')\n", "    ).withColumn(\n", "        'home_zip_store_zip_match', attributes.zipcode_match_udf(<PERSON><PERSON>col('HOME_ZIPCODE'), <PERSON><PERSON>col('STORE_ZIPCODE')) # verizon: use raw home zipcode\n", "    )"]}, {"cell_type": "code", "execution_count": 88, "id": "3bac75de-fb3a-4d33-be9e-dae40e0b39bc", "metadata": {}, "outputs": [], "source": ["## Remove 202402 data that has no name info (46568 records)\n", "df_tm_attr = df_tm_attr.filter(\n", "    ~(\n", "        (<PERSON>.col(\"ts\") == \"202402\") &\n", "        <PERSON><PERSON>col(\"LASTNAME\").isNull() &\n", "        <PERSON><PERSON>col(\"FIRSTNAME\").isNull()\n", "    )\n", ")"]}, {"cell_type": "markdown", "id": "14581fc0-fe54-465d-b89a-53d971d23b54", "metadata": {}, "source": ["# Append additional information\n", "* review flags, BYOD flag etc.\n", "* external data\n", "* invalid application skip flag"]}, {"cell_type": "code", "execution_count": 89, "id": "c44ea2b6-8d42-46d9-a7b5-16f9380b31be", "metadata": {}, "outputs": [], "source": ["df_tm_review_flags = get_review_flag_tm(tm_app.withColumnRenamed('CUSTOMER_ID', 'APP_NUMBER'))\n", "df_tm_byod = tm_app.select(<PERSON><PERSON>col('APP_NUMBER'), F.lit(None).cast('string').alias('BYOD_FLAG'))"]}, {"cell_type": "code", "execution_count": 90, "id": "46e841bf-6c63-41da-b5fc-cd79bf59a8a7", "metadata": {}, "outputs": [], "source": ["# Change period\n", "df_tm_joined = \\\n", "    df_tm_attr\\\n", "    .filter(\"ts >= 202401 and ts <= 202412\")\\\n", "    .join(df_tm_review_flags, 'APP_NUMBER')\\\n", "    .join(df_tm_byod, 'APP_NUMBER')\\\n", "    .join(df_external.drop('month'), 'APP_NUMBER')"]}, {"cell_type": "code", "execution_count": 91, "id": "b913cfdf-7b84-496d-aeeb-687b77ab7ba6", "metadata": {}, "outputs": [], "source": ["person_fields = [F.col('LASTNAME'),\n", " <PERSON><PERSON>col('FIRSTNAME'),\n", " <PERSON><PERSON>col('ADDR_LINE_1'),\n", " <PERSON><PERSON>col('ZIPCODE'),\n", " <PERSON><PERSON>col('DOB')]"]}, {"cell_type": "code", "execution_count": 92, "id": "cb0c29ec-4b76-404a-94b5-e1e6619d7354", "metadata": {}, "outputs": [], "source": ["## invalid app skip flag\n", "df_app_attr = df_tm_joined\\\n", "    .withColumn('consumer', F.concat_ws('|', *person_fields))\\\n", "    .withColumn('is_valid', (<PERSON><PERSON>col('IS_VALID') == '1').cast('int'))\\\n", "    .withColumn('has_full_ssn', (F.length(F.col('SSN')) == 9).cast('int'))\\\n", "    .withColumn('is_follow_up', F.col('is_valid') * F.col('has_full_ssn'))\\\n", "    .fillna({'is_valid': 0, 'has_full_ssn': 0})\\\n", "    .drop('has_full_ssn', 'is_follow_up')\\\n", "    .withColumn('other_info', <PERSON><PERSON>struct(F.col('is_valid'), <PERSON><PERSON>col('HSI_FLAG')))"]}, {"cell_type": "code", "execution_count": 93, "id": "3e64337e-31f2-4869-b2e9-d87a81e51819", "metadata": {}, "outputs": [], "source": ["output_cols = \"\"\"APP_NUMBER\n", "opin\n", "ts\n", "app_local_time\n", "app_gmt_time\n", "app_date\n", "month\n", "app_datetime_unix\n", "day_of_week\n", "time_of_day\n", "during_office_hour\n", "sales_channel\n", "id_type\n", "has_ssn\n", "has_dob\n", "has_phone\n", "has_email\n", "has_id\n", "home_zip_store_zip_match\n", "portin_ind\n", "is_activated\n", "NUM_LINES\n", "DEVICE_FINANCED\n", "AMT_FINANCED\n", "ORIGINAL_FPF_TAG\n", "ORIGINAL_FPD_TAG\n", "PSEUDO_FPF_TAG\n", "FPF_TAG\n", "TPF_TAG\n", "FRAUD_TAG\n", "FPD_TAG\n", "BAD_TAG\n", "OTHER_BAD_TAG\n", "LOSS_OR_BAL_AMT\n", "GOOD_TAG\n", "NO_USAGE\n", "addr_uid\n", "ssn_uid\n", "phone_uid\n", "email_uid\n", "REVIEW_FLAG\n", "REVIEW_PASS_FLAG\n", "FRAUD_REVIEW_FLAG\n", "FRAUD_REVIEW_PASS_FLAG\n", "BYOD_FLAG\n", "home_state\n", "client_name\n", "other_info\n", "INVOLUNTARY_TOTAL_BALANCE\"\"\".split('\\n')"]}, {"cell_type": "code", "execution_count": 94, "id": "*************-4ac0-9f60-06ec7420e3b3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["root\n", " |-- APP_NUMBER: string (nullable = true)\n", " |-- LASTNAME: string (nullable = true)\n", " |-- FIRSTNAME: string (nullable = true)\n", " |-- ADDR_LINE_1: string (nullable = true)\n", " |-- ADDR_LINE_2: string (nullable = true)\n", " |-- ZIPCODE: string (nullable = true)\n", " |-- CITY: string (nullable = true)\n", " |-- STATE: string (nullable = true)\n", " |-- STORE_ADDR_LINE_1: string (nullable = true)\n", " |-- STORE_ADDR_LINE_2: string (nullable = true)\n", " |-- STORE_ZIPCODE: string (nullable = true)\n", " |-- STORE_CITY: string (nullable = true)\n", " |-- store_state: string (nullable = true)\n", " |-- DOB: string (nullable = true)\n", " |-- EMAIL: string (nullable = true)\n", " |-- NUM_LINES: integer (nullable = true)\n", " |-- DEVICE_FINANCED: integer (nullable = true)\n", " |-- AMT_FINANCED: double (nullable = true)\n", " |-- id_type: integer (nullable = true)\n", " |-- CHANNEL: string (nullable = true)\n", " |-- NO_USAGE: integer (nullable = false)\n", " |-- is_valid: integer (nullable = false)\n", " |-- HSI_FLAG: string (nullable = true)\n", " |-- CONTACTPHONE: string (nullable = true)\n", " |-- SSN: string (nullable = true)\n", " |-- APPDATETIME_GMT: string (nullable = true)\n", " |-- ACTIVATIONFLAG: string (nullable = true)\n", " |-- FRAUD_WO_AMT: double (nullable = true)\n", " |-- FPD_WO_AMT: double (nullable = true)\n", " |-- OTHER_WO_AMT: double (nullable = true)\n", " |-- FRAUD_TYPE: string (nullable = false)\n", " |-- ORIGINAL_FPF_TAG: integer (nullable = false)\n", " |-- TPF_TAG: integer (nullable = false)\n", " |-- FRAUD_TAG: integer (nullable = false)\n", " |-- ORIGINAL_FPD_TAG: integer (nullable = false)\n", " |-- BAD_TAG: integer (nullable = false)\n", " |-- OTHER_BAD_TAG: integer (nullable = false)\n", " |-- client_name: string (nullable = false)\n", " |-- store_addr_valid: integer (nullable = true)\n", " |-- home_state: string (nullable = true)\n", " |-- HOME_ZIPCODE: string (nullable = true)\n", " |-- PHONE: string (nullable = true)\n", " |-- is_activated: integer (nullable = false)\n", " |-- LOSS_OR_BAL_AMT: double (nullable = false)\n", " |-- GOOD_TAG: integer (nullable = false)\n", " |-- app_gmt_time_str: string (nullable = true)\n", " |-- app_gmt_time: timestamp (nullable = true)\n", " |-- ts: string (nullable = true)\n", " |-- app_local_time_str: string (nullable = true)\n", " |-- app_local_time: timestamp (nullable = true)\n", " |-- PSEUDO_FPF_TAG: integer (nullable = false)\n", " |-- FPF_TAG: integer (nullable = false)\n", " |-- FPD_TAG: integer (nullable = false)\n", " |-- app_date: date (nullable = true)\n", " |-- month: string (nullable = true)\n", " |-- app_datetime_unix: long (nullable = true)\n", " |-- day_of_week: integer (nullable = true)\n", " |-- time_of_day: integer (nullable = true)\n", " |-- during_office_hour: integer (nullable = true)\n", " |-- sales_channel: integer (nullable = true)\n", " |-- has_ssn: integer (nullable = false)\n", " |-- has_dob: integer (nullable = true)\n", " |-- has_phone: integer (nullable = true)\n", " |-- has_email: integer (nullable = true)\n", " |-- has_id: integer (nullable = true)\n", " |-- portin_ind: integer (nullable = true)\n", " |-- home_zip_store_zip_match: integer (nullable = true)\n", " |-- REVIEW_FLAG: string (nullable = false)\n", " |-- REVIEW_PASS_FLAG: string (nullable = true)\n", " |-- FRAUD_REVIEW_FLAG: string (nullable = true)\n", " |-- FRAUD_REVIEW_PASS_FLAG: string (nullable = true)\n", " |-- BYOD_FLAG: string (nullable = true)\n", " |-- opin: string (nullable = true)\n", " |-- addr_uid: string (nullable = true)\n", " |-- ssn_uid: string (nullable = true)\n", " |-- phone_uid: string (nullable = true)\n", " |-- email_uid: string (nullable = true)\n", " |-- consumer: string (nullable = false)\n", " |-- other_info: struct (nullable = false)\n", " |    |-- is_valid: integer (nullable = false)\n", " |    |-- HSI_FLAG: string (nullable = true)\n", "\n"]}], "source": ["df_app_attr.printSchema()"]}, {"cell_type": "code", "execution_count": 95, "id": "ba6e9d76-a1b0-4439-a095-0e29222a39d9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                ]"]}, {"data": {"text/plain": ["9229112"]}, "execution_count": 95, "metadata": {}, "output_type": "execute_result"}], "source": ["df_app_attr.filter(F.col(\"opin\").isNotNull()).count()"]}, {"cell_type": "code", "execution_count": 96, "id": "c2d3ae93-527e-4dbb-b34d-cff3ebd8f73d", "metadata": {}, "outputs": [], "source": ["df_app_attr = df_app_attr.withColumn(\"INVOLUNTARY_TOTAL_BALANCE\", F.lit(None).cast(T.DoubleType()))"]}, {"cell_type": "code", "execution_count": 97, "id": "5714a134-6dc5-4e66-a7dd-d87a9ed8339f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["root\n", " |-- APP_NUMBER: string (nullable = true)\n", " |-- opin: string (nullable = true)\n", " |-- ts: string (nullable = true)\n", " |-- app_local_time: timestamp (nullable = true)\n", " |-- app_gmt_time: timestamp (nullable = true)\n", " |-- app_date: date (nullable = true)\n", " |-- month: string (nullable = true)\n", " |-- app_datetime_unix: long (nullable = true)\n", " |-- day_of_week: integer (nullable = true)\n", " |-- time_of_day: integer (nullable = true)\n", " |-- during_office_hour: integer (nullable = true)\n", " |-- sales_channel: integer (nullable = true)\n", " |-- id_type: integer (nullable = true)\n", " |-- has_ssn: integer (nullable = false)\n", " |-- has_dob: integer (nullable = true)\n", " |-- has_phone: integer (nullable = true)\n", " |-- has_email: integer (nullable = true)\n", " |-- has_id: integer (nullable = true)\n", " |-- home_zip_store_zip_match: integer (nullable = true)\n", " |-- portin_ind: integer (nullable = true)\n", " |-- is_activated: integer (nullable = false)\n", " |-- NUM_LINES: integer (nullable = true)\n", " |-- DEVICE_FINANCED: integer (nullable = true)\n", " |-- AMT_FINANCED: double (nullable = true)\n", " |-- ORIGINAL_FPF_TAG: integer (nullable = false)\n", " |-- ORIGINAL_FPD_TAG: integer (nullable = false)\n", " |-- PSEUDO_FPF_TAG: integer (nullable = false)\n", " |-- FPF_TAG: integer (nullable = false)\n", " |-- TPF_TAG: integer (nullable = false)\n", " |-- FRAUD_TAG: integer (nullable = false)\n", " |-- FPD_TAG: integer (nullable = false)\n", " |-- BAD_TAG: integer (nullable = false)\n", " |-- OTHER_BAD_TAG: integer (nullable = false)\n", " |-- LOSS_OR_BAL_AMT: double (nullable = false)\n", " |-- GOOD_TAG: integer (nullable = false)\n", " |-- NO_USAGE: integer (nullable = false)\n", " |-- addr_uid: string (nullable = true)\n", " |-- ssn_uid: string (nullable = true)\n", " |-- phone_uid: string (nullable = true)\n", " |-- email_uid: string (nullable = true)\n", " |-- REVIEW_FLAG: string (nullable = false)\n", " |-- REVIEW_PASS_FLAG: string (nullable = true)\n", " |-- FRAUD_REVIEW_FLAG: string (nullable = true)\n", " |-- FRAUD_REVIEW_PASS_FLAG: string (nullable = true)\n", " |-- BYOD_FLAG: string (nullable = true)\n", " |-- home_state: string (nullable = true)\n", " |-- client_name: string (nullable = false)\n", " |-- other_info: struct (nullable = false)\n", " |    |-- is_valid: integer (nullable = false)\n", " |    |-- HSI_FLAG: string (nullable = true)\n", " |-- INVOLUNTARY_TOTAL_BALANCE: double (nullable = true)\n", "\n"]}], "source": ["df_app_attr.select(output_cols).printSchema()"]}, {"cell_type": "code", "execution_count": 98, "id": "926938a2-aa1e-4450-960b-63db7d87a1d3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                ]"]}], "source": ["df_app_attr.select(output_cols).write.partitionBy('ts').mode('overwrite')\\\n", "    .parquet('s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202401_202412')"]}, {"cell_type": "code", "execution_count": 45, "id": "e8e20310-a585-4611-8114-119d119b7dee", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 60:====================================================>   (28 + 2) / 30]"]}, {"name": "stdout", "output_type": "stream", "text": ["+--------+-------+\n", "|is_valid|  count|\n", "+--------+-------+\n", "|       1|9063908|\n", "|       0|2600530|\n", "+--------+-------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["df_app_attr.groupBy(F.col(\"other_info.is_valid\")).count().show()"]}, {"cell_type": "markdown", "id": "f51976da-fb61-4629-8e7e-f0a6b2416f02", "metadata": {}, "source": ["# Check application attributes"]}, {"cell_type": "code", "execution_count": 99, "id": "1bbfcf67-99e4-4655-8638-636dd15083f5", "metadata": {}, "outputs": [], "source": ["app_attr = [\n", "    'sales_channel',\n", "    'id_type',\n", "    'day_of_week',\n", "    'time_of_day',\n", "    'during_office_hour',\n", "    'has_ssn',\n", "    'has_dob',\n", "    'has_phone',\n", "    'has_email',\n", "    'has_id',\n", "    'portin_ind',\n", "    'home_zip_store_zip_match',\n", "    'is_activated'\n", "]\n", "\n", "acct_cols = [\n", "    'ORIGINAL_FPF_TAG',\n", "    'ORIGINAL_FPD_TAG',\n", "    'PSEUDO_FPF_TAG',\n", "    'FPF_TAG',\n", "    'TPF_TAG',\n", "    'FRAUD_TAG',\n", "    'FPD_TAG',\n", "    'BAD_TAG',\n", "    'OTHER_BAD_TAG',\n", "    'GOOD_TAG'\n", "]\n", "\n", "tag_fillna = {col: 0 for col in acct_cols}"]}, {"cell_type": "code", "execution_count": 100, "id": "8c1b3046-6fe1-4af5-8a3d-f7a08ebf1a03", "metadata": {}, "outputs": [], "source": ["df_all = spark.read.parquet('s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202401_202412')"]}, {"cell_type": "code", "execution_count": 101, "id": "a3b576fe-d623-4e08-83a9-aa83cd4ea51c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 215:==============================================>     (241 + 28) / 269]"]}, {"name": "stdout", "output_type": "stream", "text": ["+------+-------+\n", "|ts    |count  |\n", "+------+-------+\n", "|202401|901190 |\n", "|202402|875647 |\n", "|202403|915549 |\n", "|202404|891953 |\n", "|202405|912877 |\n", "|202406|930916 |\n", "|202407|985206 |\n", "|202408|1016261|\n", "|202409|1068844|\n", "|202410|1016110|\n", "|202411|1016294|\n", "|202412|1087023|\n", "+------+-------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["check_distribution(df_all, 'ts')"]}, {"cell_type": "code", "execution_count": 103, "id": "73620097-dc51-472d-9f47-cdf4097b8ffb", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"data": {"text/plain": ["(9229112, 11617870)"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["df_all.filter(<PERSON>.col(\"opin\").isNotNull()).count(), df_all.count()"]}, {"cell_type": "code", "execution_count": 104, "id": "cefffad8-caae-4180-83a0-db3a7b855916", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"name": "stdout", "output_type": "stream", "text": ["+-------------+-------+------+\n", "|sales_channel|count  |pct   |\n", "+-------------+-------+------+\n", "|-1           |515556 |0.0444|\n", "|0            |4430682|0.3814|\n", "|1            |1752417|0.1508|\n", "|2            |2929951|0.2522|\n", "|3            |647510 |0.0557|\n", "|4            |1336300|0.115 |\n", "|5            |5454   |5.0E-4|\n", "+-------------+-------+------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"name": "stdout", "output_type": "stream", "text": ["+-------+-------+------+\n", "|id_type|count  |pct   |\n", "+-------+-------+------+\n", "|-1     |7013210|0.6037|\n", "|0      |3355527|0.2888|\n", "|1      |1113073|0.0958|\n", "|2      |110298 |0.0095|\n", "|3      |25762  |0.0022|\n", "+-------+-------+------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"name": "stdout", "output_type": "stream", "text": ["+-----------+-------+------+\n", "|day_of_week|count  |pct   |\n", "+-----------+-------+------+\n", "|0          |1712915|0.1474|\n", "|1          |1711437|0.1473|\n", "|2          |1648574|0.1419|\n", "|3          |1671276|0.1439|\n", "|4          |1810647|0.1559|\n", "|5          |1739060|0.1497|\n", "|6          |1323961|0.114 |\n", "+-----------+-------+------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"name": "stdout", "output_type": "stream", "text": ["+-----------+-------+------+\n", "|time_of_day|count  |pct   |\n", "+-----------+-------+------+\n", "|1          |440693 |0.0379|\n", "|2          |367442 |0.0316|\n", "|3          |2160023|0.1859|\n", "|4          |4179506|0.3597|\n", "|5          |3431162|0.2953|\n", "|6          |1039044|0.0894|\n", "+-----------+-------+------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"name": "stdout", "output_type": "stream", "text": ["+------------------+-------+------+\n", "|during_office_hour|count  |pct   |\n", "+------------------+-------+------+\n", "|0                 |2024225|0.1742|\n", "|1                 |9593645|0.8258|\n", "+------------------+-------+------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"name": "stdout", "output_type": "stream", "text": ["+-------+--------+---+\n", "|has_ssn|count   |pct|\n", "+-------+--------+---+\n", "|0      |14      |0.0|\n", "|2      |11617856|1.0|\n", "+-------+--------+---+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"name": "stdout", "output_type": "stream", "text": ["+-------+--------+---+\n", "|has_dob|count   |pct|\n", "+-------+--------+---+\n", "|0      |14      |0.0|\n", "|1      |11617856|1.0|\n", "+-------+--------+---+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"name": "stdout", "output_type": "stream", "text": ["+---------+-------+-----+\n", "|has_phone|count  |pct  |\n", "+---------+-------+-----+\n", "|0        |2103046|0.181|\n", "|1        |9514824|0.819|\n", "+---------+-------+-----+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"name": "stdout", "output_type": "stream", "text": ["+---------+-------+------+\n", "|has_email|count  |pct   |\n", "+---------+-------+------+\n", "|0        |5925781|0.5101|\n", "|1        |5692089|0.4899|\n", "+---------+-------+------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"name": "stdout", "output_type": "stream", "text": ["+------+-------+------+\n", "|has_id|count  |pct   |\n", "+------+-------+------+\n", "|0     |7013210|0.6037|\n", "|1     |4604660|0.3963|\n", "+------+-------+------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"name": "stdout", "output_type": "stream", "text": ["+----------+--------+---+\n", "|portin_ind|count   |pct|\n", "+----------+--------+---+\n", "|null      |11617870|1.0|\n", "+----------+--------+---+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}, {"name": "stdout", "output_type": "stream", "text": ["+------------------------+-------+------+\n", "|home_zip_store_zip_match|count  |pct   |\n", "+------------------------+-------+------+\n", "|1                       |6666598|0.5738|\n", "|2                       |15     |0.0   |\n", "|3                       |1268099|0.1092|\n", "|4                       |2296990|0.1977|\n", "|5                       |1386168|0.1193|\n", "+------------------------+-------+------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Stage 299:===============================================>    (246 + 23) / 269]"]}, {"name": "stdout", "output_type": "stream", "text": ["+------------+-------+------+\n", "|is_activated|count  |pct   |\n", "+------------+-------+------+\n", "|0           |6514894|0.5608|\n", "|1           |5102976|0.4392|\n", "+------------+-------+------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["## after appending additional information\n", "for col in app_attr:\n", "    check_distribution(df_all, col, add_pct=True)"]}, {"cell_type": "code", "execution_count": 105, "id": "59e2f797-1eac-4e72-be2b-e94216fc36a1", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 302:=================================================>  (254 + 15) / 269]"]}, {"name": "stdout", "output_type": "stream", "text": ["+-------------+-------+--------+----------------+--------------+-------+-------+---------+----------------+-------+-------+-------------+--------+\n", "|sales_channel|num_app|num_acct|num_original_fpf|num_pseudo_fpf|num_fpf|num_tpf|num_fraud|num_original_fpd|num_fpd|num_bad|num_other_bad|num_good|\n", "+-------------+-------+--------+----------------+--------------+-------+-------+---------+----------------+-------+-------+-------------+--------+\n", "|           -1| 515556|  289246|              41|          3560|   3601|   2810|     6411|           50670|  47110|  53521|        50766|  184959|\n", "|            0|4430682|  869432|            1643|          8999|  10642|  14825|    25467|          106845|  97846| 123313|       171996|  574123|\n", "|            1|1752417| 1054211|            1419|          8097|   9516|   8148|    17664|           77100|  69003|  86667|       142292|  825252|\n", "|            2|2929951| 1764654|            2043|          9682|  11725|   8852|    20577|           87097|  77415|  97992|       185419| 1481243|\n", "|            3| 647510|  498641|             198|          1280|   1478|   3924|     5402|           12943|  11663|  17065|        34214|  447362|\n", "|            4|1336300|  625887|             598|          5631|   6229|  11941|    18170|           56345|  50714|  68884|        81477|  475526|\n", "|            5|   5454|     905|               0|            19|     19|    202|      221|             172|    153|    374|          175|     356|\n", "+-------------+-------+--------+----------------+--------------+-------+-------+---------+----------------+-------+-------+-------------+--------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["calc_tag_stats(df_all, groupby='sales_channel')"]}, {"cell_type": "code", "execution_count": 106, "id": "ca52111c-8463-42bb-9528-db8706cc3668", "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 305:====================================================>(267 + 2) / 269]"]}, {"name": "stdout", "output_type": "stream", "text": ["+------+-------+--------+----------------+--------------+-------+-------+---------+----------------+-------+-------+-------------+--------+\n", "|    ts|num_app|num_acct|num_original_fpf|num_pseudo_fpf|num_fpf|num_tpf|num_fraud|num_original_fpd|num_fpd|num_bad|num_other_bad|num_good|\n", "+------+-------+--------+----------------+--------------+-------+-------+---------+----------------+-------+-------+-------------+--------+\n", "|202401| 901190|  390178|             349|          3245|   3594|   5112|     8706|           29111|  25866|  34572|        71606|  284000|\n", "|202402| 875647|  399113|             306|          3019|   3325|   5180|     8505|           31381|  28362|  36867|        76382|  285864|\n", "|202403| 915549|  421410|             323|          3086|   3409|   4550|     7959|           33476|  30390|  38349|        73240|  309821|\n", "|202404| 891953|  402216|             374|          3231|   3605|   4059|     7664|           32515|  29284|  36948|        64321|  300947|\n", "|202405| 912877|  409152|             511|          2758|   3269|   3862|     7131|           32232|  29474|  36605|        64141|  308406|\n", "|202406| 930916|  420941|             610|          2436|   3046|   3775|     6821|           31650|  29214|  36035|        60132|  324774|\n", "|202407| 985206|  432408|             653|          2818|   3471|   3884|     7355|           33292|  30474|  37829|        57514|  337065|\n", "|202408|1016261|  447845|             530|          3023|   3553|   4000|     7553|           33274|  30251|  37804|        54885|  355156|\n", "|202409|1068844|  456434|             418|          3140|   3558|   3536|     7094|           32980|  29840|  36934|        47156|  372344|\n", "|202410|1016110|  442023|             394|          3389|   3783|   4663|     8446|           31977|  28588|  37034|        38617|  366372|\n", "|202411|1016294|  428081|             535|          3437|   3972|   4492|     8464|           32917|  29480|  37944|        32549|  357588|\n", "|202412|1087023|  453175|             939|          3686|   4625|   3589|     8214|           36367|  32681|  40895|        25796|  386484|\n", "+------+-------+--------+----------------+--------------+-------+-------+---------+----------------+-------+-------+-------------+--------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["calc_tag_stats(df_all, groupby='ts')"]}, {"cell_type": "code", "execution_count": 107, "id": "6101613b-3a56-4a56-8f3c-ca1163ab3d26", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 308:================================================>   (253 + 16) / 269]"]}, {"name": "stdout", "output_type": "stream", "text": ["+------+-------+--------+------------------+------------------+------------------+------------------+------------------+--------------------+--------------------+--------------------+--------------------+\n", "|    ts|num_app|num_acct| original_fpf_loss|   pseudo_fpf_loss|          fpf_loss|          tpf_loss|        fraud_loss|   original_fpd_loss|            fpd_loss|      total_bad_loss|      other_bad_loss|\n", "+------+-------+--------+------------------+------------------+------------------+------------------+------------------+--------------------+--------------------+--------------------+--------------------+\n", "|202401| 901190|  390178|         544866.81|1570632.2299999997|2115499.0400000005|2317052.5000000005|        4432551.54|1.3142559530000003E7|1.1571927300000003E7|1.6004478839999994E7| 4.761147018999998E7|\n", "|202402| 875647|  399113|412850.75000000006|1209016.7100000002|1621867.4599999997|2115037.3899999997|3736904.8500000006|1.3634133559999999E7|       1.242511685E7|1.6162021700000001E7|       5.061977195E7|\n", "|202403| 915549|  421410|461905.31999999995|1269201.4500000011|1731106.7699999996|1841242.2600000005|3572349.0299999993|1.4446880040000001E7|1.3177678589999998E7|1.6750027619999997E7| 4.763998071999999E7|\n", "|202404| 891953|  402216|426229.56000000006|1433752.6500000001|        1859982.21|1506728.0700000008|3366710.2800000003|1.4003168570000004E7|1.2569415920000002E7|1.5936126200000007E7| 4.138266446000001E7|\n", "|202405| 912877|  409152| 727653.7799999997|1199721.5000000002|1927375.2799999996|        1331842.59| 3259217.869999999|       1.362335903E7|1.2423637529999997E7|1.5682855400000006E7| 4.119977110000002E7|\n", "|202406| 930916|  420941|1013930.1299999998|1131315.8199999996|2145245.9499999997|        1319022.94|        3464268.89|1.4051387129999997E7|1.2920071309999999E7|1.6384340200000005E7| 3.949685120999999E7|\n", "|202407| 985206|  432408| 940198.4400000003|        1388652.84|        2328851.28|1333541.6400000001|3662392.9200000013|       1.538741402E7|1.3998761180000002E7|1.7661154100000005E7|3.8738915519999996E7|\n", "|202408|1016261|  447845| 814935.4300000002|1563394.7499999993|2378330.1800000006|1496984.0300000003|        3875314.21|1.5671703850000009E7|1.4108309100000001E7|       1.798362331E7| 3.681440484999998E7|\n", "|202409|1068844|  456434|         706318.45|1638003.2199999997|2344321.6700000004|        1368792.27|3713113.9400000004|        1.56950688E7|1.4057065579999998E7|       1.777017952E7|3.2083662339999996E7|\n", "|202410|1016110|  442023| 790025.8899999999|2051161.7400000002|        2841187.63|2591423.7500000005| 5432611.380000001|1.5581005250000004E7|1.3529843510000004E7|1.8962454890000008E7|2.6235282030000016E7|\n", "|202411|1016294|  428081|         868268.15|2006710.5100000002|        2874978.66|3818437.5100000007| 6693416.170000002|1.6128516399999995E7|       1.412180589E7|       2.081522206E7|       2.219714229E7|\n", "|202412|1087023|  453175| 519464.2499999999|1938050.5999999999| 2457514.849999999|1314193.5999999996|3771708.4499999997|1.8185023369999997E7|1.6246972769999998E7|       2.001868122E7| 1.764488437999999E7|\n", "+------+-------+--------+------------------+------------------+------------------+------------------+------------------+--------------------+--------------------+--------------------+--------------------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                "]}], "source": ["calc_loss_stats(df_all, groupby='ts')"]}, {"cell_type": "code", "execution_count": 116, "id": "94192625-7fb1-43d1-9591-cda4ae51f800", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-08-06 22:53:46,896 INFO util.S3CredentialsResolverUtils: Loading com.amazon.emr.s3ranger.SecretAgentS3CredentialsResolver class\n", "2025-08-06 22:53:47,102 INFO maintenance.MultipartUploadCleaner: Multipart upload cleanup is now enabled for bucket: expn-da-prd-innovationlabs\n", "Found 33 items\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202204\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202205\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202206\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202207\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202208\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202209\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202210\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202211\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202212\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202301\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202302\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202303\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202304\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202305\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202306\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202307\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202308\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202309\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202310\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202311\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202312\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202401\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202402\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202403\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202404\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202405\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202406\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202407\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202408\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202409\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202410\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202411\n", "drwxrwxrwx   - <EMAIL> domain users          0 1970-01-01 00:00 s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/ts=202412\n", "2025-08-06 22:53:48,282 INFO maintenance.MultipartUploadCleaner: Shutting down multipart cleanup service\n"]}], "source": ["! hadoop fs -ls s3://expn-da-prd-innovationlabs/p/telco/attributes/application_attributes/tmobile_202204_202412/"]}, {"cell_type": "code", "execution_count": null, "id": "359c63a0-bb7c-46ca-beb3-aafe29c05b64", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}