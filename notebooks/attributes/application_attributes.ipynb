{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import os, findspark, socket\n", "\n", "spark_24 = '/work/app/spark'\n", "spark_23 = '/usr/hdp/current/spark2-client'\n", "\n", "hostname = socket.gethostname()\n", "if hostname.startswith('alnfrddap'):\n", "    spark_home='/opt/cloudera/parcels/SPARK2/lib/spark2'\n", "elif hostname.startswith('usmkdlph'):\n", "#     #spark 2.3\n", "    spark_home=spark_23\n", "\n", "#     spark 2.4\n", "#     spark_home=spark_24\n", "else:\n", "    pass\n", "    #print(f'No spark installed on server: {hostname}!')\n", "\n", "os.environ['SPARK_HOME']=spark_home\n", "os.environ['ARROW_PRE_0_15_IPC_FORMAT'] = '1'\n", "findspark.init(spark_home)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyspark import SparkConf\n", "sc = SparkConf()\\\n", ".set(\"spark.app.name\", \"gid-notebook-000\")\\\n", ".set(\"spark.master\", \"yarn\")\\\n", ".set(\"spark.submit.deployMode\", \"client\")\\\n", ".set(\"spark.driver.memory\", \"24g\")\\\n", ".set(\"spark.driver.maxResultSize\", \"4g\")\\\n", ".set(\"spark.executor.memory\", \"18g\")\\\n", ".set(\"spark.executor.cores\", 5)\\\n", ".set(\"spark.executor.instances\", 10)\\\n", ".set(\"spark.default.parallelism\", 200)\\\n", ".set(\"spark.sql.shuffle.partitions\", 1000)\\\n", ".set(\"spark.executor.memoryOverhead\", \"4g\")\\\n", ".set(\"spark.sql.execution.arrow.enabled\", \"false\")\\\n", ".set(\"spark.rdd.compress\", \"true\")\\\n", ".set(\"spark.shuffle.compress\", \"true\")\\\n", ".set(\"spark.serializer\", \"org.apache.spark.serializer.KryoSerializer\")\\\n", ".set(\"spark.kryoserializer.buffer.max\", \"512M\")\\\n", "\n", "#spark 2.4 installed on HDP/new cluster does not have integration with HDP and does not support dynamicAllocation\n", "#if you enable this accidentally, your job will hang and see complaint on console \"cannot allocate executors\"\n", "if spark_home != '/work/app/spark':\n", "    sc = sc\\\n", "    .set(\"spark.shuffle.service.enabled\", \"true\")\\\n", "    .set(\"spark.dynamicAllocation.enabled\", \"true\")\\\n", "    .set(\"spark.dynamicAllocation.maxExecutors\", 40)\\\n", "\n", "from pyspark.sql import SparkSession\n", "spark = SparkSession.builder.config(conf=sc).getOrCreate()\n", "spark"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "import time\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import json\n", "import yaml\n", "import math\n", "from functools import reduce\n", "from itertools import chain\n", "from sklearn.metrics import accuracy_score, precision_recall_fscore_support, roc_curve, roc_auc_score"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyspark.sql import Row\n", "from pyspark.sql import SparkSession\n", "from operator import add\n", "from pyspark.sql import functions as F\n", "from pyspark.sql import Window as W\n", "from pyspark.sql import types as T"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from retro_analysis.utils import check_distribution, log"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark.sparkContext.addPyFile(\"../attributes.py\")\n", "import attributes\n", "import importlib\n", "importlib.reload(attributes)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def check_stats(df):\n", "    df.select(F.count(F.lit(1)).alias('count'), F.countDistinct('BUID').alias('num_buid')).show()\n", "\n", "def check_uid_stats(df_uid):\n", "    check_stats(df_uid)\n", "    count = df_uid.count()\n", "    for pii in ['addr', 'phone', 'ssn']:\n", "        null_count = df_uid.filter(\"{}_uid is null\".format(pii)).count()\n", "        print(\"{} out of {} ({}) do not have {}_uid\".format(null_count, count, round(float(null_count)/count, 4), pii))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# US Cellular\n", "* Use updated FPF tag and sales channel\n", "* Use aligned sales channel and ID type\n", "* Convert time zone for application time (still having issue)\n", "* Add home_zip_store_zip_match and assign default value when application is from online channel"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["home_addr_fields = \"\"\"STREETADDRESS\n", "ZIPCODE\n", "CITY\n", "STATE\"\"\".split('\\n')\n", "\n", "store_addr_fields = \"\"\"LINE_1_ADDR\n", "LINE_2_ADDR\n", "ZIP5_CD\n", "CITY_NM\n", "STATE_CD\"\"\".split('\\n')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_app_raw = spark.read.csv('/p/telco/uscellular/raw/app_202007_202212/edlca0182_JUL2020_DEC2022_LIST_uscellular.csv.bz2', header=True)\n", "uc_app_raw_new = spark.read.csv('/p/telco/uscellular/raw/app_202007_202212_new', header=True)\n", "\n", "uc_app_raw_updated = uc_app_raw.dropDuplicates(subset=['CUSTOMER_ID'])\\\n", "                               .drop('Sales_Chnl')\\\n", "                               .join(uc_app_raw_new.select('CUSTOMER_ID', 'FPF_indicator', 'Sales_Chnl'), 'CUSTOMER_ID', 'left')\n", "\n", "uc_content = spark.read.parquet('/p/telco/uscellular/pinned/202007_202212/content')\\\n", "                  .withColumn('CUSTOMER_ID', F.split('BUID', '_')[1]).drop('BUID')\n", "uc_buid_uid = spark.read.parquet('/p/telco/uscellular/study/uscellular_buid_uid')\\\n", "                   .withColumn('CUSTOMER_ID', F.split('BUID', '_')[1]).drop('BUID')\n", "uc_addr = spark.read.parquet('/p/telco/uscellular/study/uscellular_addr')\n", "uc_pinned = spark.read.parquet('/p/telco/uscellular/pinned/202007_202212/pinned_with_GP/')\\\n", "                 .withColumn('CUSTOMER_ID', F.split('inquiry_id', '_')[1])\\\n", "                 .withColumn('opin', <PERSON><PERSON>col('pin').cast('string')).drop('inquiry_id', 'pin')\n", "uc_pii = spark.read.parquet('/p/telco/uscellular/study/uscellular_pii')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_app_raw.count(), uc_content.count(), uc_buid_uid.count(), uc_addr.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_app_raw.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_content.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_buid_uid.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_addr.printSchema()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_pinned.printSchema()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_pinned.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_pinned.select(F.countDistinct('opin'), F.countDistinct('eid')).show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_pii.printSchema()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_pii.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_pii.filter(<PERSON><PERSON>col('home_addr.validity') == 'False').count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_app = uc_app_raw_updated\\\n", "    .join(uc_content, 'CUSTOMER_ID')\\\n", "    .join(uc_buid_uid, 'CUSTOMER_ID')\\\n", "    .join(uc_addr, 'CUSTOMER_ID')\\\n", "    .join(uc_pinned, 'CUSTOMER_ID', 'left')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## new: uc_pii contains home and store address as well as email_uid\n", "uc_app = uc_app_raw_updated\\\n", "    .join(uc_pii, 'CUSTOMER_ID')\\\n", "    .join(uc_pinned, 'CUSTOMER_ID', 'left')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_app.count()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## some EDA"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_addr.withColumn(\n", "    \"home_zip_store_zip_match\", attributes.zipcode_match_udf(\n", "        <PERSON><PERSON>col(\"home_addr.zipcode\"), <PERSON><PERSON>col(\"store_addr.zipcode\")\n", "    )\n", ").take(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_addr.filter(\"store_addr is null\").count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(uc_app, 'FPD_Ind')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(uc_app, 'Portin')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Portin is available on for activated\n", "check_distribution(uc_app_raw, 'Portin')\n", "check_distribution(uc_app_raw.filter(\"actv = '1'\"), 'Portin')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(uc_app, 'Sales_Chnl', add_pct=True, sort_by_count=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(uc_app, 'IDTYPE', add_pct=True, sort_by_count=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## store zipcode is available only for agents or stores\n", "check_distribution(uc_app.filter(\"LINE_1_ADDR is not null and ZIP5_CD != '{UNK}'\"), 'Sales_Chnl', add_pct=True, sort_by_count=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(uc_app.filter(\"LINE_1_ADDR is null or ZIP5_CD = '{UNK}'\"), 'Sales_Chnl', add_pct=True, sort_by_count=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(uc_app.filter(<PERSON><PERSON>col('store_addr').getField('zipcode') != ''), 'Sales_Chnl', sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_app.filter(\"Sales_Chnl = 'Exclusive Agent'\").select(store_addr_fields).show(20, truncate=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(uc_app.filter(\"Sales_Chnl = 'Exclusive Agent'\"), 'STATE_CD', add_pct=True, sort_by_count=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_app.filter(\"LINE_1_ADDR is not null and ZIP5_CD != '{UNK}'\").filter(\"Sales_Chnl = 'Walmart Postpaid'\").select(store_addr_fields + ['store_addr']).show(20, truncate=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_app.filter(\"Sales_Chnl = 'Non-Exclusive Agent'\").select(store_addr_fields).show(20, truncate=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## STATE_CD can be 'UNK'\n", "df_tmp = uc_app.withColumn('len', F.length('STATE_CD'))\n", "check_distribution(df_tmp, 'len', add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## ZIP5_CD can be 'UNK'\n", "df_tmp = uc_app.withColumn('len', F.length('ZIP5_CD'))\n", "check_distribution(df_tmp, 'len', add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_app.filter(\"STATE_CD is null\").select('store_addr').take(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_tmp = uc_app.withColumn('len', F.length('STATE'))\n", "check_distribution(df_tmp, 'len', add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_tmp = uc_app.withColumn('len', F.length('SSN'))\n", "check_distribution(df_tmp, 'len', add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_tmp = uc_app.withColumn('len', F.length('CONTACTPHONENUMBER'))\n", "check_distribution(df_tmp, 'len', add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pytz\n", "from datetime import datetime\n", "\n", "state2timezone = {\n", "    \"AK\": \"US/Alaska\",\n", "    \"AL\": \"US/Central\",\n", "    \"AR\": \"US/Central\",\n", "    \"AS\": \"US/Samoa\",\n", "    \"AZ\": \"US/Mountain\",\n", "    \"CA\": \"US/Pacific\",\n", "    \"CO\": \"US/Mountain\",\n", "    \"CT\": \"US/Eastern\",\n", "    \"DC\": \"US/Eastern\",\n", "    \"DE\": \"US/Eastern\",\n", "    \"FL\": \"US/Eastern\",\n", "    \"GA\": \"US/Eastern\",\n", "    \"GU\": \"Pacific/Guam\",\n", "    \"HI\": \"US/Hawaii\",\n", "    \"IA\": \"US/Central\",\n", "    \"ID\": \"US/Mountain\",\n", "    \"IL\": \"US/Central\",\n", "    \"IN\": \"US/Eastern\",\n", "    \"KS\": \"US/Central\",\n", "    \"KY\": \"US/Eastern\",\n", "    \"LA\": \"US/Central\",\n", "    \"MA\": \"US/Eastern\",\n", "    \"MD\": \"US/Eastern\",\n", "    \"ME\": \"US/Eastern\",\n", "    \"MI\": \"US/Eastern\",\n", "    \"MN\": \"US/Central\",\n", "    \"MO\": \"US/Central\",\n", "    \"MP\": \"Pacific/Guam\",\n", "    \"MS\": \"US/Central\",\n", "    \"MT\": \"US/Mountain\",\n", "    \"NC\": \"US/Eastern\",\n", "    \"ND\": \"US/Central\",\n", "    \"NE\": \"US/Central\",\n", "    \"NH\": \"US/Eastern\",\n", "    \"NJ\": \"US/Eastern\",\n", "    \"NM\": \"US/Mountain\",\n", "    \"NV\": \"US/Pacific\",\n", "    \"NY\": \"US/Eastern\",\n", "    \"OH\": \"US/Eastern\",\n", "    \"OK\": \"US/Central\",\n", "    \"OR\": \"US/Pacific\",\n", "    \"PA\": \"US/Eastern\",\n", "    \"PR\": \"America/Puerto_Rico\",\n", "    \"RI\": \"US/Eastern\",\n", "    \"SC\": \"US/Eastern\",\n", "    \"SD\": \"US/Central\",\n", "    \"TN\": \"US/Central\",\n", "    \"TX\": \"US/Central\",\n", "    \"UT\": \"US/Mountain\",\n", "    \"VA\": \"US/Eastern\",\n", "    \"VI\": \"America/Virgin\",\n", "    \"VT\": \"US/Eastern\",\n", "    \"WA\": \"US/Pacific\",\n", "    \"WI\": \"US/Central\",\n", "    \"WV\": \"US/Eastern\",\n", "    \"WY\": \"US/Mountain\",\n", "    \"\": \"US/Pacific\",\n", "    \"--\": \"US/Pacific\",\n", "}\n", "# app_time_format = \"%d%b%Y:%H:%M:%S.%f\"\n", "\n", "\n", "def convert_datetime_timezone(dt_str, tz, app_time_format, server_time_zone):\n", "    gmt_tz = pytz.timezone(server_time_zone)\n", "    tz = pytz.timezone(tz)\n", "    dt = datetime.strptime(dt_str, app_time_format)\n", "    dt = gmt_tz.localize(dt)\n", "    dt = dt.astimezone(tz)\n", "    # must convert dt to string. Returning dt as datetime type will cause unpredictable error when wrapping it using T.TimestamType()\n", "    return dt.strftime('%Y-%m-%d:%H:%M:%S') \n", "\n", "\n", "convert_datetime_timezone_udf = F.udf(convert_datetime_timezone, T.StringType())\n", "\n", "\n", "def convert_time_to_local(dt_str, state, app_time_format, server_time_zone):\n", "    if not dt_str or not state:\n", "        return None\n", "#     return state2timezone[state]\n", "    return convert_datetime_timezone(\n", "        dt_str, state2timezone[state], app_time_format, server_time_zone\n", "    )\n", "\n", "\n", "def parse_app_time_to_local(dt_str, store_state, home_state, app_time_format, server_time_zone):\n", "    try:\n", "        state = next(x for x in [store_state, home_state] if x)\n", "        if not state or not dt_str:\n", "            return None\n", "        return convert_time_to_local(dt_str, state, app_time_format, server_time_zone)\n", "    except StopIteration as e:\n", "        return None\n", "\n", "\n", "# parse_app_time_udf = F.udf(parse_app_time_to_local, T.TimestampType())\n", "parse_app_time_udf = F.udf(parse_app_time_to_local, T.StringType())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_app.select('APPLICATIONTIME', 'store_addr', 'home_addr')\\\n", ".withColumn(\n", "    \"app_local_time_str\", attributes.parse_app_time_udf(\n", "        <PERSON><PERSON>col('APPLICATIONTIME'),\n", "        <PERSON><PERSON>col('store_addr.state'),\n", "        <PERSON><PERSON>col('home_addr.state'),\n", "        F.lit('%d%b%Y:%H:%M:%S'),\n", "        F.lit('GMT')\n", "    )\n", ").withColumn(\n", "    'app_local_time', F.to_timestamp('app_local_time_str', 'yyyy-MM-dd:HH:mm:ss'))\\\n", ".filter(<PERSON>.col('store_addr.state') == 'FL').take(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_app_raw.select('APPLICATIONTIME')\\\n", ".withColumn(\n", "    'app_gmt_time_str', attributes.convert_datetime_timezone_udf(\n", "        <PERSON><PERSON>col('APPLICATIONTIME'),\n", "        F.lit('GMT'),\n", "        F.lit('%d%b%Y:%H:%M:%S'),\n", "        F.lit('US/Central')\n", "    )\n", ").withColumn(\n", "    'app_gmt_time', F.to_timestamp('app_gmt_time_str', 'yyyy-MM-dd:HH:mm:ss')\n", ").take(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## application attributes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["originalChannel = \"\"\"Company-Owned      \n", "Exclusive Agent    \n", "Web_App            \n", "Walmart Postpaid   \n", "Internal Telesales \n", "External Telesales \n", "Customer Care\n", "Non-Exclusive Agent\n", "Inside Sales\n", "Exclusive Walmart  \n", "Business Direct    \n", "Sam's Club Postpaid\n", "Walmart Prepaid\"\"\"\n", "\n", "mappedChannel =\"\"\"B_COMPANY_OWNED\n", "B_EXCLUSIVE_AGENT\n", "WEB\n", "B_WALMART_POSTPAID\n", "B_INTERNAL_TELESALE\n", "B_EXTERNAL_TELESALE\n", "B_CUSTOMER_CARE\n", "B_OTHER\n", "B_OTHER\n", "B_OTHER\n", "B_OTHER\n", "B_OTHER\n", "B_OTHER\"\"\"\n", "\n", "originalChannel = [x.strip() for x in originalChannel.split('\\n')]\n", "mappedChannel = [x.strip() for x in mappedChannel.split('\\n')]\n", "sales_channel_map = dict(zip(originalChannel, mappedChannel))\n", "\n", "def map_sales_channel(x):\n", "    if x is None:\n", "        return None\n", "    return sales_channel_map.get(x, 'B_OTHER')\n", "\n", "map_sales_channel_udf = F.udf(map_sales_channel, T.StringType())\n", "\n", "\n", "originalIdType = \"\"\"DriversLicense\n", "StateID\n", "Passport\n", "Consular\n", "<PERSON><PERSON><PERSON><PERSON>\n", "Military\"\"\"\n", "\n", "mappedIdType = \"\"\"DRIVER_LICENSE\n", "STATE_ID\n", "PASSPORT\n", "B_CONSULAR\n", "B_RESIDENT_ALIEN\n", "MILITARY\"\"\"\n", "\n", "originalIdType = [x.strip() for x in originalIdType.split('\\n')]\n", "mappedIdType = [x.strip() for x in mappedIdType.split('\\n')]\n", "id_type_map = dict(zip(originalIdType, mappedIdType))\n", "\n", "def map_id_type(x):\n", "    if x is None:\n", "        return None\n", "    return id_type_map.get(x, 'B_OTHER')\n", "\n", "map_id_type_udf = F.udf(map_id_type, T.StringType())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_fields_to_standard = ['CUSTOMER_ID as APP_NUMBER', 'APPLICATIONTIME', \n", "                          'STREETADDRESS as ADDR_LINE_1', 'cast(NULL as string) as ADDR_LINE_2', 'ZIPCODE as ZIPCODE', 'CITY as CITY', 'STATE as STATE',\n", "                          'LINE_1_<PERSON><PERSON> as STORE_ADDR_LINE_1',  'LINE_2_ADDR as STORE_ADDR_LINE_2',  'ZIP5_CD as STORE_ZIPCODE', 'CITY_NM as STORE_CITY', 'STATE_CD as STORE_STATE',\n", "                          'DATEOFBIRTH as DOB', 'SSN', 'CONTACTPHONENUMBER as PHONE', 'EMAIL',\n", "                          'cast(<PERSON><PERSON> as int) as PORTIN_FLAG',\n", "                          'cast(num_line as int) as NUM_LINES', \n", "                          'cast(DVS_FIN_CNT as int) as DEVICE_FINANCED', \n", "                          'cast(EIP_FIN_AMT as double) as AMT_FINANCED', \n", "                          'cast(wo_amt as double) as FRAUD_WO_AMT',\n", "                          'IDTYPE as ID_TYPE', 'Sales_Chnl as CHANNEL', \n", "                          'addr_uid', 'phone_uid', 'email_uid', 'ssn_uid', 'eid', 'opin', 'cluster_score']\n", "\n", "raw_fields_to_immediate = ['actv', 'FPF_Indicator', 'FPD_ind', 'store_addr', 'home_addr']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## map and standardize raw fields\n", "df_uc_mapped = uc_app \\\n", ".selectExpr(*raw_fields_to_standard, *raw_fields_to_immediate)\\\n", ".withColumn(\n", "    'store_addr_valid', (<PERSON><PERSON>col('CHANNEL').isin(['Exclusive Agent', 'Non-Exclusive Agent']) & (<PERSON><PERSON>col('store_addr.validity') == 'True')).cast('int')\n", ").withColumn(\n", "    'STORE_STATE', F.when(F.col('store_addr_valid') == 1, F.col('store_addr.state')).otherwise(F.lit(''))\n", ").withColumn(\n", "    'STORE_ZIPCODE', F.when(F.col('store_addr_valid') == 1, F.col('store_addr.zipcode')).otherwise(F.lit(''))\n", ").withColumn(\n", "    'HOME_STATE', F.when(F.col('home_addr.validity') == 'True', F.col('home_addr.state')).otherwise(F.lit(''))\n", ").withColumn(\n", "    'HOME_ZIPCODE', F.when(F.col('home_addr.validity') == 'True', F.col('home_addr.zipcode')).otherwise(F.lit(''))\n", ").withColumn(\n", "    'FRAUD_TAG', <PERSON><PERSON>col('FPF_Indicator').isNotNull().cast('int')\n", ").withColumn(\n", "    'FPD_TAG', (<PERSON><PERSON>col('FPD_Ind') == '1').cast('int')\n", ").withColumn(\n", "    'BAD_TAG', ((F.col('FRAUD_TAG') == 1) | (F.col('FPD_TAG') == 1)).cast('int')\n", ").withColumn(\n", "    'CHANNEL', map_sales_channel_udf(<PERSON><PERSON>col('CHANNEL'))\n", ").withColumn(\n", "    'ID_TYPE', map_id_type_udf(F.col('ID_TYPE'))\n", ").withColumn(\n", "    'is_activated', (<PERSON>.col('actv') == '1').cast('int')\n", ").withColumn(\n", "    'app_gmt_time_str', attributes.convert_datetime_timezone_udf(\n", "        <PERSON><PERSON>col('APPLICATIONTIME'),\n", "        F.lit('GMT'),\n", "        F.lit('%d%b%Y:%H:%M:%S'),\n", "        F.lit('US/Central')\n", "    )\n", ").withColumn(\n", "    'app_gmt_time', F.to_timestamp('app_gmt_time_str', 'yyyy-MM-dd:HH:mm:ss')\n", ").withColumn(\n", "    'app_local_time_str', attributes.parse_app_time_udf(\n", "        <PERSON><PERSON>col('APPLICATIONTIME'),\n", "        <PERSON><PERSON>col('STORE_STATE'),\n", "        <PERSON><PERSON>col('HOME_STATE'),\n", "        F.lit('%d%b%Y:%H:%M:%S'),\n", "        F.lit('US/Central')\n", "    )\n", ").withColumn(\n", "    'app_local_time', F.to_timestamp('app_local_time_str', 'yyyy-MM-dd:HH:mm:ss')\n", ").withColumn(\n", "    'app_local_time', F.when(F.col('app_local_time').isNull(), F.to_timestamp('APPLICATIONTIME', 'ddMMMyyyy:HH:mm:ss'))\\\n", "                       .otherwise(<PERSON>.col('app_local_time'))\n", ") ## when app_local_time cannot be determined due to missing both store and home address, just use the original app time (in CST)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_uc_mapped.printSchema()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(uc_app_raw_updated, 'Sales_Chnl', sort_by_count=True, add_pct=True)\n", "check_distribution(df_uc_mapped, 'CHANNEL', sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(uc_app_raw_updated, 'IDTYPE', sort_by_count=True, add_pct=True)\n", "check_distribution(df_uc_mapped, 'ID_TYPE', sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_uc_mapped.filter(\"app_local_time is null\").count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_uc_mapped.filter(\"HOME_STATE = '' and STORE_STATE = ''\").count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_uc_mapped.filter(\"HOME_ZIPCODE = '' and STORE_ZIPCODE = ''\").count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["34070 / 728859"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## calculate application attributes\n", "\n", "BASE_TIMESTAMP = '01JAN1970:00:00:00'\n", "\n", "df_uc_attr = df_uc_mapped\\\n", "    .withColumn(\n", "        'app_date', F.to_date('app_local_time')\n", "    ).withColumn(\n", "        'month', F.date_format('app_date', 'yyyyMM')\n", "    ).withColumn(\n", "        'app_datetime_unix', F.unix_timestamp('app_gmt_time').cast('long') - F.to_timestamp(F.lit(BASE_TIMESTAMP), 'ddMMMyyyy:HH:mm:ss').cast('long')\n", "    ).withColumn(\n", "        'day_of_week', attributes.day_of_week_udf('app_local_time')\n", "    ).withColumn(\n", "        'time_of_day', attributes.part_of_day_udf('app_local_time')\n", "    ).withColumn(\n", "        'during_office_hour', attributes.during_office_hour_udf('app_local_time')\n", "    ).withColumn(\n", "        'sales_channel', attributes.sales_channel_udf('CHANNEL')\n", "    ).withColumn(\n", "        'has_ssn', F.when(F.length(F.col('SSN')) == 9, F.lit(1))\\\n", "                    .when(F.length(F.col('SSN')) == 4, F.lit(2))\\\n", "                    .otherwise(F.lit(0))\n", "    ).withColumn(\n", "        'has_dob', (<PERSON><PERSON>col('DOB').isNotNull() | (<PERSON>.col('DOB') != '')).cast('int')\n", "    ).withColumn(\n", "        'has_phone', (<PERSON><PERSON>col('PHONE').isNotNull() | (F.col('PHONE') != '')).cast('int')\n", "    ).withColumn(\n", "        'has_email', (<PERSON><PERSON>col('EMAIL').isNotNull() | (<PERSON><PERSON>col('EMAIL') != '')).cast('int')\n", "    ).withColumn(\n", "        'id_type', attributes.id_type_udf(F<PERSON>col('ID_TYPE'))\n", "    ).withColumn(\n", "        'has_id', (<PERSON>.col('id_type') >= 2).cast('int')\n", "    ).withColumn(\n", "        'portin_ind', F.when(F.col('PORTIN_FLAG') == 1, 1).otherwise(0)\n", "    ).withColumn(\n", "        'home_zip_store_zip_match', attributes.zipcode_match_udf(<PERSON><PERSON>col('HOME_ZIPCODE'), <PERSON><PERSON>col('STORE_ZIPCODE')) # verizon: use raw home zipcode\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_cols = \"\"\"APP_NUMBER\n", "app_local_time\n", "app_gmt_time\n", "app_date\n", "month\n", "app_datetime_unix\n", "day_of_week\n", "time_of_day\n", "during_office_hour\n", "sales_channel\n", "has_ssn\n", "has_dob\n", "has_phone\n", "has_email\n", "has_id\n", "id_type\n", "portin_ind\n", "is_activated\n", "NUM_LINES\n", "DEVICE_FINANCED\n", "AMT_FINANCED\n", "FRAUD_TAG\n", "FRAUD_WO_AMT\n", "FPD_TAG\n", "BAD_TAG\n", "home_zip_store_zip_match\n", "addr_uid\n", "ssn_uid\n", "phone_uid\n", "email_uid\n", "eid\n", "opin\n", "cluster_score\"\"\".split('\\n')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_uc_attr.select(output_cols).write.mode('overwrite').parquet('/p/telco/attributes/application_attributes/uscellular_202007_202212_new')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### check attributes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df_uc_app_attr = spark.read.parquet('/p/telco/attributes/application_attributes/uscellular_202007_202212')#.cache()\n", "df_uc_app_attr = spark.read.parquet('/p/telco/attributes/application_attributes/uscellular_202007_202212_new')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_uc_app_attr.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_uc_app_attr.printSchema()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_uc_app_attr_prev = spark.read.parquet('/p/telco/uscellular/attributes/202007_202212/app_attr_with_tag')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["set(df_uc_app_attr_prev.columns) - set(df_uc_app_attr.columns)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["set(df_uc_app_attr.columns) - set(df_uc_app_attr_prev.columns)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_uc_app_attr.filter(\"eid is null\").count(), df_uc_app_attr.filter(\"opin is null\").count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_uc_app_attr.filter(\"month = '202006'\").count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_uc_app_attr.filter(\"month = '202006'\").select('APP_NUMBER', 'app_local_time').take(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_app.filter(\"CUSTOMER_ID = '860938821'\").select('APPLICATIONTIME', 'Sales_Chnl', *home_addr_fields, *store_addr_fields).take(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_uc_app_attr.filter(\"month = '202301'\").select('APP_NUMBER', 'app_local_time').take(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_app.filter(\"CUSTOMER_ID = '863926903'\").select('APPLICATIONTIME', 'Sales_Chnl', *home_addr_fields, *store_addr_fields).take(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["app_attr = [\n", "    'day_of_week',\n", "    'time_of_day',\n", "    'during_office_hour',\n", "    'has_ssn',\n", "    'has_dob',\n", "    'has_phone',\n", "    'has_email',\n", "    'has_id',\n", "    'portin_ind',\n", "    'home_zip_store_zip_match',\n", "    'is_activated'\n", "]\n", "\n", "acct_cols = [\n", "    'FRAUD_TAG',\n", "    'FPD_TAG',\n", "    'BAD_TAG'\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## distribution in raw data for comparison\n", "+-------------------+------+------+\n", "|Sales_Chnl         |count |pct   |\n", "+-------------------+------+------+\n", "|Company-Owned      |297606|0.4083|\n", "|Exclusive Agent    |197403|0.2708|\n", "|Web_App            |157943|0.2167|\n", "|Walmart Postpaid   |43145 |0.0592|\n", "|Internal Telesales |26402 |0.0362|\n", "|External Telesales |3760  |0.0052|\n", "|Customer Care      |1827  |0.0025|\n", "|null               |322   |4.0E-4|\n", "|Non-Exclusive Agent|138   |2.0E-4|\n", "|Inside Sales       |131   |2.0E-4|\n", "|Exclusive Walmart  |122   |2.0E-4|\n", "|Business Direct    |33    |0.0   |\n", "|Sam's Club Postpaid|15    |0.0   |\n", "|Walmart Prepaid    |12    |0.0   |\n", "+-------------------+------+------+\n", "\n", "+--------------+------+------+\n", "|IDTYPE        |count |pct   |\n", "+--------------+------+------+\n", "|DriversLicense|624016|0.8562|\n", "|StateID       |80507 |0.1105|\n", "|null          |13113 |0.018 |\n", "|Passport      |5332  |0.0073|\n", "|Consular      |4243  |0.0058|\n", "|ResidentAlien |1147  |0.0016|\n", "|Military      |501   |7.0E-4|\n", "+--------------+------+------+"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for col in ['sales_channel', 'id_type']:\n", "    check_distribution(df_uc_app_attr, col, sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## previous distribution (not very reasonable due to time zone not correctly converted)\n", "+-----------+------+------+\n", "|day_of_week|count |pct   |\n", "+-----------+------+------+\n", "|0          |106720|0.1464|\n", "|1          |105509|0.1448|\n", "|2          |108531|0.1489|\n", "|3          |110377|0.1514|\n", "|4          |127051|0.1743|\n", "|5          |110728|0.1519|\n", "|6          |59943 |0.0822|\n", "+-----------+------+------+\n", "\n", "+-----------+------+------+\n", "|time_of_day|count |pct   |\n", "+-----------+------+------+\n", "|1          |23880 |0.0328|\n", "|2          |247193|0.3392|\n", "|3          |304147|0.4173|\n", "|4          |116229|0.1595|\n", "|5          |24582 |0.0337|\n", "|6          |12828 |0.0176|\n", "+-----------+------+------+"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["+-----------+------+------+\n", "|day_of_week|count |pct   |\n", "+-----------+------+------+\n", "|0          |106820|0.1466|\n", "|1          |105140|0.1443|\n", "|2          |108518|0.1489|\n", "|3          |110441|0.1515|\n", "|4          |127120|0.1744|\n", "|5          |111209|0.1526|\n", "|6          |59611 |0.0818|\n", "+-----------+------+------+\n", "\n", "+-----------+------+------+\n", "|time_of_day|count |pct   |\n", "+-----------+------+------+\n", "|1          |13741 |0.0189|\n", "|2          |13729 |0.0188|\n", "|3          |157827|0.2165|\n", "|4          |315506|0.4329|\n", "|5          |198077|0.2718|\n", "|6          |29979 |0.0411|\n", "+-----------+------+------+\n", "\n", "+------------------+------+------+\n", "|during_office_hour|count |pct   |\n", "+------------------+------+------+\n", "|0                 |75009 |0.1029|\n", "|1                 |653850|0.8971|\n", "+------------------+------+------+"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## after removing invalid store address\n", "for col in app_attr:\n", "    check_distribution(df_uc_app_attr, col, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## new result after fixing time calculation\n", "for col in app_attr:\n", "    check_distribution(df_uc_app_attr, col, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## old result\n", "for col in app_attr:\n", "    check_distribution(df_uc_app_attr, col, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for col in acct_cols:\n", "    check_distribution(df_uc_app_attr, col, sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for col in acct_cols:\n", "    check_distribution(df_uc_app_attr.filter(\"is_activated = 1\"), col, sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_uc_app_attr.select('APP_NUMBER', 'app_local_time', 'app_gmt_time', 'app_datetime_unix').take(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["21600 / 3600"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class ZipCodeMatchLevel:\n", "    LEFT_NULL = 0\n", "    RIGHT_NUll = 1\n", "    NULLS_EQUAL = 3\n", "    ZIP5_MATCH = 4\n", "    ZIP3_MATCH = 5\n", "    CONFLICT = 6\n", "    \n", "+------------------------+------+------+\n", "|home_zip_store_zip_match|count |pct   |\n", "+------------------------+------+------+\n", "|0                       |2964  |0.0041|\n", "|1                       |521681|0.7158|\n", "|3                       |10150 |0.0139|\n", "|4                       |80534 |0.1105|\n", "|5                       |74288 |0.1019|\n", "|6                       |39242 |0.0538|\n", "+------------------------+------+------+\n", "\n", "+------------------------+------+------+\n", "|home_zip_store_zip_match|count |pct   |\n", "+------------------------+------+------+\n", "|0                       |5757  |0.0079|\n", "|1                       |354392|0.4862|\n", "|3                       |242097|0.3322|\n", "|4                       |53209 |0.073 |\n", "|5                       |48095 |0.066 |\n", "|6                       |25309 |0.0347|\n", "+------------------------+------+------+"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_uc_app_attr.filter(\"home_zip_store_zip_match = 3\").select('APP_NUMBER').take(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uc_app.filter(\"CUSTOMER_ID = '453144477'\").select('home_addr', 'store_addr', 'Sales_Chnl').take(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## match level 0, 4, 5, 6 can only happen when there is a valid store address which correponds to sales channel being an agent (7 or 12)\n", "check_distribution(df_uc_app_attr.filter(\"home_zip_store_zip_match in (0, 4, 5, 6)\"), 'sales_channel')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.16"}}, "nbformat": 4, "nbformat_minor": 4}