{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import os, findspark, socket\n", "\n", "spark_24 = '/work/app/spark'\n", "spark_23 = '/usr/hdp/current/spark2-client'\n", "\n", "hostname = socket.gethostname()\n", "if hostname.startswith('alnfrddap'):\n", "    spark_home='/opt/cloudera/parcels/SPARK2/lib/spark2'\n", "elif hostname.startswith('usmkdlph'):\n", "#     #spark 2.3\n", "    spark_home=spark_23\n", "\n", "#     spark 2.4\n", "#     spark_home=spark_24\n", "else:\n", "    pass\n", "    #print(f'No spark installed on server: {hostname}!')\n", "\n", "os.environ['SPARK_HOME']=spark_home\n", "os.environ['ARROW_PRE_0_15_IPC_FORMAT'] = '1'\n", "findspark.init(spark_home)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyspark import SparkConf\n", "sc = SparkConf()\\\n", ".set(\"spark.app.name\", \"gid-notebook-000\")\\\n", ".set(\"spark.master\", \"yarn\")\\\n", ".set(\"spark.submit.deployMode\", \"client\")\\\n", ".set(\"spark.driver.memory\", \"24g\")\\\n", ".set(\"spark.driver.maxResultSize\", \"4g\")\\\n", ".set(\"spark.executor.memory\", \"18g\")\\\n", ".set(\"spark.executor.cores\", 5)\\\n", ".set(\"spark.executor.instances\", 10)\\\n", ".set(\"spark.default.parallelism\", 200)\\\n", ".set(\"spark.sql.shuffle.partitions\", 1000)\\\n", ".set(\"spark.executor.memoryOverhead\", \"4g\")\\\n", ".set(\"spark.sql.execution.arrow.enabled\", \"false\")\\\n", ".set(\"spark.rdd.compress\", \"true\")\\\n", ".set(\"spark.shuffle.compress\", \"true\")\\\n", ".set(\"spark.serializer\", \"org.apache.spark.serializer.KryoSerializer\")\\\n", ".set(\"spark.kryoserializer.buffer.max\", \"512M\")\\\n", "\n", "#spark 2.4 installed on HDP/new cluster does not have integration with HDP and does not support dynamicAllocation\n", "#if you enable this accidentally, your job will hang and see complaint on console \"cannot allocate executors\"\n", "if spark_home != '/work/app/spark':\n", "    sc = sc\\\n", "    .set(\"spark.shuffle.service.enabled\", \"true\")\\\n", "    .set(\"spark.dynamicAllocation.enabled\", \"true\")\\\n", "    .set(\"spark.dynamicAllocation.maxExecutors\", 40)\\\n", "\n", "from pyspark.sql import SparkSession\n", "spark = SparkSession.builder.config(conf=sc).getOrCreate()\n", "spark"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "import time\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import json\n", "import yaml\n", "import math\n", "import pytz\n", "from datetime import datetime\n", "from functools import reduce\n", "from itertools import chain\n", "from sklearn.metrics import accuracy_score, precision_recall_fscore_support, roc_curve, roc_auc_score"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyspark.sql import Row\n", "from pyspark.sql import SparkSession\n", "from operator import add\n", "from pyspark.sql import functions as F\n", "from pyspark.sql import Window as W\n", "from pyspark.sql import types as T\n", "from pyspark.sql import DataFrame"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from retro_analysis.utils import check_distribution, log"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["useful_fields = ['client_name', 'opin', 'eid', 'app_date', 'month', 'ts', 'is_activated', \n", "                 'FPF_TAG', 'TPF_TAG', 'FRAUD_TAG', 'FPD_TAG', 'BAD_TAG', 'LOSS_OR_BAL_AMT', 'GOOD_TAG', 'vantage_v4_score']\n", "DATA_PATH = '/work/users/c18882a/projects/telco/data' # path to save attribute on local (for univariate, modeling etc.)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_lines_from_text_file(file_name):\n", "    with open(file_name, 'r') as f:\n", "        lines = [x.strip('\\n') for x in f.readlines()]\n", "    return lines\n", "\n", "def write_lines_to_text_file(L, file_name):\n", "    with open(file_name, 'w') as f:\n", "        f.write('\\n'.join(L))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data preparation\n", "* Read and union application attributes from each carrier"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## combine data from all carriers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def check_overlap_period(df_list):\n", "    overlap_start = '190001'\n", "    overlap_end = '210001'\n", "    for df in df_list:\n", "        overlap_start = max(overlap_start, str(df.select(F.min('ts').alias('min_month')).take(1)[0]['min_month']))\n", "        overlap_end = min(overlap_end, str(df.select(F.max('ts').alias('max_month')).take(1)[0]['max_month']))\n", "    return overlap_start, overlap_end\n", "\n", "def check_uid_stats(df_uid):\n", "    count = df_uid.count()\n", "    for pii in ['addr', 'phone', 'email', 'ssn']:\n", "        null_count = df_uid.filter(\"{}_uid is null\".format(pii)).count()\n", "        print(\"{} out of {} ({}) do not have {}_uid\".format(null_count, count, round(float(null_count)/count, 4), pii))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## read application attributes from each carrier\n", "tm_app_attr = spark.read.parquet('/p/telco/attributes/application_attributes/tmobile_202006_202208')\n", "uc_app_attr = spark.read.parquet('/p/telco/attributes/application_attributes/uscellular_202007_202212')\n", "vz_app_attr = spark.read.parquet('/p/telco/attributes/application_attributes/verizon_202006_202206_clean') # removed more test-only records\n", "at_app_attr = spark.read.parquet('/p/telco/attributes/application_attributes/att_202108_202307')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["at_app_attr.printSchema()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## removed repetitive applications in online channel for Verizon and ATT\n", "tm_app_attr.count(), uc_app_attr.count(), vz_app_attr.count(), at_app_attr.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["16471774 + 728859 + 16980543 + 15578148"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tm_app_attr.count(), uc_app_attr.count(), vz_app_attr.count(), at_app_attr.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["16471774 + 728859 + 18888929 + 16133145"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## combine data from all carriers\n", "df_union = tm_app_attr.withColumn('client_name', F.lit('tmobile'))\\\n", "                      .union(uc_app_attr.withColumn('client_name', F.lit('uscellular')))\\\n", "                      .union(vz_app_attr.withColumn('client_name', F.lit('verizon')))\\\n", "                      .union(at_app_attr.withColumn('client_name', F.lit('att')))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overlap_start, overlap_end = check_overlap_period([tm_app_attr, uc_app_attr, vz_app_attr, at_app_attr])\n", "print(overlap_start, overlap_end)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["overlap_start, overlap_end = '202108', '202206'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## select applications in the time period for modeling\n", "df_base = df_union.filter(f\"ts >= '{overlap_start}' and ts <= '{overlap_end}'\")\n", "# df_base = df_union.filter(f\"month >= '{overlap_start}' and month <= '{overlap_end}'\").cache()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## after cleaning online channel for VZ/AT and using IDX PIN (including ATT), also removed more test-only records in Verizon\n", "df_base.select(F.countDistinct('APP_NUMBER'), F<PERSON>countDistinct('eid'), <PERSON><PERSON>countDistinct('opin'), <PERSON><PERSON>countDistinct('APP_NUMBER', 'eid'), F.count(F.lit(1))).show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## after cleaning online channel for VZ/AT and using IDX PIN (including ATT), also removed more test-only records in Verizon\n", "df_base.select(F.countDistinct('APP_NUMBER'), F<PERSON>countDistinct('eid'), <PERSON><PERSON>countDistinct('opin'), <PERSON><PERSON>countDistinct('APP_NUMBER', 'eid'), F.count(F.lit(1))).show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## after cleaning online channel for VZ/AT and using IDX PIN (including ATT)\n", "df_base.select(F.countDistinct('APP_NUMBER'), F<PERSON>countDistinct('eid'), <PERSON><PERSON>countDistinct('opin'), <PERSON><PERSON>countDistinct('APP_NUMBER', 'eid'), F.count(F.lit(1))).show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## after cleaning online channel for VZ/AT and using IDX PIN (except for ATT)\n", "df_base.select(F.countDistinct('APP_NUMBER'), F<PERSON>countDistinct('eid'), <PERSON><PERSON>countDistinct('opin'), <PERSON><PERSON>countDistinct('APP_NUMBER', 'eid'), F.count(F.lit(1))).show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_base.select(F.countDistinct('APP_NUMBER'), F<PERSON>countDistinct('eid'), <PERSON><PERSON>countDistinct('opin'), <PERSON><PERSON>countDistinct('APP_NUMBER', 'eid'), F.count(F.lit(1))).show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_uid_stats(df_base)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(df_base.filter(\"ts >= 202202 and ts <= 202206\").filter(\"addr_uid is null\").count())\n", "print(df_base.filter(\"ts >= 202202 and ts <= 202206\").filter(\"phone_uid is null\").count())\n", "print(df_base.filter(\"ts >= 202202 and ts <= 202206\").filter(\"email_uid is null\").count())\n", "print(df_base.filter(\"ts >= 202202 and ts <= 202206\").filter(\"ssn_uid is null\").count())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_uid_stats(df_base)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(df_base.filter(\"sales_channel = -1\"), 'client_name')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(df_base.filter(\"id_type = -1\"), 'client_name')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(df_base, 'is_activated')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## preprocessing\n", "* Mask non-personal phone and email"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["device_fillna_dict = {'NUM_LINES': 0,\n", "                      'DEVICE_FINANCED': 0, \n", "                      'AMT_FINANCED': 0}\n", "\n", "fraud_fillna_dict = {\n", "     'FPF_TAG': 0,\n", "     'TPF_TAG': 0,\n", "     'FRAUD_TAG': 0,\n", "     'FPD_TAG': 0,\n", "     'BAD_TAG': 0,\n", "     'OTHER_BAD_TAG': 0,\n", "     'GOOD_TAG': 0,\n", "     'LOSS_OR_BAL_AMT': 0,\n", "     'NO_USAGE': 0}\n", "\n", "def adjust_tags(df_app):\n", "    '''\n", "    Calculate pseudo-FPF and augmented FPF tag, adjust FRAUD and FPD tag using the pseudo-FPF tag\n", "    '''\n", "    return df_app.fillna(fraud_fillna_dict)\\\n", "                 .withColumn('PSEUDO_FPF_TAG', \n", "                                 F.when(F.col('client_name') != 'verizon', F.col('is_activated') * F.col('FPD_TAG') * F.col('NO_USAGE'))\\\n", "                                  .otherwise(F.col('FPF_TAG')))\\\n", "                 .withColumn('FPF_TAG', F.when(F.col('client_name') != 'verizon', F.col('FPF_TAG')).otherwise(F.lit(0)))\\\n", "                 .withColumnRenamed('FPF_TAG', 'ORIGINAL_FPF_TAG')\\\n", "                 .withColumnRenamed('FPD_TAG', 'ORIGINAL_FPD_TAG')\\\n", "                 .withColumn('FPF_TAG',((<PERSON>.col('ORIGINAL_FPF_TAG') == 1) | (F.col('PSEUDO_FPF_TAG') == 1)).cast('int'))\\\n", "                 .withColumn('FRAUD_TAG',((F.col('FRAUD_TAG') == 1) | (F.col('PSEUDO_FPF_TAG') == 1)).cast('int'))\\\n", "                 .withColumn('FPD_TAG', ((<PERSON><PERSON>col('ORIGINAL_FPD_TAG') == 1) & (<PERSON>.col('PSEUDO_FPF_TAG') == 0)).cast('int'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["non_personal_phone_list = read_lines_from_text_file('telco_non_personal_phone_list.txt')\n", "non_personal_email_list = read_lines_from_text_file('telco_non_personal_email_list.txt')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_base = adjust_tags(df_base.fillna(device_fillna_dict))\\\n", "    .withColumn('phone_uid', F.when(F.col('phone_uid').isin(non_personal_phone_list), F.lit(None)).otherwise(F.col('phone_uid')))\\\n", "    .withColumn('email_uid', F.when(F.col('email_uid').isin(non_personal_email_list), F.lit(None)).otherwise(<PERSON><PERSON>col('email_uid')))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_uid_stats(df_base)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_uid_stats(df_base)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(df_base.filter(\"ts >= 202202 and ts <= 202206\").filter(\"addr_uid is null\").count())\n", "print(df_base.filter(\"ts >= 202202 and ts <= 202206\").filter(\"phone_uid is null\").count())\n", "print(df_base.filter(\"ts >= 202202 and ts <= 202206\").filter(\"email_uid is null\").count())\n", "print(df_base.filter(\"ts >= 202202 and ts <= 202206\").filter(\"ssn_uid is null\").count())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## check non-personal UID\n", "* Identify corporate or business address\n", "* Identify non-personal phone and email\n", "* Create a 'blacklist' so that UID is set to null for non-personal PII\n", "* For now, we blacklist all phones and emails that are associated with >= 100 consumers. May require future study to check patterns of such phones and emails (e.g., are they from certain channel)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''\n", "Likely corporate address:\n", ";1;;VERIZON;;WAY;;07920;1025\n", ";103;;AVE ORTEGON;;;;00966;2505\n", "\n", "Likely business address:\n", ";2001;;EDMUND HALLEY;;DR;;20191;1132\n", ";30;;INDEPENDENCE;;BLVD;;07059;6747\n", ";17330;;PRESTON;;RD;;75252;5728\n", "'''"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(tm_app_attr, 'addr_uid', sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(uc_app_attr, 'addr_uid', sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## after masking empty address\n", "check_distribution(uc_app_attr, 'addr_uid', sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(vz_app_attr, 'addr_uid', sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## after removing test-only records and masking address without line1 and line2\n", "check_distribution(vz_app_attr, 'addr_uid', sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(at_app_attr, 'addr_uid', sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## after masking corporate and business addresses\n", "check_distribution(at_app_attr, 'addr_uid', sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_base.filter(\"addr_uid is null\").count(), df_base.filter(\"addr_uid = ''\").count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## find spurious address\n", "check_distribution(df_base, 'addr_uid', sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_base.filter(\"addr_uid = ';600;;HIDDEN;;RDG;;75038;3809'\").count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_base.filter(<PERSON>.trim(F.col('addr_uid')) == ';;;;').count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(df_base.filter(F.trim(F.col('addr_uid')) == ';;;;'), 'client_name')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(tm_app_attr, 'email_uid', sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(uc_app_attr, 'email_uid', sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(vz_app_attr, 'email_uid', sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(at_app_attr, 'email_uid', sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_union.groupBy('email_uid').agg(F.countDistinct('opin').alias('num_pin')).sort('num_pin', ascending=False)\\\n", "        .filter(\"num_pin >= 100\").count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_union.groupBy('email_uid').agg(F.countDistinct('opin').alias('num_pin')).sort('num_pin', ascending=False)\\\n", "        .filter(\"num_pin >= 100\").show(500, truncate=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## These emails link to >= 100 consumers but look like truly personal emails, so still keep them\n", "email_uid_to_keep = \"\"\"email:<EMAIL>\n", "email:<EMAIL>\n", "email:diose<PERSON><PERSON>@gmail.com\n", "email:david<PERSON><EMAIL>\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:frank<PERSON><PERSON>@msn.com\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:k<PERSON><PERSON>@yahoo.com\n", "email:<EMAIL>\n", "email:<EMAIL>\n", "email:<PERSON><PERSON><PERSON><PERSON>@gmail.com\n", "email:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com\"\"\".split('\\n')\n", "\n", "MAX_EMAIL_PIN_FREQ = 100"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## select high frequent and unually named emails as blacklist\n", "df_email_freq = df_union\\\n", "    .filter(\"email_uid is not null\")\\\n", "    .groupBy('email_uid')\\\n", "    .agg(F.countDistinct('opin').alias('num_pin'))\\\n", "    .sort('num_pin', ascending=False)\n", "fake_emails = [row['email_uid'] for row in df_email_freq.filter(f\"num_pin >= {MAX_EMAIL_PIN_FREQ}\").collect()]\n", "fake_emails = list(set(fake_emails) - set(email_uid_to_keep))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(fake_emails)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fake_emails"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["write_lines_to_text_file(fake_emails, 'telco_non_personal_email_list.txt')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(tm_app_attr, 'phone_uid', sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(uc_app_attr, 'phone_uid', sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(vz_app_attr, 'phone_uid', sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(at_app_attr, 'phone_uid', sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_union.groupBy('phone_uid').agg(F.countDistinct('opin').alias('num_pin')).sort('num_pin', ascending=False)\\\n", "        .filter(\"num_pin >= 200\").count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Many highly frequent phones are national stores (e.g. Walmart) and local Telco stores (mostly from T-Mobile?)\n", "df_union.groupBy('phone_uid').agg(F.countDistinct('opin').alias('num_pin')).sort('num_pin', ascending=False)\\\n", "        .filter(\"num_pin >= 100\").show(200, truncate=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_union.groupBy('phone_uid').agg(F.countDistinct('opin').alias('num_pin')).sort('num_pin', ascending=False)\\\n", "        .filter(\"num_pin >= 100 and num_pin <= 200\").show(200, truncate=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(df_union.filter(\"phone_uid = 'phone_number:3367943389'\"), 'client_name')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## select high frequent and T-Mobile TPR channel phone numbers as blacklist\n", "MAX_PHONE_PIN_FREQ = 100\n", "\n", "df_phone_freq = df_union\\\n", "    .filter(\"phone_uid is not null\")\\\n", "    .groupBy('phone_uid')\\\n", "    .agg(F.countDistinct('opin').alias('num_pin'))\\\n", "    .sort('num_pin', ascending=False)\n", "\n", "# df_tm_store_phones = df_union.filter(\"sales_channel = 1 and phone_uid is not null\").select('phone_uid').distinct()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["high_freq_phones = [row['phone_uid'] for row in df_phone_freq.filter(f\"num_pin >= {MAX_PHONE_PIN_FREQ}\").collect()]\n", "# tm_store_phones = [row['phone_uid'] for row in df_tm_store_phones.collect()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(high_freq_phones)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["write_lines_to_text_file(high_freq_phones, 'telco_non_personal_phone_list.txt')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## generate application history by month (no longer needed!)\n", "* Note: need to include unpinned applications\n", "* Note (2023/10/17): Now, no need to generate such application history based on opin since it would affect PII-lookup attributes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["consumer_key = 'opin'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## also include unpinned applications in each month\n", "for month in pd.date_range(start='2022-02-01', end='2022-06-01', freq='MS').strftime('%Y-%m-%d').tolist():\n", "    log(f'====== Generate app history for month {month} ======')\n", "    one_year_ago = pd.date_range(end=month, periods=13, freq='MS').strftime('%Y-%m-%d').tolist()[0]\n", "    ts = month.replace('-', '')[:6]\n", "    ts_one_year_ago = one_year_ago.replace('-', '')[:6]\n", "    log(f'------ Retrieve app history from {one_year_ago} to {month} (inclusive) ------')\n", "    df_eid = df_base.filter(f\"ts = {ts}\").select(consumer_key).distinct()\n", "    df_pinned = df_base.join(df_eid, consumer_key).filter(f\"ts >= {ts_one_year_ago} and ts <= {ts}\")\n", "    df_unpinned = df_base.filter(f\"ts = {ts} and {consumer_key} is null\").select(df_pinned.columns)\n", "    df_pinned.union(df_unpinned)\\\n", "             .write.mode('overwrite').parquet(f'/p/telco/attributes/app_hist/all_carriers/ts={ts}')\n", "log(f'====== Done ======')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_hist_test = spark.read.parquet('/p/telco/attributes/app_hist/all_carriers/ts=202206')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_hist_test.filter(\"eid is null\").count(), df_hist_test.filter(\"opin is null\").count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_base.filter(f\"ts = 202206\").filter(\"eid is null\").count(), df_base.filter(f\"ts = 202206\").filter(\"opin is null\").count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_hist_test.filter(f\"ts = 202206\").select(F.countDistinct('APP_NUMBER'), F.countDistinct('eid'), F<PERSON>countDistinct('opin'), F<PERSON>countDistinct('APP_NUMBER', 'eid'), F.count(F.lit(1))).show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_base.filter(f\"ts = 202206\").select(F.countDistinct('APP_NUMBER'), F.countDistinct('eid'), F<PERSON>countDistinct('opin'), F<PERSON>countDistinct('APP_NUMBER', 'eid'), F.count(F.lit(1))).show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_hist_test.filter(\"app_datetime_unix is null\").count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["[col for col in df_base.columns if 'TAG' in col]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Application attributes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["application_attr_list = [\n", " 'day_of_week',\n", " 'time_of_day',\n", " 'during_office_hour',\n", " 'sales_channel',\n", " 'has_ssn',\n", " 'has_dob',\n", " 'has_phone',\n", " 'has_email',\n", " 'has_id',\n", " 'id_type',\n", " 'portin_ind',\n", " 'home_zip_store_zip_match'\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_app_attr_sample = df_base.select(application_attr_list).sample(0.002).toPandas()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_app_attr_sample.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_base.filter(\"ts >= 202202 and ts <= 202206\")\\\n", "    .select('APP_NUMBER', *(application_attr_list + useful_fields))\\\n", "    .sample(0.05).to<PERSON><PERSON><PERSON>()\\\n", "    .to_parquet(os.path.join(DATA_PATH, 'attribute/application_attr.all_carriers.sample.pq'), engine='pyarrow')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_base.filter(\"ts >= 202202 and ts <= 202206\").count()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Consortium attributes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calc_stats(df, bad_tag='BAD_TAG', groupby_cols=None, return_stats=False):\n", "    \n", "    if groupby_cols is not None:\n", "        df = df.groupBy(groupby_cols)\n", "    \n", "    df_stats = df.agg(F.count(F.lit(1)).alias('num_app'),\n", "                      F.sum('is_activated').alias('num_activated'),\n", "                      F.sum(F.col('is_activated') * F.col(bad_tag)).alias('num_bad'))\\\n", "                 .withColumn('activation_rate', F.col('num_activated') / F.col('num_app'))\\\n", "                 .withColumn('bad_rate', F.col('num_bad') / F.col('num_activated'))\n", "    \n", "    if groupby_cols is not None:\n", "        df_stats = df_stats.sort(groupby_cols)\n", "    \n", "    if return_stats:\n", "        return df_stats\n", "    else:\n", "        df_stats.show(100, truncate=False)\n", "        \n", "def calc_stats_for_attr_by_bins(df, attr, bin_cuts, bad_tag='BAD_TAG', special_values=None):\n", "    \n", "    df_list = []\n", "    \n", "    if special_values is not None:\n", "        for v in special_values:\n", "            label = f'{v}'\n", "            df_bin = calc_stats(df.filter(f\"{attr} = {v}\"), bad_tag=bad_tag, return_stats=True)\n", "            df_list.append(df_bin.withColumn('bin', F.lit(label)).select('bin', *df_bin.columns))\n", "    \n", "    for i, c in enumerate(bin_cuts[:-1]):\n", "        label = f'[{c},{bin_cuts[i+1]})'\n", "        df_bin = calc_stats(df.filter(f\"{attr} >= {c} and {attr} < {bin_cuts[i+1]}\"), bad_tag=bad_tag, return_stats=True)\n", "        df_list.append(df_bin.withColumn('bin', F.lit(label)).select('bin', *df_bin.columns))\n", "        \n", "    label = f'>={bin_cuts[-1]}'\n", "    df_bin = calc_stats(df.filter(f\"{attr} >= {bin_cuts[-1]}\"), bad_tag=bad_tag, return_stats=True)\n", "    df_list.append(df_bin.withColumn('bin', F.lit(label)).select('bin', *df_bin.columns))\n", "    \n", "    return  reduce(lambda x, y: x.union(y), df_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["consumer_key = 'opin'\n", "windows = [7, 30, 91, 183]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## consumer profile attributes\n", "* Calculated from historical activities of the consumers"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["APP_REPORT_DELAY = 1/24 # delay is assumed to be 1 hour as applications are received in real-time stream and only require preliminary preprocessing and logging\n", "DEVICE_REPORT_DELAY = 5\n", "FRAUD_REPORT_DELAY = 91\n", "MIN_WINDOW_LEN = 5\n", "NUM_SECOND_IN_A_DAY = 86400\n", "\n", "days = lambda i: int(i * NUM_SECOND_IN_A_DAY)\n", "consumer_date_window = W.partitionBy(F.col(consumer_key)).orderBy(F.col('app_datetime_unix'))\n", "\n", "\n", "\n", "def get_lookback_window(window_start_days_ago, window_end_days_ago, date_col, partition_col=consumer_key):\n", "    '''\n", "    Require date_col is unix timestamp\n", "    '''\n", "    if isinstance(partition_col, str):\n", "        partition_col = [partition_col]\n", "    return (<PERSON>.partitionBy(*partition_col)\n", "             .orderBy(F.col(date_col))\n", "             .rangeBetween(-days(window_start_days_ago), -days(window_end_days_ago)))\n", "\n", "\n", "def calc_app_hist_attr(df_base, window_len_list, lookback_cutoff=APP_REPORT_DELAY, date_col='app_datetime_unix', suffix=None):\n", "    '''\n", "    Attributes in this function can be calculated on each client or combining all clients\n", "    Some address matching attributes are to be implemented\n", "    '''\n", "    \n", "    def time_gap_attr(ts_list, curr_time, lookback_cutoff=APP_REPORT_DELAY):\n", "        '''\n", "        ts_list is a list of unix timestamps of all applications during the effective time window from a consumer\n", "        '''\n", "\n", "        if len(ts_list) == 0:\n", "            return (-1.0, -1.0)\n", "\n", "        ts_list = sorted(ts_list)\n", "        gaps = []\n", "        for i in range(len(ts_list)-1):\n", "            gaps.append(ts_list[i+1] - ts_list[i])\n", "        gaps.append(curr_time - ts_list[-1])\n", "\n", "        return (round(min(gaps) / NUM_SECOND_IN_A_DAY, 2), round(sum(gaps) / NUM_SECOND_IN_A_DAY / len(gaps), 2))\n", "    \n", "    schema = T.StructType([\n", "        <PERSON><PERSON>('min_time_gap', T.<PERSON>(), F<PERSON><PERSON>),\n", "        <PERSON><PERSON>('avg_time_gap', T.<PERSON>T<PERSON>(), False)])\n", "    \n", "    time_gap_attr_udf = F.udf(time_gap_attr, schema)\n", "    \n", "    window_len_list = [x for x in window_len_list if x - lookback_cutoff >= MIN_WINDOW_LEN]\n", "        \n", "    df_app_hist_attr = df_base\n", "    for window_len in window_len_list:\n", "        lookback_window = get_lookback_window(window_len, lookback_cutoff, date_col)\n", "        df_app_hist_attr = df_app_hist_attr\\\n", "              .withColumn(f'num_app_{window_len}d', F.count('APP_NUMBER').over(lookback_window))\\\n", "              .withColumn(f'days_since_last_app_{window_len}d', F.round((F.col('app_datetime_unix') - F.max('app_datetime_unix').over(lookback_window)) / NUM_SECOND_IN_A_DAY, 2))\\\n", "              .withColumn(f'days_since_first_app_{window_len}d', F.round((F.col('app_datetime_unix') - F.min('app_datetime_unix').over(lookback_window)) / NUM_SECOND_IN_A_DAY, 2))\\\n", "              .withColumn(f'ts_list', F.collect_list('app_datetime_unix').over(lookback_window))\\\n", "              .withColumn(f'time_gap_attr', time_gap_attr_udf('ts_list', 'app_datetime_unix'))\\\n", "              .withColumn(f'min_app_date_gap_{window_len}d', <PERSON>.col('time_gap_attr').getField('min_time_gap'))\\\n", "              .withColumn(f'avg_app_date_gap_{window_len}d', <PERSON><PERSON>col('time_gap_attr').getField('avg_time_gap'))\\\n", "              .withColumn('sales_channel', F.when(F.col('sales_channel') >= 0, F.col('sales_channel')).otherwise(F.lit(None)))\\\n", "              .withColumn(f'num_sales_channel_{window_len}d', \\\n", "                              F.when(F.col(f'num_app_{window_len}d') > 0, F.approx_count_distinct('sales_channel').over(lookback_window))\\\n", "                               .otherwise(F.lit(-1)))\\\n", "              .withColumn(f'same_sales_channel_with_last_app_{window_len}d', \\\n", "                              F.when(F.col(f'num_app_{window_len}d') > 0, (F.col('sales_channel') == F.last('sales_channel').over(lookback_window)).cast('int'))\\\n", "                               .otherwise(F.lit(-1)))\\\n", "              .withColumn('id_type', F.when(F.col('id_type') >= 0, F.col('id_type')).otherwise(F.lit(None)))\\\n", "              .withColumn(f'num_id_type_{window_len}d', F.when(F.col(f'num_app_{window_len}d') > 0, F.approx_count_distinct('id_type').over(lookback_window))\\\n", "                                                         .otherwise(F.lit(-1)))\\\n", "              .withColumn(f'same_id_type_with_last_app_{window_len}d', \\\n", "                              F.when(F.col(f'num_app_{window_len}d') > 0, (F.col('id_type') == F.last('id_type').over(lookback_window)).cast('int'))\\\n", "                               .otherwise(F.lit(-1)))\\\n", "              .fillna({f'same_sales_channel_with_last_app_{window_len}d': -2, f'same_id_type_with_last_app_{window_len}d': -2})\\\n", "              .withColumn(f'pct_app_in_weekday_{window_len}d', F.sum(F.col('day_of_week').isin([1,2,3,4,5]).cast('int')).over(lookback_window) / F.col(f'num_app_{window_len}d'))\\\n", "              .withColumn(f'pct_app_during_day_{window_len}d', F.sum(F.col('time_of_day').isin([3,4,5]).cast('int')).over(lookback_window) / F.col(f'num_app_{window_len}d'))\\\n", "              .withColumn(f'pct_app_during_office_hour_{window_len}d', F.sum((F.col('during_office_hour') == 1).cast('int')).over(lookback_window) / F.col(f'num_app_{window_len}d'))\\\n", "              .withColumn(f'pct_app_has_dob_{window_len}d', F.sum('has_dob').over(lookback_window) / F.col(f'num_app_{window_len}d'))\\\n", "              .withColumn(f'pct_app_has_phone_{window_len}d', F.sum('has_phone').over(lookback_window) / F.col(f'num_app_{window_len}d'))\\\n", "              .withColumn(f'pct_app_has_email_{window_len}d', F.sum('has_email').over(lookback_window) / F.col(f'num_app_{window_len}d'))\\\n", "              .withColumn(f'pct_app_has_ssn_{window_len}d', F.sum((F.col('has_ssn') > 0).cast('int')).over(lookback_window) / F.col(f'num_app_{window_len}d'))\\\n", "              .drop('ts_list', 'time_gap_attr')\n", "        \n", "    ## note: min constraint for num_sales_channel and num_id_type should be 1 instead of 0\n", "        \n", "    new_attr = [col for col in df_app_hist_attr.columns if col not in df_base.columns]\n", "    if suffix is not None:\n", "        new_attr = [f'{col} AS {col}_{suffix}' for col in new_attr]\n", "\n", "    return df_app_hist_attr.selectExpr('APP_NUMBER', *new_attr).fillna(-1)\n", "\n", "\n", "def calc_app_hist_attr_by_client(df_base, window_len_list, lookback_cutoff=APP_REPORT_DELAY, date_col='app_datetime_unix'):\n", "    \n", "    ## combining applications from all clients\n", "    print(f'------ Calculating app hist attributes combining all clients ------')\n", "    df_app_hist_attr_all_client = \\\n", "        calc_app_hist_attr(\n", "            df_base,\n", "            window_len_list, \n", "            lookback_cutoff=lookback_cutoff, \n", "            date_col=date_col)\n", "    \n", "    ## for each client separately\n", "    client_list = sorted([row['client_name'] for row in df_base.select('client_name').distinct().collect()])\n", "    df_attr_list = []\n", "    for client in client_list:\n", "        print(f'------ Calculating app hist attributes for {client} ------')\n", "        df_app_hist_attr_this_client = \\\n", "            calc_app_hist_attr(\n", "                df_base.filter(f\"client_name = '{client}'\"),\n", "                window_len_list, \n", "                lookback_cutoff=lookback_cutoff, \n", "                date_col=date_col,\n", "                suffix='with_client')\n", "        df_attr_list.append(df_app_hist_attr_this_client)\n", "    \n", "    return df_app_hist_attr_all_client.join(reduce(DataFrame.unionAll, df_attr_list), 'APP_NUMBER')\n", "\n", "\n", "def calc_device_hist_attr(df_base, window_len_list, lookback_cutoff=DEVICE_REPORT_DELAY, date_col='app_datetime_unix', suffix=None):\n", "        \n", "    df_device_hist_attr = df_base.fillna(device_fillna_dict)\n", "                          \n", "    window_len_list = [x for x in window_len_list if x - lookback_cutoff >= MIN_WINDOW_LEN]\n", "    \n", "    for window_len in window_len_list:\n", "        lookback_window = get_lookback_window(window_len, lookback_cutoff, date_col)\n", "        curr_cols = df_device_hist_attr.columns\n", "        df_device_hist_attr = df_device_hist_attr\\\n", "             .withColumn(f'num_app_{window_len}d', F.count('APP_NUMBER').over(lookback_window))\\\n", "             .withColumn(f'num_activated_acct_{window_len}d', \\\n", "                             F.when(F.col(f'num_app_{window_len}d') > 0, F.sum('is_activated').over(lookback_window))\\\n", "                              .otherwise(F.lit(-1)))\\\n", "             .withColumn(f'num_rejection_{window_len}d', \\\n", "                             F.when(F.col(f'num_app_{window_len}d') > 0, F.sum(1 - F.col('is_activated')).over(lookback_window))\\\n", "                              .otherwise(F.lit(-1)))\\\n", "             .withColumn(f'pct_app_with_acct_{window_len}d', \\\n", "                             F.when(F.col(f'num_app_{window_len}d') > 0, F.col(f'num_activated_acct_{window_len}d') / F.col(f'num_app_{window_len}d'))\\\n", "                              .otherwise(F.lit(-1)))\\\n", "             .withColumn(f'num_lines_{window_len}d', F.sum(F.col('is_activated') * F.col('NUM_LINES')).over(lookback_window))\\\n", "             .withColumn(f'pct_acct_with_device_financed_{window_len}d', F.sum((F.col('is_activated') * F.col('DEVICE_FINANCED') > 0).cast('int')).over(lookback_window) / F.col(f'num_activated_acct_{window_len}d'))\\\n", "             .withColumn(f'total_device_financed_{window_len}d', F.sum(F.col('is_activated') * F.col('DEVICE_FINANCED')).over(lookback_window))\\\n", "             .withColumn(f'max_device_financed_{window_len}d', F.max(F.col('is_activated') * F.col('DEVICE_FINANCED')).over(lookback_window))\\\n", "             .withColumn(f'total_amt_financed_{window_len}d', F.sum(F.col('is_activated') * F.col('AMT_FINANCED')).over(lookback_window))\\\n", "             .withColumn(f'max_amt_financed_{window_len}d', F.max(F.col('is_activated') * F.col('AMT_FINANCED')).over(lookback_window))\\\n", "             .withColumn(f'min_amt_financed_{window_len}d', F.min(F.col('is_activated') * F.col('AMT_FINANCED')).over(lookback_window))\\\n", "             .withColumn(f'avg_amt_financed_{window_len}d', \n", "                             F.when(F.col(f'total_device_financed_{window_len}d') > 0, \n", "                                    F.col(f'total_amt_financed_{window_len}d') / F.col(f'total_device_financed_{window_len}d'))\\\n", "                              .otherwise(F.lit(0)))\\\n", "             .withColumn(f'activated_acct_in_last_app_{window_len}d', \n", "                             F.when(F.col(f'num_app_{window_len}d') > 0, (F.last('is_activated').over(lookback_window) == 1).cast('int'))\\\n", "                              .otherwise(F.lit(-1)))\\\n", "             .withColumn(f'num_device_financed_in_last_app_{window_len}d', \n", "                             F.when(F.col(f'activated_acct_in_last_app_{window_len}d') == 1, F.last(F.col('is_activated') * F.col('DEVICE_FINANCED')).over(lookback_window))\\\n", "                              .when(F.col(f'activated_acct_in_last_app_{window_len}d') == 0, F.lit(-3))\\\n", "                              .otherwise(F.lit(-1)))\\\n", "             .withColumn(f'amt_financed_in_last_app_{window_len}d', \n", "                             F.when(F.col(f'activated_acct_in_last_app_{window_len}d') == 1, F.last(F.col('is_activated') * F.col('AMT_FINANCED')).over(lookback_window))\\\n", "                              .when(F.col(f'activated_acct_in_last_app_{window_len}d') == 0, F.lit(-3))\\\n", "                              .otherwise(F.lit(-1)))\\\n", "        \n", "        ## Assign default values for no past application (-1) and no past account activation (-2)\n", "        for col in [col for col in df_device_hist_attr.columns if col not in curr_cols and any(x in col for x in ['num_lines', '_financed_'])]:\n", "            df_device_hist_attr = df_device_hist_attr\\\n", "                .withColumn(col, F.when(F.col(f'num_app_{window_len}d') == 0, F.lit(-1))\\\n", "                                  .when(F.col(f'num_activated_acct_{window_len}d') == 0, F.lit(-2))\\\n", "                                  .otherwise(<PERSON><PERSON>col(col)))\n", "        df_device_hist_attr = df_device_hist_attr.drop(f'num_app_{window_len}d')\n", "    \n", "    new_attr = [col for col in df_device_hist_attr.columns if col not in df_base.columns]\n", "    if suffix is not None:\n", "        new_attr = [f'{col} AS {col}_{suffix}' for col in new_attr]\n", "    \n", "    return df_device_hist_attr.selectExpr('APP_NUMBER', *new_attr)\n", "\n", "\n", "def calc_device_hist_attr_by_client(df_base, window_len_list, lookback_cutoff=DEVICE_REPORT_DELAY, date_col='app_datetime_unix'):\n", "    \n", "    ## combining applications from all clients\n", "    print(f'------ Calculating device hist attributes combining all clients ------')\n", "    df_device_hist_attr_all_client = \\\n", "        calc_device_hist_attr(\n", "            df_base,\n", "            window_len_list, \n", "            lookback_cutoff=lookback_cutoff, \n", "            date_col=date_col)\n", "    \n", "    ## for each client separately\n", "    client_list = sorted([row['client_name'] for row in df_base.select('client_name').distinct().collect()])\n", "    df_attr_list = []\n", "    for client in client_list:\n", "        print(f'------ Calculating device hist attributes for {client} ------')\n", "        df_device_hist_attr_this_client = \\\n", "            calc_device_hist_attr(\n", "                df_base.filter(f\"client_name = '{client}'\"),\n", "                window_len_list, \n", "                lookback_cutoff=lookback_cutoff, \n", "                date_col=date_col,\n", "                suffix='with_client')\n", "        df_attr_list.append(df_device_hist_attr_this_client)\n", "    \n", "    return df_device_hist_attr_all_client.join(reduce(DataFrame.unionAll, df_attr_list), 'APP_NUMBER')\n", "\n", "\n", "bad_tags = ['FPF_TAG', 'TPF_TAG', 'FRAUD_TAG', 'FPD_TAG', 'BAD_TAG', 'OTHER_BAD_TAG']\n", "\n", "def calc_fraud_hist_attr(df_base, window_len_list, lookback_cutoff=FRAUD_REPORT_DELAY, date_col='app_datetime_unix', suffix=None):\n", "    \n", "    df_fraud_hist_attr = df_base\\\n", "        .withColumn('fraud_type', F.when((F.col('is_activated') == 0) | (F.col('GOOD_TAG') == 1), F.lit(None))\\\n", "                                   .when(F.col('FPF_TAG') == 1, F.lit(1))\\\n", "                                   .when(F.col('TPF_TAG') == 1, F.lit(2))\\\n", "                                   .when(F.col('FRAUD_TAG') == 1, F.lit(3))\\\n", "                                   .when(F.col('FPD_TAG') == 1, F.lit(4))\\\n", "                                   .otherwise(F.lit(5)))\n", "                          \n", "    window_len_list = [x for x in window_len_list if x - lookback_cutoff >= MIN_WINDOW_LEN]\n", "    \n", "    for window_len in window_len_list:\n", "        lookback_window = get_lookback_window(window_len, lookback_cutoff, date_col)\n", "        curr_cols = df_fraud_hist_attr.columns\n", "        df_fraud_hist_attr = df_fraud_hist_attr\\\n", "            .withColumn(f'num_app_{window_len}d', F.count('APP_NUMBER').over(lookback_window))\\\n", "            .withColumn(f'num_activated_acct_{window_len}d', \\\n", "                             F.when(F.col(f'num_app_{window_len}d') > 0, F.sum('is_activated').over(lookback_window))\\\n", "                              .otherwise(F.lit(-1)))\\\n", "            .withColumn(f'num_fraud_types_{window_len}d', F.when(F.col(f'num_app_{window_len}d') == 0, F.lit(-1))\\\n", "                              .when(F.col(f'num_activated_acct_{window_len}d') == 0, F.lit(-2))\\\n", "                              .otherwise(F.approx_count_distinct('fraud_type').over(lookback_window)))\n", "        \n", "        # create tag-specific attributes\n", "        for tag in bad_tags:\n", "            tag_alias = tag.split('_TAG')[0].lower()\n", "            df_fraud_hist_attr = df_fraud_hist_attr\\\n", "            .withColumn(f'num_{tag_alias}_acct_{window_len}d', F.sum(F.col('is_activated') * F.col(tag)).over(lookback_window))\\\n", "            .withColumn(f'pct_{tag_alias}_acct_{window_len}d', \\\n", "                             F.when(F.col(f'num_activated_acct_{window_len}d') > 0, F.col(f'num_{tag_alias}_acct_{window_len}d') / F.col(f'num_activated_acct_{window_len}d'))\\\n", "                              .otherwise(F.lit(-2)))\\\n", "            .withColumn(f'app_date_of_first_{tag_alias}_acct_{window_len}d', F.min(F.when(F.col(tag) == 1, F.col('app_datetime_unix'))).over(lookback_window))\\\n", "            .withColumn(f'app_date_of_last_{tag_alias}_acct_{window_len}d', F.max(F.when(F.col(tag) == 1, F.col('app_datetime_unix'))).over(lookback_window))\\\n", "            .withColumn(f'days_since_first_{tag_alias}_app_{window_len}d', F.round((F.col('app_datetime_unix') - F.col(f'app_date_of_first_{tag_alias}_acct_{window_len}d')) / NUM_SECOND_IN_A_DAY, 2))\\\n", "            .withColumn(f'days_since_last_{tag_alias}_app_{window_len}d', F.round((F.col('app_datetime_unix') - F.col(f'app_date_of_last_{tag_alias}_acct_{window_len}d')) / NUM_SECOND_IN_A_DAY, 2))\\\n", "            .drop(f'app_date_of_first_{tag_alias}_acct_{window_len}d', f'app_date_of_last_{tag_alias}_acct_{window_len}d')\\\n", "            .withColumn(f'total_{tag_alias}_loss_amt_{window_len}d', F.sum(F.col(tag) * F.col('is_activated') * F.col('LOSS_OR_BAL_AMT')).over(lookback_window))\\\n", "            .withColumn(f'max_{tag_alias}_loss_amt_{window_len}d', F.max(F.col(tag) * F.col('is_activated') * F.col('LOSS_OR_BAL_AMT')).over(lookback_window))\\\n", "            .withColumn(f'min_{tag_alias}_loss_amt_{window_len}d', F.min(F.col(tag) * F.col('is_activated') * F.col('LOSS_OR_BAL_AMT')).over(lookback_window))\\\n", "            .withColumn(f'avg_{tag_alias}_loss_amt_{window_len}d', \n", "                             F.when(F.col(f'num_{tag_alias}_acct_{window_len}d') > 0, \n", "                                    F.col(f'total_{tag_alias}_loss_amt_{window_len}d') / F.col(f'num_{tag_alias}_acct_{window_len}d'))\\\n", "                              .otherwise(F.lit(0)))\n", "        \n", "            ## for fraud related attributes, assign default value -1 when there was no application, -2 when there was no account open, -3 when there was no fraud account\n", "            tag_attr_cols = [col for col in df_fraud_hist_attr.columns if col not in curr_cols and f'_{tag_alias}_' in col]\n", "            for col in tag_attr_cols:\n", "                if any(x in col for x in ['days_since_', '_loss_amt_']):\n", "                    df_fraud_hist_attr = df_fraud_hist_attr\\\n", "                        .withColumn(col, F.when(F.col(f'num_app_{window_len}d') == 0, F.lit(-1))\\\n", "                                          .when(F.col(f'num_activated_acct_{window_len}d') == 0, F.lit(-2))\\\n", "                                          .when(F.col(f'num_{tag_alias}_acct_{window_len}d') == 0, F.lit(-3))\\\n", "                                          .otherwise(<PERSON><PERSON>col(col)))\n", "                else:\n", "                    df_fraud_hist_attr = df_fraud_hist_attr\\\n", "                        .withColumn(col, F.when(F.col(f'num_app_{window_len}d') == 0, F.lit(-1))\\\n", "                                          .when(F.col(f'num_activated_acct_{window_len}d') == 0, F.lit(-2))\\\n", "                                          .otherwise(<PERSON><PERSON>col(col)))\n", "            \n", "        df_fraud_hist_attr = df_fraud_hist_attr.drop(f'num_app_{window_len}d', f'num_activated_acct_{window_len}d')\n", "        \n", "    df_fraud_hist_attr = df_fraud_hist_attr.drop('fraud_type')\n", "    \n", "    new_attr = [col for col in df_fraud_hist_attr.columns if col not in df_base.columns]\n", "    if suffix is not None:\n", "        new_attr = [f'{col} AS {col}_{suffix}' for col in new_attr]\n", "    \n", "    return df_fraud_hist_attr.selectExpr('APP_NUMBER', *new_attr)\n", "\n", "\n", "def calc_fraud_hist_attr_by_client(df_base, window_len_list, lookback_cutoff=FRAUD_REPORT_DELAY, date_col='app_datetime_unix'):\n", "    \n", "    ## combining applications from all clients\n", "    print(f'------ Calculating fraud hist attributes combining all clients ------')\n", "    df_fraud_hist_attr_all_client = \\\n", "        calc_fraud_hist_attr(\n", "            df_base,\n", "            window_len_list, \n", "            lookback_cutoff=lookback_cutoff, \n", "            date_col=date_col)\n", "    \n", "    ## for each client separately\n", "    client_list = sorted([row['client_name'] for row in df_base.select('client_name').distinct().collect()])\n", "    df_attr_list = []\n", "    for client in client_list:\n", "        print(f'------ Calculating fraud hist attributes for {client} ------')\n", "        df_fraud_hist_attr_this_client = \\\n", "            calc_fraud_hist_attr(\n", "                df_base.filter(f\"client_name = '{client}'\"),\n", "                window_len_list, \n", "                lookback_cutoff=lookback_cutoff, \n", "                date_col=date_col,\n", "                suffix='with_client')\n", "        df_attr_list.append(df_fraud_hist_attr_this_client)\n", "    \n", "    return df_fraud_hist_attr_all_client.join(reduce(DataFrame.unionAll, df_attr_list), 'APP_NUMBER')\n", "\n", "\n", "def app_client_hist_attr(struct_list, curr_time, curr_client):\n", "    \n", "    if len(struct_list) == 0:\n", "        return -1, -1.0\n", "    \n", "#     first_app_with_curr_client = int(curr_client not in [item['client_name'] for item in struct_list])\n", "    \n", "    struct_list = sorted(struct_list, key=lambda item: item['app_datetime_unix'])\n", "                          \n", "    c = struct_list[0]['client_name']\n", "    num_app_client_switch = 0\n", "    days_since_last_app_with_another_client = -2.0 # default value: has past application but not with any other client\n", "    for i, item in enumerate(struct_list):\n", "        time, client = item['app_datetime_unix'], item['client_name']\n", "        if client != c:\n", "            num_app_client_switch += 1\n", "            c = client\n", "        if client != curr_client:\n", "            days_since_last_app_with_another_client = round((curr_time - time) / 86400, 2)\n", "    if c != curr_client:\n", "        num_app_client_switch += 1\n", "\n", "    return num_app_client_switch, days_since_last_app_with_another_client\n", "    \n", "schema = T.StructType([\n", "    <PERSON><PERSON>('num_app_client_switch', T.<PERSON>T<PERSON>(), False),\n", "    <PERSON><PERSON>('days_since_last_app_with_another_client', T.<PERSON>(), False)])\n", "\n", "app_client_hist_attr_udf = F.udf(app_client_hist_attr, schema)\n", "\n", "\n", "def device_client_hist_attr(struct_list, curr_time, curr_client):\n", "    \n", "    if len(struct_list) == 0:\n", "        return -1, -1.0\n", "                          \n", "    struct_list = sorted(struct_list, key=lambda item: item)\n", "    \n", "    c = None\n", "    num_acct_client_switch = 0\n", "    days_since_last_rejected_app_with_another_client = -3.0 # default value: has past account but not with any other client\n", "    for i, item in enumerate(struct_list):\n", "        time, client, is_activated = item['app_datetime_unix'], item['client_name'], item['is_activated']\n", "        if is_activated == 1 and c is None:\n", "            c = client\n", "        elif is_activated == 1 and c != client:\n", "            num_acct_client_switch += 1\n", "            c = client\n", "        if is_activated == 0 and client != curr_client:\n", "            days_since_last_rejected_app_with_another_client = round((curr_time - time) / 86400, 2)\n", "    \n", "    return num_acct_client_switch, days_since_last_rejected_app_with_another_client\n", "\n", "schema = T.StructType([\n", "    <PERSON><PERSON>('num_acct_client_switch', T.<PERSON>(), False),\n", "    <PERSON><PERSON>('days_since_last_rejected_app_with_another_client', T.<PERSON>(), False)])\n", "\n", "device_client_hist_attr_udf = F.udf(device_client_hist_attr, schema)\n", "\n", "\n", "def calc_client_hist_attr(df_base, window_len_list, lookback_cutoff_app=APP_REPORT_DELAY, lookback_cutoff_device=DEVICE_REPORT_DELAY, \n", "                           lookback_cutoff_fraud=FRAUD_REPORT_DELAY, date_col='app_datetime_unix'):\n", "    \n", "    print('------ Calculating client count attributes ------')\n", "    \n", "    window_len_list_for_app = [x for x in window_len_list if x - lookback_cutoff_app >= MIN_WINDOW_LEN]\n", "    window_len_list_for_device = [x for x in window_len_list if x - lookback_cutoff_device >= MIN_WINDOW_LEN]\n", "    window_len_list_for_fraud = [x for x in window_len_list if x - lookback_cutoff_fraud >= MIN_WINDOW_LEN]\n", "    \n", "    df_client_hist_attr = df_base.fillna(device_fillna_dict).fillna(fraud_fillna_dict)\n", "    \n", "    ## based on past applications\n", "    for window_len in window_len_list_for_app:\n", "        lookback_window_app = get_lookback_window(window_len, lookback_cutoff_app, date_col)\n", "        curr_cols = df_client_hist_attr.columns\n", "        df_client_hist_attr = df_client_hist_attr\\\n", "            .withColumn(f'num_app_{window_len}d', F.count('APP_NUMBER').over(lookback_window_app))\\\n", "            .withColumn(f'num_client_with_app_{window_len}d', F.approx_count_distinct('client_name').over(lookback_window_app))\\\n", "            .withColumn(f'client_of_last_app_{window_len}d', F.last('client_name').over(lookback_window_app))\\\n", "            .withColumn(f'same_client_with_last_app_{window_len}d', (F.col('client_name') == F.col(f'client_of_last_app_{window_len}d')).cast('int'))\\\n", "            .withColumn('list', F.collect_list(F.struct('app_datetime_unix', 'client_name')).over(lookback_window_app))\\\n", "            .withColumn('app_client_hist_attr', app_client_hist_attr_udf('list', 'app_datetime_unix', 'client_name'))\\\n", "            .withColumn(f'num_app_client_switch_{window_len}d', F.col('app_client_hist_attr').getField('num_app_client_switch'))\\\n", "            .withColumn(f'days_since_last_app_with_another_client_{window_len}d', F.col('app_client_hist_attr').getField('days_since_last_app_with_another_client'))\\\n", "            .drop(f'client_of_last_app_{window_len}d', 'list', 'app_client_hist_attr')\n", "\n", "# .withColumn(f'num_client_with_app_{window_len}d', \n", "# F.when(F.col(f'same_client_with_last_app_{window_len}d').isNull(), F.lit(None))\\\n", "#  .otherwise(<PERSON>.col(f'num_client_with_app_{window_len}d')))\\                      \n", "    \n", "        for col in [col for col in df_client_hist_attr.columns if col not in curr_cols and col != f'num_app_{window_len}d']:\n", "            df_client_hist_attr = df_client_hist_attr\\\n", "                .withColumn(col, F.when(F.col(f'num_app_{window_len}d') > 0, F.col(col)).otherwise(F.lit(-1)))\n", "        df_client_hist_attr = df_client_hist_attr.drop(f'num_app_{window_len}d')\n", "        \n", "    ## based on past accounts and devices\n", "    for window_len in window_len_list_for_device:\n", "        lookback_window_device = get_lookback_window(window_len, lookback_cutoff_device, date_col)\n", "        curr_cols = df_client_hist_attr.columns\n", "        df_client_hist_attr = df_client_hist_attr\\\n", "            .withColumn(f'num_app_{window_len}d', F.count('APP_NUMBER').over(lookback_window_device))\\\n", "            .withColumn(f'num_activated_acct_{window_len}d', F.sum('is_activated').over(lookback_window_device))\\\n", "            .withColumn(f'num_client_with_activated_acct_{window_len}d', F.approx_count_distinct(F.when(F.col('is_activated') == 1, F.col('client_name'))).over(lookback_window_device))\\\n", "            .withColumn(f'num_client_with_device_financed_{window_len}d', F.approx_count_distinct(F.when(F.col('DEVICE_FINANCED') > 0, F.col('client_name'))).over(lookback_window_device))\\\n", "            .withColumn('list', F.collect_list(F.struct('app_datetime_unix', 'client_name', 'is_activated')).over(lookback_window_device))\\\n", "            .withColumn('device_client_hist_attr', device_client_hist_attr_udf('list', 'app_datetime_unix', 'client_name'))\\\n", "            .withColumn(f'num_acct_client_switch_{window_len}d', F.col('device_client_hist_attr').getField('num_acct_client_switch'))\\\n", "            .withColumn(f'days_since_last_rejected_app_with_another_client_{window_len}d', F.col('device_client_hist_attr').getField('days_since_last_rejected_app_with_another_client'))\\\n", "            .drop('list', 'device_client_hist_attr')\n", "        \n", "        for col in [col for col in df_client_hist_attr.columns if col not in curr_cols and col not in [f'num_app_{window_len}d', f'num_activated_acct_{window_len}d']]:\n", "            df_client_hist_attr = df_client_hist_attr\\\n", "                .withColumn(col, F.when(F.col(f'num_app_{window_len}d') == 0, F.lit(-1))\\\n", "                                  .when(F.col(f'num_activated_acct_{window_len}d') == 0, F.lit(-2))\\\n", "                                  .otherwise(<PERSON><PERSON>col(col)))\n", "        df_client_hist_attr = df_client_hist_attr.drop(f'num_app_{window_len}d', f'num_activated_acct_{window_len}d')\n", "        \n", "    ## based on past fraud outcome\n", "    for window_len in window_len_list_for_fraud:\n", "        lookback_window_fraud = get_lookback_window(window_len, lookback_cutoff_fraud, date_col)\n", "        curr_cols = df_client_hist_attr.columns\n", "        df_client_hist_attr = df_client_hist_attr\\\n", "            .withColumn(f'num_app_{window_len}d', F.count('APP_NUMBER').over(lookback_window_fraud))\\\n", "            .withColumn(f'num_activated_acct_{window_len}d', F.sum('is_activated').over(lookback_window_fraud))\\\n", "            .drop('is_activated')\n", "        \n", "        for tag in bad_tags:\n", "            tag_alias = tag.split('_TAG')[0].lower()\n", "            df_client_hist_attr = df_client_hist_attr\\\n", "                .withColumn(f'num_client_with_{tag_alias}_{window_len}d', F.approx_count_distinct(F.when(F.col(tag) == 1, F.col('client_name'))).over(lookback_window_fraud))\\\n", "                .withColumn(f'num_client_with_{tag_alias}_{window_len}d', F.when(F.col(f'num_client_with_{tag_alias}_{window_len}d') == 0, F.lit(-3))\\\n", "                                                                           .otherwise(<PERSON>.col(f'num_client_with_{tag_alias}_{window_len}d')))\n", "        \n", "        for col in [col for col in df_client_hist_attr.columns if col not in curr_cols and col not in [f'num_app_{window_len}d', f'num_activated_acct_{window_len}d']]:\n", "            df_client_hist_attr = df_client_hist_attr\\\n", "                .withColumn(col, F.when(F.col(f'num_app_{window_len}d') == 0, F.lit(-1))\\\n", "                                  .when(F.col(f'num_activated_acct_{window_len}d') == 0, F.lit(-2))\\\n", "                                  .otherwise(<PERSON><PERSON>col(col)))\n", "\n", "        df_client_hist_attr = df_client_hist_attr.drop(f'num_app_{window_len}d', f'num_activated_acct_{window_len}d')\n", "    \n", "    new_attr = [col for col in df_client_hist_attr.columns if col not in df_base.columns]\n", "    \n", "    return df_client_hist_attr.select('APP_NUMBER', *new_attr).fillna(-2)\n", "\n", "## recency from last app with another client (done)\n", "## if rejected by another client (done)\n", "## attributes based on difference between all clients and this client\n", "\n", "\n", "def calc_other_client_attr(df_joined, window_len_list, lookback_cutoff_app=APP_REPORT_DELAY, lookback_cutoff_device=DEVICE_REPORT_DELAY, \n", "                           lookback_cutoff_fraud=FRAUD_REPORT_DELAY, date_col='app_datetime_unix'):\n", "    \n", "    window_len_list_for_app = [x for x in window_len_list if x - lookback_cutoff_app >= MIN_WINDOW_LEN]\n", "    window_len_list_for_device = [x for x in window_len_list if x - lookback_cutoff_device >= MIN_WINDOW_LEN]\n", "    window_len_list_for_fraud = [x for x in window_len_list if x - lookback_cutoff_fraud >= MIN_WINDOW_LEN]\n", "    \n", "    df_other_client_attr = df_joined\n", "    \n", "    for window_len in window_len_list_for_app:\n", "        df_other_client_attr = df_other_client_attr\\\n", "            .withColumn(f'num_app_with_other_client_{window_len}d', \n", "                            F.when(F.col(f'num_app_{window_len}d') > 0, F.col(f'num_app_{window_len}d') - F.col(f'num_app_{window_len}d_with_client'))\\\n", "                             .otherwise(F.lit(-1)))\n", "            \n", "    for window_len in window_len_list_for_device:\n", "        df_other_client_attr = df_other_client_attr\\\n", "            .withColumn(f'num_activated_acct_with_other_client_{window_len}d', \\\n", "                           F.when(F.col(f'num_activated_acct_{window_len}d') == -1, F.lit(-1))\\\n", "                            .when(F.col(f'num_activated_acct_{window_len}d_with_client') == -1, F.col(f'num_activated_acct_{window_len}d'))\\\n", "                            .otherwise(F.col(f'num_activated_acct_{window_len}d') - F.col(f'num_activated_acct_{window_len}d_with_client')))\\\n", "            .withColumn(f'num_lines_with_other_client_{window_len}d', \\\n", "                           F.when(F.col(f'num_lines_{window_len}d') <= -1, F.col(f'num_lines_{window_len}d'))\\\n", "                            .when(F.col(f'num_lines_{window_len}d_with_client') <= -1, F.col(f'num_lines_{window_len}d'))\\\n", "                            .otherwise(F.col(f'num_lines_{window_len}d') - F.col(f'num_lines_{window_len}d_with_client')))\\\n", "            .withColumn(f'total_device_financed_with_other_client_{window_len}d', \\\n", "                           F.when(F.col(f'total_device_financed_{window_len}d') <= -1, F.col(f'total_device_financed_{window_len}d'))\\\n", "                            .when(F.col(f'total_device_financed_{window_len}d_with_client') <= -1, F.col(f'total_device_financed_{window_len}d'))\\\n", "                            .otherwise(F.col(f'total_device_financed_{window_len}d') - F.col(f'total_device_financed_{window_len}d_with_client')))\n", "        \n", "    return df_other_client_attr\n", "\n", "\n", "def calc_consumer_profile_attr(df_base, window_len_list, lookback_cutoff_app=APP_REPORT_DELAY, lookback_cutoff_device=DEVICE_REPORT_DELAY, \n", "                               lookback_cutoff_fraud=FRAUD_REPORT_DELAY, date_col='app_datetime_unix', raw_cols_to_keep=None):\n", "    '''\n", "    df_base contains both pinned and unpinned records, need to handle separately\n", "    '''\n", "    ## make sure only pinned applications are selected for attribute calculation\n", "    contains_no_hit = (df_base.filter(f\"{consumer_key} is null\").count() > 0)\n", "    if contains_no_hit:\n", "        df_attr = df_base.filter(f\"{consumer_key} is not null\")\n", "    else:\n", "        df_attr = df_base\n", "    \n", "    ## window length should be at least 5 days after deducting the reporting delay\n", "    window_len_list_for_app = [x for x in window_len_list if x - lookback_cutoff_app >= MIN_WINDOW_LEN]\n", "    window_len_list_for_device = [x for x in window_len_list if x - lookback_cutoff_device >= MIN_WINDOW_LEN]\n", "    window_len_list_for_fraud = [x for x in window_len_list if x - lookback_cutoff_fraud >= MIN_WINDOW_LEN]\n", "    \n", "    ## calculate profile attributes based on past application, device and fraud\n", "    df_app_hist_attr = calc_app_hist_attr_by_client(df_attr, window_len_list_for_app, lookback_cutoff=lookback_cutoff_app, date_col=date_col)\n", "    df_device_hist_attr = calc_device_hist_attr_by_client(df_attr, window_len_list_for_device, lookback_cutoff=lookback_cutoff_device, date_col=date_col)\n", "    df_fraud_hist_attr = calc_fraud_hist_attr_by_client(df_attr, window_len_list_for_fraud, lookback_cutoff=lookback_cutoff_fraud, date_col=date_col)\n", "    df_client_hist_attr = calc_client_hist_attr(df_attr, window_len_list, lookback_cutoff_app=lookback_cutoff_app, lookback_cutoff_device=lookback_cutoff_device, \n", "                                                lookback_cutoff_fraud=lookback_cutoff_fraud, date_col=date_col)\n", "    \n", "    if raw_cols_to_keep is None:\n", "        raw_cols_to_keep = []\n", "    \n", "    df_joined = df_attr.select('APP_NUMBER', *raw_cols_to_keep)\\\n", "                       .join(df_app_hist_attr, 'APP_NUMBER')\\\n", "                       .join(df_device_hist_attr, 'APP_NUMBER')\\\n", "                       .join(df_fraud_hist_attr, 'APP_NUMBER')\\\n", "                       .join(df_client_hist_attr, 'APP_NUMBER')\n", "    \n", "#     df_profile_attr = df_attr.select('APP_NUMBER', *raw_cols_to_keep)\\\n", "#                        .join(df_app_hist_attr, 'APP_NUMBER')\\\n", "#                        .join(df_device_hist_attr, 'APP_NUMBER')\n", "    \n", "    df_profile_attr = calc_other_client_attr(df_joined, window_len_list, lookback_cutoff_app=lookback_cutoff_app, lookback_cutoff_device=lookback_cutoff_device, \n", "                                             lookback_cutoff_fraud=lookback_cutoff_fraud, date_col=date_col)\n", "    \n", "    ## fill DEFAULT VALUE for consumer pii attributes for no-hit applications if present\n", "    if contains_no_hit:\n", "        df_no_hit = df_base.filter(f\"{consumer_key} is null\").select('APP_NUMBER', *raw_cols_to_keep)\n", "        for col in [col for col in df_profile_attr.columns if col not in ['APP_NUMBER'] + raw_cols_to_keep]:\n", "            df_no_hit = df_no_hit.withColumn(col, F.lit(DEFAULT_VALUE))\n", "        return df_profile_attr.union(df_no_hit)\n", "    \n", "    return df_profile_attr"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["month = '2022-02-01'\n", "log(f'====== Generate pii lookup attributes for month {month} ======')\n", "ts = month.replace('-', '')[:6]\n", "ts_start = pd.date_range(end=month, periods=8, freq='MS').strftime('%Y-%m-%d').tolist()[0].replace('-', '')[:6]\n", "df_hist_test = df_base.filter(f\"ts >= {ts_start} and ts <= {ts}\")\n", "calc_consumer_profile_attr(df_hist_test, [183], raw_cols_to_keep=useful_fields)\\\n", "    .filter(f\"ts = {ts}\")\\\n", "    .write.mode('overwrite')\\\n", "    .parquet(f'/p/telco/attributes/consortium_attributes/consumer_profile_attr_test_2/all_clients_202202_202206/ts={ts}')\n", "log('====== Done ======')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_profile_attr_test = spark.read.parquet(f'/p/telco/attributes/consortium_attributes/consumer_profile_attr_test/all_clients_202202_202206/')\n", "\n", "# device attr only (good)\n", "df_profile_attr_test_1 = spark.read.parquet(f'/p/telco/attributes/consortium_attributes/consumer_profile_attr_test_1/all_clients_202202_202206/')\n", "\n", "# app + device attr\n", "df_profile_attr_test_2 = spark.read.parquet(f'/p/telco/attributes/consortium_attributes/consumer_profile_attr_test_2/all_clients_202202_202206/')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_profile_attr_test.printSchema()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_profile_attr_test.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_profile_attr_test_1.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(df_profile_attr_test.filter(\"num_app_183d = 0\").count())\n", "print(df_profile_attr_test.filter(\"num_activated_acct_183d = -1\").count())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(df_profile_attr_test_1.filter(\"num_app_183d = 0\").count())\n", "print(df_profile_attr_test_1.filter(\"num_activated_acct_183d = -1\").count())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(df_profile_attr_test_2.filter(\"num_app_183d = 0\").count())\n", "print(df_profile_attr_test_2.filter(\"num_activated_acct_183d = -1\").count())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(df_profile_attr_test.filter(\"num_app_183d = -9999\").count())\n", "print(df_profile_attr_test.filter(\"num_activated_acct_183d = -9999\").count())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["profile_attr_list = [col for col in df_profile_attr_test.columns if col not in ['APP_NUMBER'] + useful_fields]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_profile_attr_default = df_hist_test.filter(f\"{consumer_key} is null\").select('APP_NUMBER', *useful_fields)\n", "for col in profile_attr_list:\n", "    df_profile_attr_default = df_profile_attr_default.withColumn(col, F.lit(DEFAULT_VALUE))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_hist = spark.read.parquet(f'/p/telco/attributes/app_hist/all_carriers/ts=202206')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_hist.filter(\"opin is null\").count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["month = '202206'\n", "calc_app_hist_attr(df_hist.filter(\"opin is not null\"), [183], lookback_cutoff=APP_REPORT_DELAY)\\\n", "    .filter(f\"ts = {month}\")\\\n", "    .write.mode('overwrite')\\\n", "    .parquet(f'/p/telco/attributes/consortium_attributes/app_hist_attr_test/all_clients_202202_202206/ts={month}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_app_hist_attr = spark.read.parquet(f'/p/telco/attributes/consortium_attributes/app_hist_attr_test/all_clients_202202_202206/ts={month}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for col in ['num_sales_channel_183d', 'same_sales_channel_with_last_app_183d', 'num_id_type_183d', 'same_id_type_with_last_app_183d']:\n", "    check_distribution(df_app_hist_attr, col)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_hist_test = spark.read.parquet('/p/telco/attributes/app_hist/all_carriers/ts=202206')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["window_len = 183\n", "calc_device_hist_attr_by_client(df_hist_test.filter(\"opin is not null\"), [window_len])\\\n", "    .write.mode('overwrite')\\\n", "    .parquet(f'/p/telco/attributes/consortium_attributes/device_hist_attr_test/all_clients_202202_202206/ts=202206')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_device_attr_test = spark.read.parquet(f'/p/telco/attributes/consortium_attributes/device_hist_attr_test/all_clients_202202_202206/ts=202206')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(df_device_attr_test.filter(\"num_app_183d = 0\").count())\n", "print(df_device_attr_test.filter(\"num_activated_acct_183d = -1\").count())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for col in ['num_app_183d', 'num_activated_acct_183d']:\n", "    check_distribution(df_device_attr_test, col)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## PII-lookup attributes\n", "* Calculated from PII linkage or sharing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''\n", "Default values:\n", "    + If no valid pii: return -9999\n", "'''"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uid_fields = [f'{col}_uid' for col in ['addr', 'phone', 'email', 'ssn']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DEFAULT_VALUE = -9999\n", "\n", "def calc_pii_lookup_attr(df_base, window_len_list, lookback_cutoff=APP_REPORT_DELAY, date_col='app_datetime_unix', suffix=None):\n", "    \n", "    df_pii_lookup_attr = df_base.select('APP_NUMBER')\n", "    \n", "    for uid in uid_fields:\n", "        ## We handle uid is null separately as null uid could be a bottleneck for window aggregation\n", "        ## When uid is null, all attributes are DEFAULT_VALUE\n", "        df_uid_not_null = df_base.filter(f\"{uid} is not null\")\n", "        df_uid_null = df_base.filter(f\"{uid} is null\")\n", "        uid_alias = uid.split('_')[0]\n", "        for window_len in window_len_list:\n", "            lookback_window_uid = get_lookback_window(window_len, lookback_cutoff, date_col, partition_col=uid)\n", "            lookback_window_uid_consumer = get_lookback_window(window_len, lookback_cutoff, date_col, partition_col=[uid, consumer_key])\n", "            df_uid_not_null = df_uid_not_null\\\n", "                .withColumn('is_pinned', <PERSON><PERSON>col(consumer_key).isNotNull().cast('int'))\\\n", "                .withColumn(f'num_app_using_{uid_alias}_{window_len}d', F.count('APP_NUMBER').over(lookback_window_uid))\\\n", "                .withColumn(f'days_since_last_app_using_{uid_alias}_{window_len}d', F.round((<PERSON>.col('app_datetime_unix') - F.max('app_datetime_unix').over(lookback_window_uid)) / NUM_SECOND_IN_A_DAY, 2))\\\n", "                .withColumn(f'num_pinned', F.sum('is_pinned').over(lookback_window_uid))\\\n", "                .withColumn(f'num_identity_using_{uid_alias}_{window_len}d', \n", "                               F.when(F.col('num_pinned').isNull(), F.lit(-1))\\\n", "                                .when(F.col('num_pinned') > 0, F.approx_count_distinct(consumer_key).over(lookback_window_uid))\\\n", "                                .otherwise(F.lit(-2)))\\\n", "                .withColumn('last_identity', F.last(consumer_key).over(lookback_window_uid))\\\n", "                .withColumn(f'{uid_alias}_used_by_same_identity_in_last_app_{window_len}d', \n", "                                       F.when(F.col(f'num_app_using_{uid_alias}_{window_len}d') == 0, F.lit(-1))\\\n", "                                        .when(F.col('last_identity').isNull() & F.col(consumer_key).isNotNull(), F.lit(-2))\\\n", "                                        .when(F.col('last_identity').isNotNull() & F.col(consumer_key).isNull(), F.lit(-3))\\\n", "                                        .when(F.col('last_identity').isNull() & F.col(consumer_key).isNull(), F.lit(-4))\\\n", "                                        .otherwise((F.col(consumer_key) == F.col('last_identity')).cast('int')))\\\n", "                .withColumn(f'num_app_using_{uid_alias}_same_identity_{window_len}d', \n", "                                        F.when(F.col(f'num_app_using_{uid_alias}_{window_len}d') == 0, F.lit(-1))\\\n", "                                         .when(F.col(consumer_key).isNull(), F.lit(-2))\\\n", "                                         .otherwise(F.count('APP_NUMBER').over(lookback_window_uid_consumer)))\\\n", "                .withColumn(f'days_since_last_app_using_{uid_alias}_same_identity_{window_len}d', \n", "                                        F.when(F.col(f'num_app_using_{uid_alias}_{window_len}d') == 0, F.lit(-1))\\\n", "                                         .when(F.col(consumer_key).isNull(), F.lit(-2))\\\n", "                                         .when(F.col(f'num_app_using_{uid_alias}_same_identity_{window_len}d') == 0, F.lit(-3))\\\n", "                                         .otherwise(F.round((<PERSON>.col('app_datetime_unix') - F.max('app_datetime_unix').over(lookback_window_uid_consumer)) / NUM_SECOND_IN_A_DAY, 2)))\\\n", "                .drop('is_pinned', 'num_pinned', 'last_identity')\n", "        \n", "        new_attr = [col for col in df_uid_not_null.columns if col not in df_base.columns]\n", "        for col in new_attr:\n", "            df_uid_null = df_uid_null.withColumn(col, F.lit(DEFAULT_VALUE))\n", "        if suffix is not None:\n", "            new_attr = [f'{col} AS {col}_{suffix}' for col in new_attr]\n", "        \n", "        df_pii_lookup_attr = df_pii_lookup_attr.join(df_uid_not_null.union(df_uid_null).selectExpr('APP_NUMBER', *new_attr), 'APP_NUMBER').fillna(-1)\n", "        \n", "    return df_pii_lookup_attr\n", "\n", "\n", "def calc_consumer_pii_attr(df_base, window_len_list, lookback_cutoff=APP_REPORT_DELAY, date_col='app_datetime_unix', suffix=None):\n", "    \n", "    df_consumer_pii_attr = df_base\n", "    \n", "    for uid in uid_fields:\n", "        uid_alias = uid.split('_')[0]\n", "        for window_len in window_len_list:\n", "            lookback_window = get_lookback_window(window_len, lookback_cutoff, date_col, partition_col=consumer_key)\n", "            curr_cols = df_consumer_pii_attr.columns\n", "            df_consumer_pii_attr = df_consumer_pii_attr\\\n", "                .withColumn('has_uid', <PERSON><PERSON>col(uid).isNotNull().cast('int'))\\\n", "                .withColumn('num_app_with_uid', F.sum('has_uid').over(lookback_window))\\\n", "                .withColumn(f'num_{uid_alias}_{window_len}d', \n", "                               F.when(F.col('num_app_with_uid').isNull(), F.lit(-1))\\\n", "                                .when(F.col('num_app_with_uid') > 0, F.approx_count_distinct(uid).over(lookback_window))\\\n", "                                .otherwise(F.lit(-2)))\\\n", "                .withColumn('last_uid', F.last(uid).over(lookback_window))\\\n", "                .withColumn(f'same_{uid_alias}_as_last_app_{window_len}d', \n", "                                F.when(F.col(f'num_app_with_uid').isNull(), F.lit(-1))\\\n", "                                 .when(F.col('last_uid').isNull() & F.col(uid).isNotNull(), F.lit(-2))\\\n", "                                 .when(F.col('last_uid').isNotNull() & F.col(uid).isNull(), F.lit(-3))\\\n", "                                 .when(F.col('last_uid').isNull() & F.col(uid).isNull(), F.lit(-4))\\\n", "                                 .otherwise((F.col(uid) == F.col('last_uid')).cast('int')))\\\n", "                .drop('has_uid', 'num_app_with_uid', 'last_uid')\n", "            \n", "    new_attr = [col for col in df_consumer_pii_attr.columns if col not in df_base.columns]\n", "    if suffix is not None:\n", "        new_attr = [f'{col} AS {col}_{suffix}' for col in new_attr]\n", "    \n", "    return df_consumer_pii_attr.selectExpr('APP_NUMBER', *new_attr).fillna(-1)\n", "\n", "\n", "def calc_pii_consortium_attr(df_base, window_len_list, lookback_cutoff=APP_REPORT_DELAY, date_col='app_datetime_unix', \n", "                             raw_cols_to_keep=None, suffix=None):\n", "        \n", "    ## pii lookup attributes accepts all applications regardless of hit or no-hit\n", "    df_pii_lookup_attr = calc_pii_lookup_attr(df_base, window_len_list, lookback_cutoff=lookback_cutoff, date_col=date_col, suffix=suffix)\n", "    \n", "    ## consumer pii attributes only accept pinned applications\n", "    contains_no_hit = (df_base.filter(f\"{consumer_key} is null\").count() > 0)\n", "    if contains_no_hit:\n", "        df_attr = df_base.filter(f\"{consumer_key} is not null\")\n", "    else:\n", "        df_attr = df_base\n", "    df_consumer_pii_attr = calc_consumer_pii_attr(df_attr, window_len_list, lookback_cutoff=lookback_cutoff, date_col=date_col, suffix=suffix)\n", "    \n", "    ## fill DEFAULT VALUE for consumer pii attributes for no-hit applications\n", "    if contains_no_hit:\n", "        df_no_hit = df_base.filter(f\"{consumer_key} is null\").select('APP_NUMBER')\n", "        for col in [col for col in df_consumer_pii_attr.columns if col != 'APP_NUMBER']:\n", "            df_no_hit = df_no_hit.withColumn(col, F.lit(DEFAULT_VALUE))\n", "        df_consumer_pii_attr = df_consumer_pii_attr.union(df_no_hit.select(df_consumer_pii_attr.columns))\n", "        \n", "    if raw_cols_to_keep is None:\n", "        raw_cols_to_keep = []\n", "    \n", "    return df_base.select('APP_NUMBER', *raw_cols_to_keep)\\\n", "                  .join(df_pii_lookup_attr, 'APP_NUMBER')\\\n", "                  .join(df_consumer_pii_attr, 'APP_NUMBER')\n", "\n", "\n", "def calc_pii_consortium_attr_by_client(df_base, window_len_list, lookback_cutoff=APP_REPORT_DELAY, date_col='app_datetime_unix', raw_cols_to_keep=None):\n", "    \n", "    ## combining applications from all clients\n", "    print(f'------ Calculating pii consortium attributes combining all clients ------')\n", "    df_pii_consortium_attr_all_client = \\\n", "        calc_pii_consortium_attr(\n", "            df_base,\n", "            window_len_list, \n", "            lookback_cutoff=lookback_cutoff, \n", "            date_col=date_col)\n", "    \n", "    ## for each client separately\n", "    client_list = sorted([row['client_name'] for row in df_base.select('client_name').distinct().collect()])\n", "    df_attr_list = []\n", "    for client in client_list:\n", "        print(f'------ Calculating pii consortium attributes for {client} ------')\n", "        df_pii_consortium_attr_this_client = \\\n", "            calc_pii_consortium_attr(\n", "                df_base.filter(f\"client_name = '{client}'\"),\n", "                window_len_list, \n", "                lookback_cutoff=lookback_cutoff, \n", "                date_col=date_col, \n", "                suffix='with_client')\n", "        df_attr_list.append(df_pii_consortium_attr_this_client)\n", "        \n", "    if raw_cols_to_keep is None:\n", "        raw_cols_to_keep = []\n", "    \n", "    return df_base.select('APP_NUMBER', *raw_cols_to_keep)\\\n", "                  .join(df_pii_consortium_attr_all_client, 'APP_NUMBER')\\\n", "                  .join(reduce(DataFrame.unionAll, df_attr_list), 'APP_NUMBER')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_pii_lookup_attr_test = calc_pii_consortium_attr_by_client(df_hist_test, windows, raw_cols_to_keep=useful_fields)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_pii_lookup_attr_test.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_pii_lookup_attr_test.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(df_hist_test.groupBy('email_uid').agg(F.countDistinct('opin').alias('num_identity')), 'num_identity', sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_hist_test.groupBy('email_uid').agg(F.countDistinct('opin').alias('num_identity')).filter(\"num_identity > 100\").show(truncate=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_tmp = df_hist_test.groupBy('phone_uid').agg(F.countDistinct('opin').alias('num_identity'))\n", "check_distribution(df_tmp, 'num_identity', sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_pii_lookup_attr(df_hist, windows, lookback_cutoff=APP_REPORT_DELAY, date_col='app_datetime_unix').columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["month = '2022-02-01'\n", "log(f'====== Generate pii lookup attributes for month {month} ======')\n", "ts = month.replace('-', '')[:6]\n", "ts_start = pd.date_range(end=month, periods=8, freq='MS').strftime('%Y-%m-%d').tolist()[0].replace('-', '')[:6]\n", "df_hist = df_base.filter(f\"ts >= {ts_start} and ts <= {ts}\")\n", "calc_pii_consortium_attr_by_client(df_hist, windows, raw_cols_to_keep=useful_fields)\\\n", "    .filter(f\"ts = {ts}\")\\\n", "    .write.mode('overwrite')\\\n", "    .parquet(f'/p/telco/attributes/consortium_attributes/pii_lookup_attr_test/all_clients_202202_202206_with_client/ts={ts}')\n", "log(f'====== Done ======')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_pii_attr_prev = spark.read.parquet(f'/p/telco/attributes/consortium_attributes/pii_lookup_attr_test/all_clients_202202_202206/')\n", "df_pii_attr = spark.read.parquet(f'/p/telco/attributes/consortium_attributes/pii_lookup_attr_test/all_clients_202202_202206_with_client/')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_pii_attr_prev.count(), df_pii_attr.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_base.filter(\"ts = 202202\").count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bin_cut_dict = {\n", "    'num_app_using_addr_183d': [0.0, 0.1, 1.0, 2.0, 3.0, 305.0],\n", "    'num_identity_using_phone_183d': [1.0, 2.0, 3.0, 4.0, 9.0, 18.0, 88.0]\n", "}\n", "\n", "bin_cut_dict_with_client = {f'{k}_with_client': bin_cut_dict[k] for k in bin_cut_dict}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'num_identity_using_phone_183d'\n", "df_stats = calc_stats_for_attr_by_bins(df_pii_attr_prev.filter(\"is_activated = 1\"), attr, bin_cut_dict[attr], special_values=[-1, -2, DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'num_app_using_addr_183d'\n", "df_stats = calc_stats_for_attr_by_bins(df_pii_attr.filter(\"is_activated = 1\"), attr, bin_cut_dict[attr], special_values=[-1, -2, DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'num_app_using_addr_183d_with_client'\n", "df_stats = calc_stats_for_attr_by_bins(df_pii_attr.filter(\"is_activated = 1\"), attr, bin_cut_dict_with_client[attr], special_values=[-1, -2, DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["days_since_pii_attr = [col for col in df_pii_attr.columns if col.startswith('days_since')]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["days_since_pii_attr"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_pii_attr.select('APP_NUMBER', *days_since_pii_attr, *useful_fields)\\\n", "    .sample(0.1).toPandas().to_parquet(os.path.join(DATA_PATH, 'attribute/pii_lookup_attr.recency_based.all_carriers.sample.pq'), engine='pyarrow')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## generate consortium attributes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## DO NOT use app history as it was anchored to pins, hence not suitable for PII-lookup attributes\n", "# for month in pd.date_range(start='2022-03-01', end='2022-06-01', freq='MS').strftime('%Y-%m-%d').tolist():\n", "for month in ['2022-02-01']:\n", "    log(f'====== Generate consortium attributes for month {month} ======')\n", "    ts = month.replace('-', '')[:6]\n", "    ts_start = pd.date_range(end=month, periods=8, freq='MS').strftime('%Y-%m-%d').tolist()[0].replace('-', '')[:6]\n", "#     df_hist = spark.read.parquet(f'/p/telco/attributes/app_hist/all_carriers/ts={ts}')\n", "    df_hist = df_base.filter(f\"ts >= {ts_start} and ts <= {ts}\")\n", "    df_profile_attr = calc_consumer_profile_attr(df_hist, windows, raw_cols_to_keep=useful_fields)\n", "    df_pii_lookup_attr = calc_pii_consortium_attr_by_client(df_hist, windows, raw_cols_to_keep=[])\n", "    df_profile_attr.join(df_pii_lookup_attr, 'APP_NUMBER')\\\n", "        .filter(f\"ts = {ts}\")\\\n", "        .write.mode('overwrite')\\\n", "        .parquet(f'/p/telco/attributes/consortium_attributes/consortium_attributes/all_clients_202202_202206_test/ts={ts}')\n", "log(f'====== Done ======')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["month = '2022-06-01'\n", "ts = month.replace('-', '')[:6]\n", "ts_start = pd.date_range(end=month, periods=8, freq='MS').strftime('%Y-%m-%d').tolist()[0].replace('-', '')[:6]\n", "df_hist = df_base.filter(f\"ts >= {ts_start} and ts <= {ts}\")\n", "df_profile_attr = calc_consumer_profile_attr(df_hist, windows, contains_no_hit=True, raw_cols_to_keep=useful_fields)\n", "df_pii_lookup_attr = calc_pii_consortium_attr(df_hist, windows, contains_no_hit=True, raw_cols_to_keep=useful_fields)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_hist.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(df_profile_attr.count(), df_pii_lookup_attr.count())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_union.filter(\"ts = 202206\").count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_base.filter(\"ts = 202206\").count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(df_profile_attr.filter(\"ts = 202206\").count(), df_pii_lookup_attr.filter(\"ts = 202206\").count())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_consortium_attr_test = spark.read.parquet(f'/p/telco/attributes/consortium_attributes/consortium_attributes/all_clients_202202_202206_test/')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(df_consortium_attr_test.filter(\"num_app_183d = 0\").count())\n", "print(df_consortium_attr_test.filter(\"num_activated_acct_183d is null\").count())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(df_consortium_attr.filter(\"ts = 202202\").count())\n", "print(df_consortium_attr_test.count())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## (2023/11/14) added _with_client version of PII lookup attributes and more fraud related attributes\n", "df_consortium_attr = spark.read.parquet('/p/telco/attributes/consortium_attributes/consortium_attributes/all_clients_202202_202206/')\n", "df_consortium_attr_prev = spark.read.parquet('/p/telco/attributes/consortium_attributes/consortium_attributes/all_clients_202202_202206_20231024/')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(df_consortium_attr.columns), len(df_consortium_attr_prev.columns)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_consortium_attr.count(), df_consortium_attr_prev.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["set(df_consortium_attr.columns) - set(df_consortium_attr_prev.columns)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["set(df_consortium_attr_prev.columns) - set(df_consortium_attr.columns)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_union.filter(\"ts >= 202202 and ts <= 202206\").count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_consortium_attr.select(F.countDistinct('APP_NUMBER'), <PERSON><PERSON>countDistinct('eid'), <PERSON><PERSON>countDistinct('opin'), <PERSON><PERSON>countDistinct('APP_NUMBER', 'eid'), F.count(F.lit(1))).show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(df_consortium_attr, 'ts')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(df_consortium_attr, 'is_activated', add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(df_consortium_attr, 'is_activated', add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for col in ['num_sales_channel_183d', 'same_sales_channel_with_last_app_183d', 'num_id_type_183d', 'same_id_type_with_last_app_183d']:\n", "    check_distribution(df_consortium_attr, col)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## generate sample for univariate plot\n", "df_consortium_attr.sample(0.05)\\\n", "    .join(df_union.select('APP_NUMBER', 'NO_USAGE'), 'APP_NUMBER')\\\n", "    .to<PERSON><PERSON><PERSON>()\\\n", "    .to_parquet(os.path.join(DATA_PATH, 'attribute/consumer_profile_attr.all_carriers.sample.20231114.pq'), engine='pyarrow')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DATA_PATH"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["10498177 / 20"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### check attribute calculation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(df_consortium_attr.filter(\"is_activated = 1\"), 'BAD_TAG', add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'num_identity_using_phone_183d'\n", "bin_cuts = [1.0, 2.0, 3.0, 4.0, 9.0, 18.0, 88.0]\n", "df_stats = calc_stats_for_attr_by_bins(df_consortium_attr, attr, bin_cuts, special_values=[-1, -2, -3, DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'num_identity_using_phone_183d_with_client'\n", "bin_cuts = [1.0, 2.0, 3.0, 4.0, 9.0, 18.0, 88.0]\n", "df_stats = calc_stats_for_attr_by_bins(df_consortium_attr, attr, bin_cuts, special_values=[-1, -2, -3, DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'num_identity_using_phone_183d'\n", "bin_cuts = [1.0, 2.0, 3.0, 4.0, 9.0, 18.0, 88.0]\n", "df_stats = calc_stats_for_attr_by_bins(df_consortium_attr_prev, attr, bin_cuts, special_values=[-1, -2, -3, DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'num_identity_using_addr_183d'\n", "bin_cuts = [1.0, 2.0, 3.0, 4.0, 9.0, 18.0, 88.0]\n", "df_stats = calc_stats_for_attr_by_bins(df_consortium_attr, attr, bin_cuts, special_values=[-1, -2, -3, DEFAULT_VALUE])\n", "df_stats.show()\n", "\n", "df_stats = calc_stats_for_attr_by_bins(df_consortium_attr_prev, attr, bin_cuts, special_values=[-1, -2, -3, DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'num_identity_using_email_183d'\n", "bin_cuts = [1.0, 2.0, 3.0, 4.0, 9.0, 18.0, 88.0]\n", "df_stats = calc_stats_for_attr_by_bins(df_consortium_attr, attr, bin_cuts, special_values=[-1, -2, -3, DEFAULT_VALUE])\n", "df_stats.show()\n", "\n", "df_stats = calc_stats_for_attr_by_bins(df_consortium_attr_prev, attr, bin_cuts, special_values=[-1, -2, -3, DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'num_phone_183d'\n", "bin_cuts = [0, 1.0, 2.0, 3.0, 4.0, 9.0, 18.0, 88.0]\n", "df_stats = calc_stats_for_attr_by_bins(df_consortium_attr, attr, bin_cuts, special_values=[-1, -2, -3, DEFAULT_VALUE])\n", "df_stats.show()\n", "\n", "df_stats = calc_stats_for_attr_by_bins(df_consortium_attr_prev, attr, bin_cuts, special_values=[-1, -2, -3, DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'num_app_183d'\n", "bin_cuts = [0, 1.0, 2.0, 3.0, 4.0, 9.0, 18.0, 88.0]\n", "df_stats = calc_stats_for_attr_by_bins(df_consortium_attr, attr, bin_cuts, special_values=[-1, -2, -3, DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'num_sales_channel_183d'\n", "bin_cuts = [0, 1.0, 2.0, 3.0, 4.0, 9.0, 18.0, 88.0]\n", "df_stats = calc_stats_for_attr_by_bins(df_consortium_attr, attr, bin_cuts, special_values=[-1, -2, -3, DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'num_activated_acct_183d'\n", "bin_cuts = [0, 1.0, 2.0, 3.0, 4.0, 9.0, 18.0, 88.0]\n", "df_stats = calc_stats_for_attr_by_bins(df_consortium_attr, attr, bin_cuts, special_values=[-1, -2, -3, DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'max_fpd_loss_amt_183d'\n", "bin_cuts = [0, 100, 200, 500, 700, 1000, 2000]\n", "df_stats = calc_stats_for_attr_by_bins(df_consortium_attr, attr, bin_cuts, special_values=[-1, -2, -3, DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'days_since_last_fpd_app_183d'\n", "bin_cuts = [88, 100, 120, 150]\n", "df_stats = calc_stats_for_attr_by_bins(df_consortium_attr, attr, bin_cuts, special_values=[-1, -2, -3, DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'num_fpf_acct_183d'\n", "bin_cuts = [0, 1.0, 2.0]\n", "df_stats = calc_stats_for_attr_by_bins(df_consortium_attr, attr, bin_cuts, special_values=[-1, -2, -3, DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_stats(df_consortium_attr)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(df_consortium_attr.filter(\"num_activated_acct_183d = -1\"), 'num_app_183d')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_consortium_attr.filter(\"num_activated_acct_183d = -1 and num_app_183d > 0 and opin is null\").count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(df_consortium_attr.filter(\"num_fpf_acct_183d = -1\"), 'num_app_183d')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(df_consortium_attr.filter(\"num_fpf_acct_183d = -1\"), 'num_activated_acct_183d')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["check_distribution(df_consortium_attr.filter(\"num_fpf_acct_183d = -2\"), 'is_activated')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_consortium_attr.filter(\"total_bad_loss_amt_183d < -3 and total_bad_loss_amt_183d != -9999\").count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr_to_check = ['days_since_last_app_183d', 'days_since_first_app_183d']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bin_cut_dict = {\n", "    'days_since_last_app_183d': [0.04, 0.09, 0.65, 1.17, 3.38, 8.1, 21.98, 50.824, 88.86, 131.12],\n", "    'days_since_first_app_183d': [0.04, 0.23, 1.18, 4.04, 10.818, 26.95, 56.694, 90.432, 123.48, 155.616]\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'days_since_last_app_183d'\n", "df_stats = calc_stats_for_attr_by_bins(df_consortium_attr.filter(\"is_activated = 1\"), attr, bin_cut_dict[attr], special_values=[-1, DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'days_since_last_app_183d'\n", "df_stats = calc_stats_for_attr_by_bins(df_consortium_attr.filter(\"is_activated = 1\"), attr, bin_cut_dict[attr], bad_tag='FRAUD_TAG', special_values=[-1, DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'days_since_last_app_183d'\n", "df_stats = calc_stats_for_attr_by_bins(df_consortium_attr.filter(\"is_activated = 1\"), attr, bin_cut_dict[attr], bad_tag='FPD_TAG', special_values=[-1, DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_consortium_attr.filter(\"is_activated = 1 and days_since_last_app_183d > 100 and BAD_TAG = 1\").select('APP_NUMBER', 'opin', 'days_since_last_app_183d').take(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_hist.filter(\"opin = '58499908'\")\\\n", "    .select('APP_NUMBER', 'client_name', 'app_date', 'app_datetime_unix', 'is_activated', 'FRAUD_TAG', 'FPD_TAG', 'BAD_TAG')\\\n", "    .sort('app_datetime_unix').drop('app_datetime_unix').show(truncate=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["month = '2022-03-01'\n", "df_app_hist = spark.read.parquet(f'/p/telco/attributes/app_hist/app_hist_uc_vz/month={month}')\n", "calc_pii_consortium_attr(df_app_hist, windows)\\\n", "    .filter(f\"trunc(app_date, 'month') = '{month}'\")\\\n", "    .write.mode('overwrite')\\\n", "    .parquet(f'/p/telco/attributes/consortium_attributes/pii_consortium_attr_uc_vz/month={month}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_pii_consortium_attr = spark.read.parquet(f'/p/telco/attributes/consortium_attributes/pii_consortium_attr_uc_vz/month=2022-03-01')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## no-hit applications are not included here as they are not included in df_app_hist due to missing eid\n", "df_pii_consortium_attr.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_base.filter(f\"trunc(app_date, 'month') = '2022-03-01' and eid is not null\").count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_pii_consortium_attr.printSchema()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr_to_check = ['num_app_using_addr_183d', 'num_app_using_ssn_183d', 'num_identity_using_phone_183d', 'num_identity_using_email_183d']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for col in attr_to_check:\n", "    check_distribution(df_pii_consortium_attr, col, sort_by_count=True, add_pct=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bin_cut_dict = {\n", "    'num_app_using_addr_183d': [0, 1, 2, 3, 4, 5, 10, 15, 20, 30],\n", "    'num_app_using_ssn_183d': [0, 1, 2],\n", "    'num_identity_using_phone_183d': [0, 1, 2, 3, 4, 5, 10, 15, 20, 30],\n", "    'num_identity_using_email_183d': [0, 1, 2, 3, 4, 5, 10, 15, 20, 30],\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'num_app_using_addr_183d'\n", "df_stats = calc_stats_for_attr_by_bins(df_pii_consortium_attr, attr, bin_cut_dict[attr], special_values=[DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'num_app_using_ssn_183d'\n", "df_stats = calc_stats_for_attr_by_bins(df_pii_consortium_attr, attr, bin_cut_dict[attr], special_values=[DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'num_identity_using_phone_183d'\n", "df_stats = calc_stats_for_attr_by_bins(df_pii_consortium_attr, attr, bin_cut_dict[attr], special_values=[DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'num_identity_using_email_183d'\n", "df_stats = calc_stats_for_attr_by_bins(df_pii_consortium_attr, attr, bin_cut_dict[attr], special_values=[DEFAULT_VALUE])\n", "df_stats.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### test"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_base.groupBy('addr_uid')\\\n", "       .agg(F.count('APP_NUMBER').alias('num_app'),\n", "            F.countDistinct('client_name').alias('num_client'))\\\n", "       .filter(\"num_app > 15 and num_client > 1\").take(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_g = df_base.groupBy('addr_uid').agg(F.count('APP_NUMBER').alias('num_app'))\n", "df_g.select(F.max('num_app')).show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["window_len = 183\n", "lookback_window = get_lookback_window(window_len, 1, 'app_datetime_unix', partition_col='addr_uid')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["partition_cols = ['addr_uid', 'eid']\n", "lookback_window_new = W.partitionBy(*partition_cols)\\\n", "                       .orderBy(<PERSON>.col('app_datetime_unix'))\\\n", "                       .rangeBetween(-days(183), -days(1))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_example = df_base.filter(\"eid = 'de40fdb744f40f489d7ce123f127fec088dc9e14'\")\\\n", "                    .union(df_base.filter(\"addr_uid = ';911;;PARK;;CIR;;53590;3148'\"))\\\n", "                    .dropDuplicates()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["curr_cols = df_example.columns\n", "df_example_attr = df_example.select('APP_NUMBER', 'client_name', 'app_local_time', 'app_datetime_unix', 'eid', 'addr_uid')\\\n", "       .withColumn('num_app', F.when(F.col('addr_uid').isNotNull(), F.count('APP_NUMBER').over(lookback_window)).otherwise(F.lit(DEFAULT_VALUE)))\\\n", "       .withColumn('num_eid', F.when(F.col('addr_uid').isNotNull(), F.approx_count_distinct('eid').over(lookback_window)).otherwise(F.lit(DEFAULT_VALUE)))\\\n", "       .withColumn('last_eid', F.last('eid').over(lookback_window))\\\n", "       .withColumn('same_eid', (F.col('eid') == F.col('last_eid')).cast('int'))\\\n", "       .withColumn('num_app_same_cons', F.count('APP_NUMBER').over(lookback_window_new))\n", "\n", "new_attr = [col for col in df_example_attr.columns if col not in curr_cols]\n", "for col in new_attr:\n", "    df_example_attr = df_example_attr.withColumn(col, F.when(F.col('addr_uid').isNotNull(), F.col(col)).otherwise(F.lit(-9999)))\n", "\n", "df_example_attr.drop('APP_NUMBER', 'addr_uid')\\\n", "       .fillna(-1)\\\n", "       .sort('app_local_time').show(50, truncate=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_base.withColumn('no_addr', <PERSON><PERSON>col('addr_uid').isNull().cast('int'))\\\n", "       .groupBy('eid')\\\n", "       .agg(F.count('APP_NUMBER').alias('num_app'),\n", "            F.countDistinct('client_name').alias('num_client'),\n", "            F.countDistinct('addr_uid').alias('num_addr'),\n", "            F.sum('no_addr').alias('num_no_addr'))\\\n", "       .filter(\"num_app > 15 and num_client > 1 and num_addr > 5 and num_no_addr > 3\").take(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_example_1 = df_base.filter(\"eid = 'b910ecca0422970b9562414f782055d0ff20c6ca'\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lookback_window_eid = get_lookback_window(183, 1, 'app_datetime_unix', partition_col='eid')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_example_attr_1 = df_example_1.select('APP_NUMBER', 'client_name', 'app_local_time', 'app_datetime_unix', 'sales_channel', 'eid', 'addr_uid')\\\n", "       .withColumn('num_app', F.count('APP_NUMBER').over(lookback_window_eid))\\\n", "       .withColumn('num_addr', F.approx_count_distinct('addr_uid').over(lookback_window_eid))\\\n", "       .withColumn('last_addr', F.last('addr_uid').over(lookback_window_eid))\\\n", "       .withColumn('same_addr', (F.col('addr_uid') == F.col('last_addr')).cast('int'))\n", "\n", "for col in ['same_addr']:\n", "    df_example_attr_1 = df_example_attr_1.withColumn(col, F.when(F.col('addr_uid').isNotNull(), F.col(col)).otherwise(F.lit(-9999)))\n", "\n", "df_example_attr_1.drop('APP_NUMBER', 'eid')\\\n", "       .fillna(-1)\\\n", "       .sort('app_local_time').show(50, truncate=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_example_2 = df_base.filter(\"eid = '8d03bef102fc4321d667f72000ef41db6e2ee9ca'\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_example_attr_2 = df_example_2.select('APP_NUMBER', 'client_name', 'app_local_time', 'app_datetime_unix', 'sales_channel', 'eid', 'addr_uid')\\\n", "       .withColumn('num_app', F.count('APP_NUMBER').over(lookback_window_eid))\\\n", "       .withColumn('num_addr', F.approx_count_distinct('addr_uid').over(lookback_window_eid))\\\n", "       .withColumn('last_addr', F.last('addr_uid').over(lookback_window_eid))\\\n", "       .withColumn('same_addr', (F.col('addr_uid') == F.col('last_addr')).cast('int'))\n", "\n", "for col in ['same_addr']:\n", "    df_example_attr_2 = df_example_attr_2.withColumn(col, F.when(F.col('addr_uid').isNotNull(), F.col(col)).otherwise(F.lit(-9999)))\n", "\n", "df_example_attr_2.drop('APP_NUMBER', 'eid')\\\n", "       .fillna(-1)\\\n", "       .sort('app_local_time').show(50, truncate=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = [(0,'<PERSON>',None), (1,'<PERSON>',123), (2,'<PERSON>',234), (3,'<PERSON>',None), (4,'<PERSON>',567), (5,'<PERSON>',None), (6,'<PERSON>',None), (7,'<PERSON>',789)]\n", "df = spark.createDataFrame(data, ['app', 'pii', 'pin'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["w0 = W.partition<PERSON>y('pii')\n", "ww = W.partition<PERSON>y('pii').orderBy(<PERSON><PERSON>col('app')).rowsBetween(<PERSON><PERSON>unboundedPreceding, -1)\n", "wx = W.partition<PERSON>y('pii', 'pin').orderBy(<PERSON><PERSON>col('app')).rowsBetween(<PERSON><PERSON>unboundedPreceding, -1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.withColumn('num_pin', F.approx_count_distinct('pin').over(w0))\\\n", "  .withColumn('is_same', (F.col('pin') == F.last('pin').over(ww)).cast('int'))\\\n", "  .sort('app').show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## num_identity = 0 contains two cases: 1. PII never used before, 2. used only by no-hit consumers\n", "df.withColumn('is_pinned', <PERSON><PERSON>col('pin').isNotNull().cast('int'))\\\n", "  .withColumn(f'num_app', F.count('app').over(ww))\\\n", "  .withColumn(f'num_pinned', F.sum('is_pinned').over(ww))\\\n", "  .withColumn(f'num_identity', F.when(F.col('num_pinned').isNull(), F.lit(None))\\\n", "                                .when(F.col('num_pinned') > 0, F.approx_count_distinct('pin').over(ww))\\\n", "                                .otherwise(F.lit(-2)))\\\n", "  .withColumn('last_identity', F.last('pin').over(ww))\\\n", "  .withColumn('same_as_last_identity', F.when(F.col('num_app') == 0, F.lit(-1))\\\n", "                                        .when(F.col('last_identity').isNull() & F.col('pin').isNotNull(), F.lit(-2))\\\n", "                                        .when(F.col('last_identity').isNotNull() & <PERSON>.col('pin').isNull(), F.lit(-3))\\\n", "                                        .when(F.col('last_identity').isNull() & F.col('pin').isNull(), F.lit(-4))\\\n", "                                        .otherwise((F.col('pin') == F.col('last_identity')).cast('int')))\\\n", "  .withColumn('num_app_same_identity', F.when(F.col('pin').isNotNull(), F.count('app').over(wx))\\\n", "                                        .otherwise(F.lit(-2)))\\\n", "  .show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.16"}}, "nbformat": 4, "nbformat_minor": 4}