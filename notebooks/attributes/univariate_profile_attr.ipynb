{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import os, findspark, socket\n", "\n", "spark_24 = '/work/app/spark'\n", "spark_23 = '/usr/hdp/current/spark2-client'\n", "\n", "hostname = socket.gethostname()\n", "if hostname.startswith('alnfrddap'):\n", "    spark_home='/opt/cloudera/parcels/SPARK2/lib/spark2'\n", "elif hostname.startswith('usmkdlph'):\n", "#     #spark 2.3\n", "    spark_home=spark_23\n", "\n", "#     spark 2.4\n", "#     spark_home=spark_24\n", "else:\n", "    pass\n", "    #print(f'No spark installed on server: {hostname}!')\n", "\n", "os.environ['SPARK_HOME']=spark_home\n", "os.environ['ARROW_PRE_0_15_IPC_FORMAT'] = '1'\n", "findspark.init(spark_home)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "import time\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import json\n", "import yaml\n", "from functools import reduce\n", "from itertools import chain\n", "from operator import add\n", "from sklearn.metrics import accuracy_score, precision_recall_fscore_support, roc_curve, roc_auc_score"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyspark.sql import Row\n", "from pyspark.sql import SparkSession\n", "from operator import add\n", "from pyspark.sql import functions as F\n", "from pyspark.sql import Window as W\n", "from pyspark.sql import types as T"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import gbm\n", "from train_model.gbm_model_attribute import *\n", "from train_model.gbm_model_utils import *\n", "from retro_analysis.utils import log, check_distribution, plot_score\n", "from train_model.univariate_plot_new import *\n", "from telco_utils import calc_bad_rate, filter_good_bad"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option('max_colwidth', 400)\n", "pd.set_option('display.max_rows', 500)\n", "pd.set_option('display.max_columns', 500)\n", "pd.set_option('display.width', 1000)\n", "pd.set_option('display.float_format', lambda x: '%.9f' % x)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DATA_PATH = '/work/users/c18882a/projects/telco/data'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calc_bad_rate(df, tag, print_stats=True):\n", "    count = df.shape[0]\n", "    bad_count = df.loc[df[tag] == 1].shape[0]\n", "    bad_rate = bad_count / count\n", "    if print_stats:\n", "        print(count, bad_count, bad_rate)\n", "    return bad_rate\n", "\n", "def read_lines_from_text_file(file_name):\n", "    with open(file_name, 'r') as f:\n", "        lines = [x.strip('\\n') for x in f.readlines()]\n", "    return lines\n", "\n", "def write_lines_to_text_file(L, file_name):\n", "    with open(file_name, 'w') as f:\n", "        f.write('\\n'.join(L))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load attribute dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_attr = pd.read_parquet(os.path.join(DATA_PATH, 'attribute/consumer_profile_attr.tm_uc.202203_202204.pq'), engine='pyarrow')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_attr.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_attr.dtypes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_attr.isna().sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_attr['ACTIVATIONFLAG'].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_attr.query(\"ACTIVATIONFLAG == '1'\")['FRAUD_TAG'].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["21417 / 210850"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Prepare constraint file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["non_attr_cols = ['APP_NUMBER',\n", " 'client_name',\n", " 'opin',\n", " 'eid',\n", " 'app_date',\n", " 'month',\n", " 'ACTIVATIONFLAG',\n", " 'FRAUD_TAG',\n", " 'FRAUD_WO_AMT']\n", "\n", "attr_list = [col for col in df_attr.columns if col not in non_attr_cols]\n", "fraud_based_attr = [col for col in df_attr.columns if any(x in col for x in ['fraud'])]\n", "device_based_attr = [col for col in df_attr.columns if any(x in col for x in ['acct', 'device', 'lines', 'financed']) and col not in fraud_based_attr]\n", "app_based_attr = [col for col in df_attr.columns if col in attr_list and col not in device_based_attr + fraud_based_attr]\n", "print(len(attr_list), len(app_based_attr), len(device_based_attr), len(fraud_based_attr))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["min_max_dict = {}\n", "for col in df_attr.columns:\n", "    if col in non_attr_cols:\n", "        continue\n", "    if any(col.startswith(x) for x in ['pct_', 'opened_acct_', 'same_', 'was_']):\n", "        min_val = 0\n", "        max_val = 1\n", "    elif col.startswith('fraud_type'):\n", "        min_val = 100\n", "        max_val = np.inf\n", "    else:\n", "        min_val = 0\n", "        max_val = np.inf\n", "    min_max_dict[col] = [min_val, max_val, 'consumer_profile']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(min_max_dict)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["min_max_dict"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## print constraint file\n", "for col in df_attr.columns:\n", "    if col in non_attr_cols:\n", "        continue\n", "    if col in acct_based_attr:\n", "        group = 'consumer_profile_account_based'\n", "    elif col in device_based_attr:\n", "        group = 'consumer_profile_device_based'\n", "    elif col in fraud_based_attr:\n", "        group = 'consumer_profile_fraud_based'\n", "    else:\n", "        group = 'consumer_profile_app_based'\n", "    print(','.join([str(x) for x in [col, group, min_max_dict[col][0], min_max_dict[col][1], 0]]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generate univariate plot"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fraud_rate(df):\n", "    return df.loc[df.FRAUD_TAG == 1].shape[0] / df.shape[0]\n", "\n", "def fpf_rate(df):\n", "    return df.loc[df.FRAUD_TYPE == '1'].shape[0] / df.shape[0]\n", "\n", "def tpf_rate(df):\n", "    return df.loc[df.FRAUD_TYPE == '2'].shape[0] / df.shape[0]\n", "\n", "def fpd_rate(df):\n", "    return df.loc[df.FRAUD_TYPE == '3'].shape[0] / df.shape[0]\n", "\n", "def wo_others_rate(df):\n", "    return df.loc[df.FRAUD_TYPE == '4'].shape[0] / df.shape[0]\n", "\n", "bad_rate_funcs = {\n", "    'FRAUD': fraud_rate,\n", "    'FPF_TAG': fpf_rate,\n", "    'TPF_TAG': tpf_rate,\n", "    'FPF_TPF_TAG': fraud_rate,\n", "    'FPD_TAG': fpd_rate,\n", "    'WO_OTHERS_TAG': wo_others_rate\n", "}\n", "\n", "fraud_tag_and_type_map = {\n", "    'FPF_TAG': ['1'],\n", "    'TPF_TAG': ['2'],\n", "    'FPF_TPF_TAG': ['1','2'],\n", "    'FPD_TAG': ['3'],\n", "    'NON_PAY_TAG': ['4'],\n", "}\n", "\n", "def filter_good_bad(df, tag):\n", "    # only include this tag as bad, and the pure good (FRAUD_TYPE == 0), e.g., if use FPF, other type of FRAUDs will be excluded \n", "    return df[(df['FRAUD_TYPE'].isin(fraud_tag_and_type_map[tag])) | (df['FRAUD_TYPE'] == '0')].copy()\n", "\n", "def add_fraud_tag(df, tag):\n", "    df[tag] = df['FRAUD_TYPE'].map(lambda x: 1 if x in fraud_tag_and_type_map[tag] else 0)\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_uni = df_attr.query(\"ACTIVATIONFLAG == '1'\").fillna({'FRAUD_TAG':0, 'FRAUD_WO_AMT':0}).copy()\n", "df_uni.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_uni['same_client_with_last_app_183d'].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_uni['num_app_183d'].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bin_cuts_dict, attr_with_one_bin_for_each_value = generate_attr_bins(df_uni, attr_list, min_max_dict, num_bins=10, value_count_threshold=5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np.save('../data/bin_cut/bin_cuts_dict.tmobile_uc_2yr.consumer_profile_attr.npy', bin_cuts_dict, allow_pickle=True)\n", "write_lines_to_text_file(attr_with_one_bin_for_each_value, \\\n", "                         '../data/bin_cut/attr_with_one_bin_for_each_value.tmobile_uc_2yr.consumer_profile_attr.txt')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bin_cuts_dict = np.load('../data/bin_cut/bin_cuts_dict.tmobile_uc_2yr.consumer_profile_attr.npy', allow_pickle=True).item()\n", "attr_with_one_bin_for_each_value = read_lines_from_text_file('../data/bin_cut/attr_with_one_bin_for_each_value.tmobile_uc_2yr.consumer_profile_attr.txt')\n", "print(len(bin_cuts_dict), len(attr_with_one_bin_for_each_value))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bin_cuts_dict['days_since_last_app_183d']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["folder = f'tm_uc_consumer_profile_attr_2yr_fraud_tag'\n", "df_plot = df_uni\n", "univariate_tag = 'FRAUD_TAG'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["univariate_plot_for_continuous_attr(df_plot, 'num_app_183d', univariate_tag, bin_cuts_dict, fraud_rate, min_max_dict=min_max_dict, \n", "                                    special_value_dict=None,  \n", "                                    log_scale=False, one_bin_for_each_value=False, ax=None, attr_desc=None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attr = 'num_app_183d'\n", "df_valid = df_plot.query(f\"{attr} >= 0\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["calc_bad_rate(df_valid.query(f\"{attr} >= 3\"), univariate_tag)\n", "calc_bad_rate(df_valid.query(f\"{attr} == 2\"), univariate_tag)\n", "calc_bad_rate(df_valid.query(f\"{attr} == 1\"), univariate_tag)\n", "calc_bad_rate(df_valid.query(f\"{attr} == 0\"), univariate_tag)\n", "calc_bad_rate(df_valid, univariate_tag)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["66 / 19755, 138 / (201606 - 19755)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fpr, tpr, _ = roc_curve(df_valid[univariate_tag], df_valid[attr])\n", "tpr_fpr = np.stack([tpr, fpr])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tpr_fpr"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## application-based profile attributes (based on all clients' applications)\n", "app_based_attr_1 = [col for col in app_based_attr if '_with_client' not in col]\n", "generate_univariate_plot(df_plot, app_based_attr_1, univariate_tag, bin_cuts_dict, min_max_dict, \n", "                         fraud_rate, special_value_dict=None, attr_desc=None, \n", "                         attr_with_one_bin_for_each_value=attr_with_one_bin_for_each_value, \n", "                         log_scale=False, num_fig_in_row=2, save_fig=True, \n", "                         save_name=f'../data/univariate_plot/{folder}/app_based_attr_all_client')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## application-based profile attributes (based on this client's applications)\n", "app_based_attr_2 = [col for col in app_based_attr if '_with_client' in col]\n", "generate_univariate_plot(df_plot, app_based_attr_2, univariate_tag, bin_cuts_dict, min_max_dict, \n", "                         fraud_rate, special_value_dict=None, attr_desc=None, \n", "                         attr_with_one_bin_for_each_value=attr_with_one_bin_for_each_value, \n", "                         log_scale=False, num_fig_in_row=2, save_fig=True, \n", "                         save_name=f'../data/univariate_plot/{folder}/app_based_attr_with_client')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## device-based profile attributes (based on all clients' applications)\n", "device_based_attr_1 = [col for col in device_based_attr if '_with_client' not in col]\n", "generate_univariate_plot(df_plot, device_based_attr_1, univariate_tag, bin_cuts_dict, min_max_dict, \n", "                         fraud_rate, special_value_dict=None, attr_desc=None, \n", "                         attr_with_one_bin_for_each_value=attr_with_one_bin_for_each_value, \n", "                         log_scale=False, num_fig_in_row=2, save_fig=True, \n", "                         save_name=f'../data/univariate_plot/{folder}/device_based_attr_all_client')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_plot.query(\"num_lines_183d == -2\")['num_device_financed_183d'].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_plot.query(\"num_device_financed_183d == -1\")['num_lines_183d'].value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_plot.query(\"num_activated_acct_183d == 0\").shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## device-based profile attributes (based on this client's applications)\n", "device_based_attr_2 = [col for col in device_based_attr if '_with_client' in col]\n", "generate_univariate_plot(df_plot, device_based_attr_2, univariate_tag, bin_cuts_dict, min_max_dict, \n", "                         fraud_rate, special_value_dict=None, attr_desc=None, \n", "                         attr_with_one_bin_for_each_value=attr_with_one_bin_for_each_value, \n", "                         log_scale=False, num_fig_in_row=2, save_fig=True, \n", "                         save_name=f'../data/univariate_plot/{folder}/device_based_attr_with_client')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## fraud-based profile attributes\n", "generate_univariate_plot(df_plot, fraud_based_attr, univariate_tag, bin_cuts_dict, min_max_dict, \n", "                         fraud_rate, special_value_dict=None, attr_desc=None, \n", "                         attr_with_one_bin_for_each_value=attr_with_one_bin_for_each_value, \n", "                         log_scale=False, num_fig_in_row=2, save_fig=True, \n", "                         save_name=f'../data/univariate_plot/{folder}/fraud_based_attr')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_plot.query(\"num_client_with_fraud_183d == 0\").shape, df_plot.query(\"num_activated_acct_183d == 0\").shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.16"}}, "nbformat": 4, "nbformat_minor": 4}